interface FilterFields {
  [key: string]: string | { $in: any[] };
}

interface SortParam {
  sortBy: string;
  sort: string | number;
}

interface PaginationParam {
  page?: string | number;
  perPage?: string | number;
}

export const createSearchFilterSortPagination = (
  search: string,
  searchFields: string[] | null,
  filterFields: FilterFields,
  sortParam: SortParam,
  paginationParam: PaginationParam,
) => {
  // Search
  const searchObj: Record<string, any> = {};
  if (search && searchFields.length) {
    const regex = new RegExp(search.trim(), 'i');
    searchObj.$or = searchFields.map((field) => ({
      [field]: regex,
    }));
  }

  // Filter
  const filterObj: Record<string, any> = {};
  if (filterFields) {
    for (const field in filterFields) {
      const value = filterFields[field];
      if (typeof value === 'string' && value) {
        if (value === 'false') {
          filterObj[field] = false;
        } else if (value === 'true') {
          filterObj[field] = true;
        } else {
          filterObj[field] = value;
        }
      } else if (typeof value === 'object' && value.$in?.length) {
        filterObj[field] = value;
      }
    }
  }

  // Sort
  const sortObj: { [key: string]: any } = {};
  if (sortParam?.sortBy && sortParam?.sort) {
    const sortField = sortParam.sortBy;
    sortObj[sortField] = Number(sortParam.sort);
  } else {
    sortObj['createdAt'] = -1;
  }

  // Pagination
  let page = 1;
  let limitData = 10;
  let skipData = 0;

  if (paginationParam) {
    page = Number(paginationParam.page || 1);
    limitData = Number(paginationParam.perPage || 10);
    skipData = (page - 1) * limitData;
  }
  return { searchObj, filterObj, sortObj, skipData, limitData };
};
