// Test file for Global Search API
// This is a simple test to verify the API structure

const testSearchAPI = {
  // Test the API endpoint structure
  endpoint: 'GET /user/search?q={search_string}',
  
  // Test request body structure
  requestBody: {
    tab: 'for_you|accounts|posts|podcasts|jobs|events|union_organizations',
    page: 1,
    perPage: 10
  },
  
  // Test response structure
  expectedResponse: {
    status: true,
    message: 'Search results fetched successfully',
    data: {
      accounts: [],
      posts: [],
      podcasts: [],
      jobs: [],
      events: [],
      union_organizations: []
    },
    pagination: {
      totalResults: 0,
      currentResults: 0,
      totalPages: 1,
      currentPage: 1
    }
  },
  
  // Test search tabs
  searchTabs: [
    'for_you',
    'accounts', 
    'posts',
    'podcasts',
    'jobs',
    'events',
    'union_organizations'
  ],
  
  // Test search functionality
  testSearch: function(searchTerm, tab = 'for_you') {
    console.log(`Testing search for "${searchTerm}" in tab "${tab}"`);
    return {
      query: `q=${encodeURIComponent(searchTerm)}`,
      body: {
        tab: tab,
        page: 1,
        perPage: 10
      }
    };
  }
};

// Example usage
console.log('Global Search API Test');
console.log('=====================');
console.log('Endpoint:', testSearchAPI.endpoint);
console.log('Available tabs:', testSearchAPI.searchTabs.join(', '));

// Test examples
console.log('\nTest Examples:');
console.log('1. Search for "actor":', testSearchAPI.testSearch('actor', 'accounts'));
console.log('2. Search for "music":', testSearchAPI.testSearch('music', 'for_you'));
console.log('3. Search for "job":', testSearchAPI.testSearch('job', 'jobs'));

console.log('\nAPI is ready for testing!');

// Example curl commands for testing
console.log('\nExample cURL Commands:');
console.log('======================');

// Example 1: Search for "actor" in accounts
console.log('\n1. Search for "actor" in accounts:');
console.log('curl -X GET "http://localhost:3000/user/search?q=actor" \\');
console.log('  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\');
console.log('  -H "Content-Type: application/json" \\');
console.log('  -d \'{"tab": "accounts", "page": 1, "perPage": 10}\'');

// Example 2: Search for "music" in all categories
console.log('\n2. Search for "music" in all categories:');
console.log('curl -X GET "http://localhost:3000/user/search?q=music" \\');
console.log('  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\');
console.log('  -H "Content-Type: application/json" \\');
console.log('  -d \'{"tab": "for_you"}\'');

// Example 3: Search for "job" in jobs
console.log('\n3. Search for "job" in jobs:');
console.log('curl -X GET "http://localhost:3000/user/search?q=job" \\');
console.log('  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\');
console.log('  -H "Content-Type: application/json" \\');
console.log('  -d \'{"tab": "jobs", "page": 1, "perPage": 5}\'');

console.log('\nNote: Replace YOUR_JWT_TOKEN with your actual JWT token');
console.log('Make sure the search query parameter "q" is not empty!');

// Job search with application status
console.log('\n4. Search for "developer" in jobs (shows application status):');
console.log('curl -X GET "http://localhost:3000/user/search?q=developer&tab=jobs" \\');
console.log('  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\');
console.log('  -H "Content-Type: application/json"');

console.log('\nJob search now includes:');
console.log('- isApplied: true/false (whether you applied)');
console.log('- applicationCount: number (total applications)');
console.log('- user: job poster details'); 