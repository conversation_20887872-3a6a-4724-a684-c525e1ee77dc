import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { licenseCertificationDocument } from 'src/Models/licenseCertification.schema';

@Injectable()
export class LicenseCertificationRepository extends AbstractRepository<licenseCertificationDocument> {
  constructor(
    @InjectModel('LicenseCertification')
    LicenseCertificationModel: Model<licenseCertificationDocument>,
  ) {
    super(LicenseCertificationModel);
  }

  async findLicenseCertifications(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const results: any = await this.model
      .find(filter)
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified userProfileAboutYou.skills',
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();

    return results.map((licenseCertification) => {
      if (licenseCertification.userId?.userProfileAboutYou?.skills) {
        licenseCertification = {
          ...licenseCertification.toObject(),
          skills: licenseCertification.userId.userProfileAboutYou.skills,
        };
      }

      delete licenseCertification.userId?.userProfileAboutYou;
      return licenseCertification;
    });
  }
}
