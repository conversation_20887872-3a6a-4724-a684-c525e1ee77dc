import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type userSignupDataDocument = UserSignupData & Document;

export enum SelectionType {
  ALL = 'all',
  ONLY = 'only',
  NONE = 'none',
}

@Schema({ timestamps: true, versionKey: false })
export class UserSignupData {
  @Prop({ type: String })
  title: string;

  @Prop({ type: [String] })
  parentSlug: string[];

  @Prop({ type: String })
  itemText: string;

  @Prop({ type: String })
  slug: string;

  @Prop({ type: String, enum: SelectionType })
  selectionType: SelectionType;
}

export const UserSignupDataSchema =
  SchemaFactory.createForClass(UserSignupData);
