# Pepli Node Database ER Diagram

## Overview
This document provides a comprehensive Entity-Relationship (ER) diagram for the Pepli Node application, showing all database collections and their relationships.

## Core Entities and Relationships

### 1. User (Central Entity)
**Primary Collection: `users`**
- **Primary Key**: `_id` (ObjectId)
- **Unique Fields**: `userName`, `email`
- **Key Relationships**:
  - One-to-Many: User → Posts
  - One-to-Many: User → Comments
  - One-to-Many: User → Stories
  - One-to-Many: User → Events
  - One-to-Many: User → JobPosts
  - One-to-Many: User → Education
  - One-to-Many: User → Experience
  - One-to-Many: User → Projects
  - One-to-Many: User → Featured
  - One-to-Many: User → Feedback
  - One-to-Many: User → Groups (as creator)

### 2. Social Networking Relationships

#### ConnectionInfo (User Connections)
**Collection: `connectioninfos`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User (requester)
  - `connectionWithId` → User (target)
- **Status**: `pending`, `accept`, `reject`, `remove`

#### FollowerInfo (User Followers)
**Collection: `followerinfos`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `followerId` → User (follower)
  - `followingId` → User (being followed)
- **Status**: `pending`, `accept`, `reject`

#### People (User Relationships)
**Collection: `peoples`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `parentUserId` → User (parent)
  - `childUserId` → User (child)
- **Status**: `pending`, `accept`, `reject`

#### ClientInfo (Client Relationships)
**Collection: `clientsinfos`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User (service provider)
  - `clientId` → User (client)
- **Status**: `pending`, `accept`, `reject`

### 3. Content Management

#### Post (Main Content)
**Collection: `posts`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User (author)
  - `repostBy` → User (reposter)
  - `post` → Post (original post for reposts)
  - `group` → Group (if posted in group)
- **Arrays of References**:
  - `taggedPeople` → User[]
  - `fundraisers` → User[]
  - `collaborators` → User[]
  - `reactions` → User[] (with reaction types)

#### Comment (Post Comments)
**Collection: `comments`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `postId` → Post
  - `userId` → User (commenter)
  - `parentId` → Comment (for nested comments)

#### Like (Post Reactions)
**Collection: `likes`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `postId` → Post
  - `userId` → User

#### Bookmark (Saved Posts)
**Collection: `bookmarks`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `postId` → Post
  - `userId` → User

#### Story (Temporary Content)
**Collection: `stories`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User
- **Arrays of References**:
  - `likeUsers` → User[]

### 4. Group Management

#### Group (Communities)
**Collection: `groups`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `createdBy` → User (group creator)
- **Arrays of References**:
  - `groupRule` → GroupRule[]

#### GroupMember (Group Membership)
**Collection: `groupmembers`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `group` → Group
  - `member` → User

#### GroupRule (Group Rules)
**Collection: `grouprules`**
- **Primary Key**: `_id` (ObjectId)
- **No direct foreign keys** (referenced by Group)

### 5. Professional Profile

#### Education (User Education)
**Collection: `educations`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User
  - `school` → User (school/organization)

#### Experience (Work Experience)
**Collection: `experiences`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User

#### Project (User Projects)
**Collection: `projects`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User
- **Arrays of References**:
  - `affiliateOrganizations` → User[]
  - `collaborators` → User[]

#### Featured (Featured Content)
**Collection: `featureds`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User

### 6. Job & Career

#### JobPost (Job Listings)
**Collection: `jobposts`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User (job poster)
- **Arrays of References**:
  - `applications` → JobApplication[]

#### JobApplication (Job Applications)
**Collection: `jobapplications`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `jobId` → JobPost
  - `userId` → User (applicant)

#### JobList (Job Categories)
**Collection: `joblists`**
- **Primary Key**: `_id` (ObjectId)
- **No direct foreign keys** (reference data)

### 7. Events & Activities

#### Event (Events)
**Collection: `events`**
- **Primary Key**: `_id` (ObjectId)
- **Arrays of References**:
  - `collaborators` → User[]
  - `participants` → User[]

### 8. Notifications & Communication

#### Notification (User Notifications)
**Collection: `notifications`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `sender` → User
  - `receiver` → User
  - `group` → Group
  - `postId` → Post
  - `storyId` → Story
  - `connectionId` → ConnectionInfo
  - `clientId` → ClientInfo
  - `memberId` → People

### 9. System & Support

#### Feedback (User Feedback)
**Collection: `feedbacks`**
- **Primary Key**: `_id` (ObjectId)
- **Foreign Keys**:
  - `userId` → User

#### ContactUs (Contact Form)
**Collection: `contactuses`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### OtpLog (OTP Management)
**Collection: `otplogs`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### UserSignupData (Signup Options)
**Collection: `usersignupdatas`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (reference data)

#### Record (Reference Data)
**Collection: `records`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (reference data)

### 10. Additional Schemas

#### ProfileViews (Profile Analytics)
**Collection: `profileviews`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (analytics data)

#### MissingInformation (User Data)
**Collection: `missinginformations`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### Subscriber (Newsletter)
**Collection: `subscribers`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### BetaLeads (Beta Program)
**Collection: `beta_leads`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### SelfIdentify (User Identity)
**Collection: `selfidentifies`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### VolunteerExperience (Volunteer Work)
**Collection: `volunteer-experiences`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### LicenseCertification (Certifications)
**Collection: `licensecertifications`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

#### HonorsAndAwards (Achievements)
**Collection: `honorsandawards`**
- **Primary Key**: `_id` (ObjectId)
- **No foreign keys** (standalone)

## Relationship Summary

### One-to-Many Relationships
1. **User → Posts**: One user can create many posts
2. **User → Comments**: One user can make many comments
3. **User → Stories**: One user can create many stories
4. **User → Events**: One user can create many events
5. **User → JobPosts**: One user can post many jobs
6. **User → Education**: One user can have many education records
7. **User → Experience**: One user can have many work experiences
8. **User → Projects**: One user can have many projects
9. **User → Featured**: One user can have many featured items
10. **User → Feedback**: One user can submit many feedback
11. **User → Groups**: One user can create many groups
12. **Post → Comments**: One post can have many comments
13. **Comment → Comments**: One comment can have many replies (nested)
14. **Group → GroupMembers**: One group can have many members
15. **JobPost → JobApplications**: One job can have many applications

### Many-to-Many Relationships
1. **Users ↔ Users** (via ConnectionInfo): Users can connect with other users
2. **Users ↔ Users** (via FollowerInfo): Users can follow other users
3. **Users ↔ Users** (via People): Users can have relationships with other users
4. **Users ↔ Users** (via ClientInfo): Users can have client relationships
5. **Users ↔ Posts** (via Like): Users can like many posts, posts can be liked by many users
6. **Users ↔ Posts** (via Bookmark): Users can bookmark many posts, posts can be bookmarked by many users
7. **Users ↔ Groups** (via GroupMember): Users can join many groups, groups can have many users
8. **Users ↔ Events** (via participants/collaborators): Users can participate in many events, events can have many participants
9. **Users ↔ Projects** (via collaborators): Users can collaborate on many projects, projects can have many collaborators

### Self-Referencing Relationships
1. **Post → Post**: Posts can repost other posts
2. **Comment → Comment**: Comments can have nested replies

## Key Features of the Schema Design

1. **Modular Design**: Each major feature has its own collection
2. **Flexible Relationships**: Uses ObjectId references for relationships
3. **Status Tracking**: Many relationships include status fields for state management
4. **Analytics Support**: Includes counters and analytics fields
5. **Media Support**: Most content types support media attachments
6. **Privacy Controls**: Posts and groups have privacy settings
7. **Notification System**: Comprehensive notification tracking
8. **Professional Features**: Job posting, applications, and career management
9. **Social Features**: Following, connections, groups, and content sharing
10. **Reference Data**: Separate collections for lookup data

This schema design supports a comprehensive social networking platform with professional features, content management, and community building capabilities. 