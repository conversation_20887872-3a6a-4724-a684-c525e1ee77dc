import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import { projectDto } from 'src/Modules/user/dtos/project.dto';
import CONSTANT from '../../../common/constant/common.constant';
import mongoose from 'mongoose';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { ProjectRepository } from 'src/Repositories/project.repository';
import { UserRepository } from 'src/Repositories/user.repository';

@Injectable()
export class ProjectService {
  constructor(
    private readonly projectRepository: ProjectRepository,
    private readonly userRepository: UserRepository,
  ) {}

  public async addProject(req, projectData: projectDto) {
    try {
      const { user: loggedInUser } = req;
      const {
        _id: id,
        affiliateOrganizations,
        collaborators,
        ...restProjectData
      } = projectData;

      let project;

      // Convert collaborator IDs to ObjectIds
      const collaboratorObjectIds = collaborators?.map(
        (collaboratorId) => new mongoose.Types.ObjectId(collaboratorId),
      );

      const affiliateOrganizationsIds = affiliateOrganizations?.map(
        (id) => new mongoose.Types.ObjectId(id),
      );

      if (id) {
        const projectExist = await this.projectRepository.findById(id);
        if (!projectExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Project'),
            HttpStatus.BAD_REQUEST,
          );
        }

        project = await this.projectRepository.updateProject(id, {
          ...restProjectData,
          startDate: new Date(restProjectData.startDate),
          endDate: new Date(restProjectData.endDate),
          collaborators: collaboratorObjectIds,
          affiliateOrganizations: affiliateOrganizationsIds,
          userId: loggedInUser._id,
        });

        if (
          Array.isArray(projectData.skills) &&
          projectData.skills.length > 0
        ) {
          const updatedSkills = await this.userRepository.updateSkills(
            loggedInUser._id,
            projectData.skills,
          );
          projectData.skills = Array.isArray(updatedSkills)
            ? updatedSkills
            : [];
        }

        return successResponse(
          project,
          CONSTANT.UPDATED_SUCCESSFULLY('Project'),
          HttpStatus.OK,
        );
      }

      if (Array.isArray(projectData.skills) && projectData.skills.length > 0) {
        const updatedSkills = await this.userRepository.updateSkills(
          loggedInUser._id,
          projectData.skills,
        );
        projectData.skills = Array.isArray(updatedSkills) ? updatedSkills : [];
      }

      const data = {
        ...restProjectData,
        collaborators: collaboratorObjectIds,
        affiliateOrganizations: affiliateOrganizationsIds,
        userId: loggedInUser._id,
      };

      project = await this.projectRepository.addProject(data);

      return successResponse(
        project,
        CONSTANT.ADDED_SUCCESSFULLY('Project'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getProjects(userId: string, req: any) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;
      const searchFields = ['name'];

      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const projects = await this.projectRepository.findWithCollaborators(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );
      const totalResults = await this.projectRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults,
        currentResults: projects?.length,
        totalPages: Math.ceil(totalResults / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        projects,
        CONSTANT.FETCHED_SUCCESSFULLY('Projects'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async deleteProject(id: string) {
    try {
      const projectExist = await this.projectRepository.findById(id);
      if (!projectExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Project'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.projectRepository.deleteById(id);

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Project'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
