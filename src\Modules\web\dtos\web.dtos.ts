import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';
import { DeviceType } from 'src/common/constant/enum';

export class BetaLeadDto {
  @IsString()
  type: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsString()
  @IsEmail()
  email: string;

  @IsString()
  phone: string;

  @IsString()
  @IsOptional()
  profession: string;

  @IsString()
  @IsOptional()
  industry: string;

  @IsString()
  @IsOptional()
  organizationName: string;

  @IsString()
  @IsOptional()
  location: string;

  @IsBoolean()
  isMessagingEnable: boolean;

  @IsString()
  @IsEnum(DeviceType)
  deviceType: string;
}

export class ContactUsDto {
  @IsString()
  name: string;

  @IsString()
  @IsEmail()
  email: string;

  @IsString()
  phone: string;

  @IsString()
  message: string;
}
