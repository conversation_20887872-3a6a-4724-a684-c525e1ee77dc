// Test file for Global Search API with Profile Visibility Enhancement
// This demonstrates how the search now respects user privacy settings

const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = 'your_jwt_token_here'; // Replace with actual token

console.log('=== Global Search API - Profile Visibility Enhancement Test ===\n');

// Test Scenarios for Profile Visibility

// Scenario 1: User with "No One" Discovery Setting
console.log('1. Testing "No One" Discovery Setting:');
console.log('Setup: User sets profile discovery to "No One"');
console.log('Expected: User should NOT appear in search results\n');

console.log('cURL Command:');
console.log(`curl -X GET "${BASE_URL}/user/search?q=john" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "accounts",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Scenario 2: User with "My Connections" Discovery Setting
console.log('2. Testing "My Connections" Discovery Setting:');
console.log('Setup: User sets profile discovery to "My Connections"');
console.log('Expected: User should only appear if searcher is a connection\n');

console.log('cURL Command:');
console.log(`curl -X GET "${BASE_URL}/user/search?q=jane" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "accounts",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Scenario 3: User with "Everyone" Discovery Setting
console.log('3. Testing "Everyone" Discovery Setting:');
console.log('Setup: User sets profile discovery to "Everyone"');
console.log('Expected: User should appear in search results for anyone\n');

console.log('cURL Command:');
console.log(`curl -X GET "${BASE_URL}/user/search?q=alice" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "accounts",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Scenario 4: Private Profile Character Setting
console.log('4. Testing "Private Profile Character" Setting:');
console.log('Setup: User enables "Private profile character"');
console.log('Expected: Limited profile information shown in search results\n');

console.log('cURL Command:');
console.log(`curl -X GET "${BASE_URL}/user/search?q=bob" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "accounts",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Test All Search Types with Profile Visibility

console.log('=== Testing All Search Types with Profile Visibility ===\n');

// Posts Search
console.log('Posts Search (respects post creator privacy):');
console.log(`curl -X GET "${BASE_URL}/user/search?q=music" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "posts",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Jobs Search
console.log('Jobs Search (respects job poster privacy):');
console.log(`curl -X GET "${BASE_URL}/user/search?q=developer" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "jobs",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Events Search
console.log('Events Search (respects event organizer privacy):');
console.log(`curl -X GET "${BASE_URL}/user/search?q=concert" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "events",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Tags Search
console.log('Tags Search (respects post creator privacy):');
console.log(`curl -X GET "${BASE_URL}/user/search?q=%23music" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "tab": "tags",
    "page": 1,
    "perPage": 10
  }'`);
console.log('\n');

// Expected Response Examples

console.log('=== Expected Response Examples ===\n');

// Example 1: User with "No One" setting
console.log('Example 1: User with "No One" Discovery Setting');
console.log('Response: User will NOT appear in search results');
console.log('Result: Empty array or reduced result count\n');

// Example 2: User with "Private Profile Character"
console.log('Example 2: User with "Private Profile Character"');
console.log('Response: Limited profile information');
console.log(JSON.stringify({
  status: true,
  message: "Accounts fetched successfully",
  data: [
    {
      _id: "user_id_here",
      firstName: "John",
      lastName: "Doe",
      userName: "johndoe",
      profileImage: "https://example.com/profile.jpg",
      // Note: headline, position, industry, skills are hidden
    }
  ],
  pagination: {
    totalResults: 1,
    currentResults: 1,
    totalPages: 1,
    currentPage: 1
  }
}, null, 2));
console.log('\n');

// Example 3: Normal user (public profile)
console.log('Example 3: Normal user (public profile)');
console.log('Response: Full profile information');
console.log(JSON.stringify({
  status: true,
  message: "Accounts fetched successfully",
  data: [
    {
      _id: "user_id_here",
      firstName: "Jane",
      lastName: "Smith",
      userName: "janesmith",
      profileImage: "https://example.com/profile.jpg",
      businessOrganizationName: "Tech Corp",
      headline: "Senior Software Engineer",
      position: "Lead Developer",
      industry: "Technology",
      followers: 150,
      following: 75,
      accountVerified: true
    }
  ],
  pagination: {
    totalResults: 1,
    currentResults: 1,
    totalPages: 1,
    currentPage: 1
  }
}, null, 2));
console.log('\n');

// Profile Visibility Settings API

console.log('=== Profile Visibility Settings API ===\n');

// Get current profile visibility settings
console.log('Get Profile Visibility Settings:');
console.log(`curl -X GET "${BASE_URL}/api/v1/profile-visibility" \\
  -H "Authorization: Bearer ${JWT_TOKEN}"`);
console.log('\n');

// Update profile visibility settings
console.log('Update Profile Visibility Settings:');
console.log(`curl -X POST "${BASE_URL}/api/v1/profile-visibility/save" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '{
    "subMenuDetails": [
      {
        "settingName": "Profile discovery with contact",
        "settings": [
          {
            "Name": "Everyone",
            "isEnabled": false
          },
          {
            "Name": "My Connections",
            "isEnabled": false
          },
          {
            "Name": "People I Follow",
            "isEnabled": false
          },
          {
            "Name": "No One",
            "isEnabled": true
          }
        ]
      }
    ]
  }'`);
console.log('\n');

// Benefits of Profile Visibility Enhancement

console.log('=== Benefits of Profile Visibility Enhancement ===\n');
console.log('✅ Privacy Compliance: Respects user privacy preferences');
console.log('✅ Consistent Experience: Search aligns with profile visibility');
console.log('✅ User Control: Full control over discoverability');
console.log('✅ Platform Trust: Builds trust by respecting choices');
console.log('✅ Data Protection: Ensures sensitive data is not exposed');
console.log('✅ Security: Prevents unauthorized access to private information');
console.log('\n');

// Testing Checklist

console.log('=== Testing Checklist ===\n');
console.log('□ Test with user having "No One" discovery setting');
console.log('□ Test with user having "My Connections" discovery setting');
console.log('□ Test with user having "Everyone" discovery setting');
console.log('□ Test with user having "Private profile character" enabled');
console.log('□ Test search results across all tabs (accounts, posts, jobs, events, tags)');
console.log('□ Test "For You" tab with different privacy settings');
console.log('□ Test pagination with filtered results');
console.log('□ Test performance with large result sets');
console.log('□ Test error handling when user settings are missing');
console.log('□ Test backward compatibility with existing search functionality');
console.log('\n');

console.log('=== Summary ===');
console.log('The global search API now respects profile visibility settings:');
console.log('- Users with "No One" discovery setting are excluded from search results');
console.log('- Users with "My Connections" setting only appear to their connections');
console.log('- Users with "Private profile character" show limited information');
console.log('- All search types (accounts, posts, jobs, events, tags) apply visibility filtering');
console.log('- The enhancement maintains backward compatibility and performance');
