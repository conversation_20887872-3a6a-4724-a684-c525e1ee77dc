import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import twilio from 'twilio';
import CONSTANT, { DUMMY_NUMBERS } from '../constant/common.constant';

interface MessageData {
  phone: string;
  message: string;
}

@Injectable()
export class TwillioService {
  constructor(private readonly configService: ConfigService) {}

  // send twillio message
  public async sendMessageTwilio(messageData: MessageData) {
    try {
      const ACCOUNT_SID = this.configService.get<string>('ACCOUNT_SID');
      const AUTH_TOKEN = this.configService.get<string>('AUTH_TOKEN');
      const client = twilio(ACCOUNT_SID, AUTH_TOKEN);
      const twilioServiceId: any = this.configService.get<string>(
        'MESSAGING_SERVICE_ID',
      );

      if (DUMMY_NUMBERS.includes(messageData.phone)) {
        messageData.message = 'Test Message';
      } else {
        // send OTP using twilio
        const response = await client.messages.create({
          body: messageData.message,
          from: twilioServiceId,
          to: `${messageData.phone}`,
        });
        if (!response) {
          throw new HttpException(
            CONSTANT.ERROR('Sending Message'),
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
