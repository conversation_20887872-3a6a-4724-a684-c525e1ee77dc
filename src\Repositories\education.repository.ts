import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { educationDocument } from 'src/Models/education.schema';

@Injectable()
export class EducationRepository extends AbstractRepository<educationDocument> {
  constructor(
    @InjectModel('Education') educationModel: Model<educationDocument>,
  ) {
    super(educationModel);
  }

  async findEducations(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const results: any = await this.model
      .find(filter)
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified userProfileAboutYou.skills',
      })
      .populate({
        path: 'school',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();

    return results.map((education) => {
      if (education.userId?.userProfileAboutYou?.skills) {
        education = {
          ...education.toObject(),
          skills: education.userId.userProfileAboutYou.skills,
        };
      }

      delete education.userId?.userProfileAboutYou;
      return education;
    });
  }
}
