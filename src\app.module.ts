import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { DatabaseModule } from './Database/database.module';
// import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import mongoose from 'mongoose';
import { AppService } from './app.service';
import { CommonModule } from './common/common.module';
import { AdminModule } from './Modules/admin/admin.module';
import { AuthModule } from './Modules/auth/auth.module';
import { GroupModule } from './Modules/group/group.module';
import { PostModule } from './Modules/post/post.module';
import { UserModule } from './Modules/user/user.module';
import { WebModule } from './Modules/web/web.module';

@Module({
  imports: [
    ScheduleModule.forRoot(), //To activate job scheduling

    ConfigModule.forRoot({ isGlobal: true /* load: [Configuration]  */ }),
    DatabaseModule,
    AuthModule,
    UserModule,
    PostModule,
    // HelperModule,
    CommonModule,
    AdminModule,
    WebModule,
    GroupModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {
    if (process.env.NODE_ENV === 'development') {
      // mongoose.set('debug', true);
      mongoose.set('strictQuery', true);
    }
  }
}
