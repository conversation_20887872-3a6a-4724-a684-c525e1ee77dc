import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { storyDocument } from 'src/Models/story.schema';
import { AwsService } from './aws.service';

@Injectable()
export class CronjobService {
  constructor(
    @InjectModel('Story') private readonly storyModel: Model<storyDocument>,
    private readonly awsService: AwsService,
  ) {}

  @Cron('* * 1 * * *') //1 hr
  async deleteExpiredStories() {
    const twentyFourHoursAgo: any = new Date(Date.now() - 48 * 60 * 60 * 1000);

    const expiredStories: any = await this.storyModel.find({
      createdAt: { $lt: twentyFourHoursAgo },
    });

    if (expiredStories.length > 0) {
      await Promise.all(
        expiredStories.map(async (story: any) => {
          let mediaType, folderName;

          if (story.thumbUrl !== null) {
            await this.awsService.s3Delete(
              story.thumbUrl,
              (folderName = `Pepli/users/${story.userId._id}/stories/thumbnail/`),
              (mediaType = 'thumbnail'),
            );
          }
          await this.awsService.s3Delete(
            story.url,
            (folderName = `Pepli/users/${story.userId._id}/stories/`),
            (mediaType = 'STORY'),
          );
        }),
      );

      // Find and delete stories older than 24 hours
      await this.storyModel.deleteMany({
        createdAt: { $lt: twentyFourHoursAgo },
      });
    }
    console.log('--- Run Cron job for delete expired stories ---');
  }
}
