import { IsString, IsNotEmpty, IsOptional, IsEnum } from 'class-validator';

export class CreateMainCategoryDto {
  @IsString()
  @IsNotEmpty()
  itemText: string;

  @IsString()
  @IsNotEmpty()
  slug: string;

  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdateMainCategoryDto {
  @IsString()
  @IsOptional()
  itemText?: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsString()
  @IsOptional()
  description?: string;
}

export class CreateSubcategoryDto {
  @IsString()
  @IsNotEmpty()
  itemText: string;

  @IsString()
  @IsNotEmpty()
  slug: string;

  @IsString()
  @IsNotEmpty()
  parentSlug: string;

  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdateSubcategoryDto {
  @IsString()
  @IsOptional()
  itemText?: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsString()
  @IsOptional()
  parentSlug?: string;

  @IsString()
  @IsOptional()
  description?: string;
}

export class DeleteCategoryDto {
  @IsString()
  @IsNotEmpty()
  slug: string;

  @IsEnum(['main', 'sub'])
  @IsNotEmpty()
  type: 'main' | 'sub';
}
