import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  ArrayNotEmpty,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  isString,
  IsString,
  Matches,
  MaxLength,
  Min<PERSON>ength,
  ValidateNested,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { WhoAreYou } from 'src/common/constant/enum';
import {
  IsNullOrString,
  IsOptionalBoolean,
  IsOptionalString,
} from 'src/common/dto/common.dto';
import { IsStringValidation } from 'src/Custom/helpers/dto.helper';
import { StatusEnum } from 'src/Models/connectionInfo.schema';
import { IsJobForEnum } from 'src/Models/jobList.schema';
import {
  collaborationEnum,
  GenderEnum,
  iAmMemberEnum,
} from 'src/Models/user.schema';

export enum hashtagSearchData {
  POST = 'post',
  USER = 'user',
  PLACE = 'place',
}

@ValidatorConstraint({ name: 'Match', async: false })
export class MatchConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    const relatedValue = (args.object as any)[relatedPropertyName];
    return value === relatedValue;
  }

  defaultMessage(args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    return `${relatedPropertyName} and ${args.property} must be same`;
  }
}

export enum sendVerify {
  EMAIL = 'email',
  FORGOT = 'forgot',
  MOBILE = 'mobile',
}

export enum connectionRequestStatus {
  ACCEPT = 'accept',
  REJECT = 'reject',
  CANCEL = 'cancel',
  REMOVE = 'remove',
}

export enum countryCityState {
  COUNTRY = 'country',
  STATE = 'state',
  CITY = 'city',
}

export class SignupDataDto {
  @IsString()
  itemId: string;

  @IsBoolean()
  isSelected: boolean;
}

class SocialLinkDto {
  @IsOptional()
  @IsString()
  website: string;

  @IsOptional()
  @IsString()
  imdb: string;

  @IsOptional()
  @IsString()
  facebook: string;

  @IsOptional()
  @IsString()
  instagram: string;

  @IsOptional()
  @IsString()
  tiktok: string;

  @IsOptional()
  @IsString()
  linkedin: string;

  @IsOptional()
  @IsString()
  twitter: string;

  @IsOptional()
  @IsString()
  youtube: string;

  @IsOptional()
  @IsString()
  patreon: string;
}
class WorkStatusDto {
  @IsString()
  memberAs: string;

  @IsBoolean()
  isSelected: boolean;

  // @IsString()
  // addTxt: string | null;
}

class AvailabilityForWorkDto {
  @IsOptional()
  @IsBoolean({ message: 'Available To Work must be a boolean' })
  availableToWork: boolean;

  @IsOptional()
  @IsString({ message: 'Currently Working must be a string' })
  currentlyWorking?: string;

  @IsOptional()
  @IsString({ message: 'Hiring must be a string' })
  hiring?: string;

  @IsOptional()
  @IsString({ message: 'Investing in Projects must be a string' })
  investingInProjects?: string;

  @IsOptional()
  @IsString({ message: 'Availability Dates must be string' })
  availabilityDates: string;

  @IsOptional()
  @IsString({ message: 'Based In must be a string' })
  basedIn?: string;

  @IsOptional()
  @IsBoolean({
    message:
      'Willing To Travel For Work Nation-wide Or Relocate must be a boolean',
  })
  willingToTravelForWorkNationwideOrRelocate: boolean;

  @IsOptional()
  @IsBoolean({
    message:
      'Willing To Travel For Work World-wide Or Relocate must be a boolean',
  })
  willingToTravelForWorkWorldwideOrRelocate: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'Willing To Work From Home must be a boolean',
  })
  willingToWorkFromHome: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'Willing To Work Onsite must be a boolean',
  })
  willingToWorkOnSite: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'Willing To Work Hybrid must be a boolean',
  })
  willingToWorkHybrid: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'Valid Passport must be a boolean',
  })
  validPassport: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'Valid Driver License must be a boolean',
  })
  validDriverLicense: boolean;
}

class JobDetailDto {
  @IsOptional()
  @IsBoolean({ message: 'isCastingCallAudition must be a boolean' })
  isCastingCallAudition: boolean;

  @IsOptional()
  @IsString({ message: 'roleType must be a string' })
  roleType?: string;

  @IsOptional()
  @IsString({ message: 'breakDownOfRoleOrPosition must be a string' })
  breakDownOfRoleOrPosition?: string;

  @IsOptional()
  @IsString({ message: 'lookingForProfessions must be a string' })
  lookingForProfessions?: string;

  @IsOptional()
  @IsString({ message: 'demographics must be a string' })
  demographics?: string;

  @IsOptional()
  @IsString({ message: 'projectType must be a string' })
  projectType?: string;

  @IsOptional()
  @IsString({ message: 'genre must be a string' })
  genre?: string;

  @IsOptional()
  @IsString({
    message: 'synopsisOfProjectOrDescriptionOfJobOpportunity must be a string',
  })
  synopsisOfProjectOrDescriptionOfJobOpportunity?: string;

  @IsOptional()
  @IsString({ message: 'companyOrNetwork must be a string' })
  companyOrNetwork?: string;

  @IsOptional()
  @IsString({
    message:
      'typeOfOrganizationYouAreWorkingWithForThisProject must be a string',
  })
  typeOfOrganizationYouAreWorkingWithForThisProject?: string;

  @IsOptional()
  @IsString({ message: 'ageRange must be a string' })
  ageRange?: string;

  @IsOptional()
  @IsArray({ message: 'selfIdentifiableQualities must be an array of strings' })
  selfIdentifiableQualities: string[];

  @IsOptional()
  @IsString({ message: 'dateNeededToStart must be a string' })
  dateNeededToStart?: string;

  @IsOptional()
  @IsString({ message: 'dateOfWorkCompleted must be a string' })
  dateOfWorkCompleted?: string;

  @IsOptional()
  @IsBoolean({ message: 'ongoingWork must be a boolean' })
  ongoingWork: boolean;

  @IsOptional()
  @IsBoolean({ message: 'inPerson must be a boolean' })
  inPerson: boolean;

  @IsOptional()
  @IsBoolean({ message: 'virtual must be a boolean' })
  virtual: boolean;

  @IsOptional()
  @IsString({ message: 'locationOrLink must be a string' })
  locationOrLink?: string;

  @IsOptional()
  @IsBoolean({ message: 'localHireOnly must be a boolean' })
  localHireOnly: boolean;

  @IsOptional()
  @IsBoolean({ message: 'openNationwide must be a boolean' })
  openNationwide: boolean;

  @IsOptional()
  @IsBoolean({ message: 'openWorldwide must be a boolean' })
  openWorldwide: boolean;

  @IsOptional()
  @IsBoolean({ message: 'isPay must be a boolean' })
  isPay: boolean;

  @IsOptional()
  @IsString({ message: 'jobType must be a string' })
  jobType?: string;

  @IsOptional()
  @IsString({ message: 'rateType must be a string' })
  rateType?: string;

  @IsOptional()
  @IsNumber()
  hourlyExpectedHours?: number;

  @IsOptional()
  @IsString({ message: 'currency must be a string' })
  currency?: string;

  @IsOptional()
  @IsNumber()
  pay?: number;

  @IsOptional()
  @IsString({ message: 'payRange must be a string' })
  payRange?: string;

  @IsOptional()
  @IsString({ message: 'contractDetailsOrCompensation must be a string' })
  contractDetailsOrCompensation?: string;

  @IsOptional()
  @IsBoolean({ message: 'noPayDeferredPay must be a boolean' })
  noPayDeferredPay: boolean;

  @IsOptional()
  @IsBoolean({ message: 'isUnion must be a boolean' })
  isUnion: boolean;

  @IsOptional()
  @IsBoolean({ message: 'isNonUnion must be a boolean' })
  isNonUnion: boolean;

  @IsOptional()
  @IsArray({ message: 'mediaRequired must be an array of strings' })
  mediaRequired: string[];

  @IsOptional()
  @IsString({ message: 'titleOfEvent must be a string' })
  titleOfEvent?: string;

  @IsOptional()
  @IsString({ message: 'forWhatDescriptionOfEvent must be a string' })
  forWhatDescriptionOfEvent?: string;

  @IsOptional()
  @IsArray({ message: 'whenDatesIncludeRecurring must be an array of strings' })
  whenDatesIncludeRecurring: string[];

  @IsOptional()
  @IsString({ message: 'time must be a string' })
  time?: string;

  @IsOptional()
  @IsBoolean({ message: 'isThisRecurring must be a boolean' })
  isThisRecurring: boolean;

  @IsOptional()
  @IsString({ message: 'ifRecurring must be a string' })
  ifRecurring?: string;

  @IsOptional()
  @IsBoolean({ message: 'isThereFee must be a boolean' })
  isThereFee: boolean;

  @IsOptional()
  @IsString({ message: 'whatIsTheFeeCost must be a string' })
  whatIsTheFeeCost?: string;

  @IsOptional()
  @IsString({ message: 'sendPaymentTo must be a string' })
  sendPaymentTo?: string;

  @IsOptional()
  @IsBoolean({ message: 'isItUnionMembersOnly must be a boolean' })
  isItUnionMembersOnly: boolean;

  @IsOptional()
  @IsString({ message: 'bioOfPersonOrOrganizationHosting must be a string' })
  bioOfPersonOrOrganizationHosting?: string;

  @IsOptional()
  @IsString({ message: 'whenShouldThisEventExpire must be a string' })
  whenShouldThisEventExpire?: string;
}

class whatDoesThisProjectInvolveDto {
  @IsOptional()
  @IsBoolean({
    message: 'doesThisProjectInvolveBodyScanning must be a boolean',
  })
  doesThisProjectInvolveBodyScanning: boolean;

  @IsOptional()
  @IsBoolean({ message: 'doesThisProjectInvolveAI must be a boolean' })
  doesThisProjectInvolveAI: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'doesThisProjectInvolveGenerativeAI must be a boolean',
  })
  doesThisProjectInvolveGenerativeAI: boolean;

  @IsOptional()
  @IsBoolean({
    message: 'doesThisProjectInvolveSyntheticPerformers must be a boolean',
  })
  doesThisProjectInvolveSyntheticPerformers: boolean;

  @IsOptional()
  @IsString({
    message: 'anyOfTheBodyScansAIquestionsAboveHowSo must be a string',
  })
  anyOfTheBodyScansAIquestionsAboveHowSo?: string;

  @IsOptional()
  @IsBoolean({ message: 'doesThisProjectInvolveAnyNudity must be a boolean' })
  doesThisProjectInvolveAnyNudity: boolean;

  @IsOptional()
  @IsBoolean({ message: 'willThereBeAnIntimacyCoordinator must be a boolean' })
  willThereBeAnIntimacyCoordinator: boolean;

  @IsOptional()
  @IsBoolean({
    message:
      'doesThisProjectInvolveAnythingElseThatNeedsMentioningUpFront must be a boolean',
  })
  doesThisProjectInvolveAnythingElseThatNeedsMentioningUpFront: boolean;

  @IsOptional()
  @IsString({ message: 'pleaseTypeWhatItInvolves must be a string' })
  pleaseTypeWhatItInvolves?: string;

  @IsOptional()
  @IsString({ message: 'anyNotes must be a string' })
  anyNotes?: string;

  @IsOptional()
  @IsString({
    message: 'ifInterestedSubmitMaterialsOrResponseHere must be a string',
  })
  ifInterestedSubmitMaterialsOrResponseHere?: string;

  @IsOptional()
  @IsString({ message: 'forwardJobListingTo must be a string' })
  forwardJobListingTo?: string;

  @IsOptional()
  @IsString({ message: 'whenShouldThisJobListingExpire must be a string' })
  whenShouldThisJobListingExpire?: string;
}

class yourInfoDto {
  @IsOptional()
  @IsString({ message: 'firstName must be a string' })
  firstName?: string;

  @IsOptional()
  @IsString({ message: 'lastName must be a string' })
  lastName?: string;

  @IsOptional()
  @IsString({ message: 'companyName must be a string' })
  companyName?: string;

  @IsOptional()
  @IsString({ message: 'companyWebsite must be a string' })
  companyWebsite?: string;

  @IsOptional()
  @IsString({ message: 'yourJobTitle must be a string' })
  yourJobTitle?: string;

  @IsOptional()
  @IsString({ message: 'country must be a string' })
  country?: string;

  @IsOptional()
  @IsString({ message: 'postalCode must be a string' })
  postalCode?: string;

  @IsOptional()
  @IsString({ message: 'forwardEventTo must be a string' })
  forwardEventTo?: string;
}

export class ContactInfoDto {
  @IsString()
  slug: string;

  @IsOptional()
  @IsString()
  name: string | null;

  @IsOptional()
  @IsString()
  address: string | null;

  @IsOptional()
  @IsString()
  phoneNumber: string | null;

  @IsOptional()
  @IsEmail()
  email: string | null;

  @IsBoolean()
  isSelected = false;
}

export class InterestDto {
  @IsString()
  category: string;

  @IsArray()
  subCategory: string[];
}

export class LanguageInfoDto {
  @IsString()
  language: string;

  @IsString()
  proficiencyLevel: string;
}

export class AboutInfoDto {
  @IsString()
  itemText: string;

  @IsString()
  idNumber: string;

  @IsString()
  memberIdCard: string;

  @IsString()
  memberDoc: string;

  @IsArray()
  iAm: SignupDataDto[];
}

export class DemographicsDto {
  @IsString()
  self_identify: string;

  @IsString()
  addTxt: string | null;
}

export class signUpDto {
  @IsEnum(iAmMemberEnum)
  iAmMember: iAmMemberEnum;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(40)
  @MinLength(3)
  firstName: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @MaxLength(40)
  @MinLength(2)
  lastName: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  businessOrganizationName: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(15)
  @MinLength(3)
  @Matches(/^[A-Za-z0-9_.-]{3,15}$/, {
    message:
      'Username must contain lowercase letters(A-Z), numbers(0-9), underscores(_), hyphens(-) and dot(.)',
  })
  userName: string;

  @IsOptional()
  @IsString()
  profileImage: string;

  @IsBoolean()
  accountVerified: boolean;

  @IsOptional()
  @IsString()
  country: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  state: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  @IsNotEmpty()
  countryCode: string;

  // @IsString()
  // @IsOptional()
  // phone: string;

  @IsOptional()
  @IsString()
  website: string;

  @IsOptional()
  businessAddress: object;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*-{}?._&])[A-Za-z\d@#$!%*-{}?._&]{8,30}$/,
    {
      message: 'Password is weak',
    },
  )
  password: string;

  @IsArray()
  signUpData: SignupDataDto[];

  @IsOptional()
  isFunding: boolean;
}

export class signInDto {
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class fakeAccountDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  businessOrganizationName: string;

  @IsEnum(WhoAreYou)
  accountType: string;
}

export class userNameDto {
  @IsString()
  @IsNotEmpty()
  // @MinLength(5)
  // @MaxLength(15)
  // @Matches(/^[A-Za-z0-9_.-]{5,15}$/, {
  //   message:
  //     'Username must contain lowercase letters(A-Z,a-z), numbers(0-9), underscores(_), hyphens(-) and dot(.)',
  // })
  userName: string;
}

export class emailDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  countryCode: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsEnum(sendVerify)
  type: sendVerify;
}

export class verifyOtpDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  countryCode: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsNotEmpty()
  otp: string;

  @IsEnum(sendVerify)
  type: sendVerify;
}

export class resetPasswordDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*-{}?._&])[A-Za-z\d@#$!%*-{}?._&]{8,30}$/,
    {
      message: 'Password is weak',
    },
  )
  password: string;

  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;
}

export class countryCityStateDto {
  @IsEnum(countryCityState)
  type: countryCityState;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  isoCode: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  countryCode: string;
}

export class privacySettingDto {
  // @IsOptional()
  // @IsEnum(ProfileOpenEnum)
  // profileOpen: ProfileOpenEnum;

  // @IsOptional()
  // contactYou: AreYouDataDto[];

  // @IsOptional()
  // collaborationOptions: AreYouDataDto[];

  // @IsOptional()
  // notify: AreYouDataDto[];

  @IsOptional()
  @IsBoolean()
  isOnline: boolean;
}

export class visibilitySettingDto {
  @IsNotEmpty()
  @IsEnum(collaborationEnum)
  collaboration: collaborationEnum;
}

export class socialMediaDto {
  @IsOptional()
  socialMedia: SocialLinkDto;

  @IsOptional()
  @IsNotEmpty()
  contactInfo: ContactInfoDto[];
}

export class genderDto {
  @IsNotEmpty()
  @IsEnum(GenderEnum)
  gender: GenderEnum;

  @IsOptional()
  @IsNullOrString()
  customGenderValue: string;
}

export class profileDto {
  @IsOptionalString()
  @MaxLength(40)
  @MinLength(3)
  firstName: string;

  @IsOptionalString()
  @MaxLength(40)
  @MinLength(3)
  lastName: string;

  @IsOptionalString()
  businessOrganizationName: string;

  @IsOptionalString()
  profileImage: string;

  @IsOptionalString()
  bio: string;

  @IsOptionalString()
  gender: string;

  @IsOptional()
  @IsNullOrString()
  customGenderValue: string;

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  education: string[];

  @IsOptional()
  @IsNotEmpty()
  degree: DemographicsDto[];

  @IsOptional()
  @IsNotEmpty()
  profession: DemographicsDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AvailabilityForWorkDto)
  availabilityForWork?: AvailabilityForWorkDto;

  @IsOptional()
  @IsNotEmpty()
  passportCountries: DemographicsDto[];

  @IsOptional()
  @IsNotEmpty()
  jobType: WorkStatusDto[];

  @IsOptional()
  @IsNotEmpty()
  interests: InterestDto[];
}

export class selfIdentifyDto {
  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  ethnicity: string[];

  @IsOptionalBoolean()
  ethnicityVisibleOnProfile: boolean;

  @IsOptionalBoolean()
  ethnicitySearchable: boolean;

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  nationality: string[];

  @IsOptionalBoolean()
  nationalityVisibleOnProfile: boolean;

  @IsOptionalBoolean()
  nationalitySearchable: boolean;

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  disability: string[];

  @IsOptionalBoolean()
  disabilityVisibleOnProfile: boolean;

  @IsOptionalBoolean()
  disabilitySearchable: boolean;

  @IsOptional()
  @IsNullOrString()
  customDisability: string;

  @IsOptional()
  @IsNotEmpty()
  languagesSpokenSigned: LanguageInfoDto[];

  @IsOptional()
  @Transform(({ value }) => new Date(value))
  birthDate: Date;

  @IsOptional()
  ageRange: string;

  @IsOptional()
  showYearOnYourBirthday: boolean;

  @IsOptionalBoolean()
  birthDateVisibleOnProfile: boolean;

  @IsOptionalBoolean()
  birthDateSearchable: boolean;

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  skills: string[];

  @IsOptional()
  @IsString({ each: true })
  selfIgender: string[];

  @IsOptionalBoolean()
  selfIgenderVisibleOnProfile: boolean;

  @IsOptionalBoolean()
  selfIgenderSearchable: boolean;

  @IsOptional()
  genderPronoun: string;

  @IsOptional()
  customGenderInfo: string;

  @IsOptional()
  @IsNullOrString()
  customSelfIgender: string;

  @IsOptional()
  @IsNotEmpty()
  other: string[];

  @IsOptionalBoolean()
  otherVisibleOnProfile: boolean;

  @IsOptionalBoolean()
  otherSearchable: boolean;

  @IsOptional()
  @IsArray()
  fanDegree: string[];
}

export class UpdateSelfIdentifyLanguagesDTO {
  @IsNotEmpty()
  languagesSpokenSigned: LanguageInfoDto[];
}

export class fileUploadDto {
  @IsNotEmpty()
  @IsString()
  uploadType: string;
}

export class aboutYouDto {
  @IsOptional()
  @IsArray()
  union: AboutInfoDto[];

  @IsOptional()
  @IsNotEmpty()
  affiliateOrganization: AboutInfoDto[];

  @IsOptional()
  @IsNotEmpty()
  affiliateBusiness: AboutInfoDto[];

  @IsOptional()
  @IsNotEmpty()
  schoolTrainingFacility: AboutInfoDto[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  visualArts: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  performingArts: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  dance: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  acting: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  music: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  filmMedia: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  design: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  literaryArts: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  crafts: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  appliedArts: string[];

  @IsArray()
  @IsOptional()
  @ArrayUnique()
  @IsString({ each: true })
  other: string[];
}

export class userProfileAboutYouDto {
  @IsString()
  @IsOptional()
  description: string;

  @IsString()
  @IsOptional()
  website: string;

  @IsString()
  @IsOptional()
  industry: string;

  @IsString()
  @IsOptional()
  phone: string;

  @IsString()
  @IsOptional()
  founded: string;

  @IsString()
  @IsOptional()
  organizationType: string;

  @IsString()
  @IsOptional()
  organizationSize: string;

  @IsString()
  @IsOptional()
  headquarter: string;
  @IsString()
  @IsOptional()
  member: string;

  @IsOptional()
  @ArrayUnique()
  @IsString()
  skills: string[];

  @IsOptional()
  @ArrayUnique()
  @IsString()
  specialization: string[];
}

export class EditContactInfoDto {
  @IsOptional()
  @IsString()
  slug: string;

  @IsOptional()
  @IsBoolean()
  isSelected: boolean;

  @IsOptional()
  @IsString()
  _id: string;

  @IsOptional()
  @IsString()
  name: string | null;

  @IsOptional()
  @IsString()
  address: string | null;

  @IsOptional()
  @IsString()
  phoneNumber: string | null;

  @IsOptional()
  @IsEmail()
  email: string | null;
}

export class editProfileDto {
  @IsOptional()
  aboutYou: aboutYouDto;

  @IsOptional()
  phone: string;

  @IsOptional()
  userProfileAboutYou: userProfileAboutYouDto;

  @IsOptional()
  @IsArray()
  signUpData: SignupDataDto[];

  @IsOptional()
  socialMedia: SocialLinkDto;

  @IsOptional()
  contactInfo: EditContactInfoDto[];

  @IsBoolean()
  @IsOptional()
  isNotificationOn: boolean;

  @IsBoolean()
  @IsOptional()
  isEmailOn: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parentUserIds: string[];

  hirerEmployerVerifiedStatus?: string;
}

export class followUnfollowDto {
  @IsString()
  followingId: string;

  @IsOptionalString()
  status: string;
}

export class AcceptRejectfollowDto {
  @IsString()
  followerId: string;

  @IsOptionalString()
  status: string;
}

export class subscribeDto {
  @IsString()
  userId: string;
}

export class hirerVerificationDTO {
  @IsEmail()
  email: string;

  @IsString()
  fullName: string;

  @IsString()
  position: string;

  @IsString()
  profession: string;

  @IsString()
  website: string;

  @IsString()
  message: string;
}

export class connectionRequestDto {
  connectionWithId: string;
}

export class clientRequestDto {
  clientId: string;
}

export class memberVerificationDto {
  @IsString()
  id: string;

  @IsString()
  email: string;

  @IsString()
  idNumber: string;

  @IsString()
  document: string;
}

export class connectionRequestAcceptRejectDto {
  @IsString()
  requestId: string;

  @IsOptional()
  @IsString()
  notificationId: string;

  @IsEnum(connectionRequestStatus)
  status: connectionRequestStatus;
}

export class clientRequestAcceptRejectDto {
  @IsString()
  requestId: string;

  @IsOptional()
  @IsString()
  notificationId: string;

  @IsEnum(StatusEnum)
  status: StatusEnum;
}

export class getFollowersFollowingDto {
  @IsOptional()
  @IsString()
  userId: string;

  @IsString()
  type: string;
}

export class logoutDto {
  @IsString()
  deviceToken: string;
}

export class changePasswordDto {
  @IsString()
  @IsNotEmpty()
  oldPassword: string;

  @IsString()
  @IsNotEmpty()
  newPassword: string;
}

export class feedbackDto {
  @IsString()
  category: string;

  @IsString()
  title: string;

  @IsString()
  description: string;
}

export class publicationDto {
  @IsOptional()
  @IsString()
  _id?: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsNotEmpty()
  website: string;
}

export class professionDto {
  @IsArray()
  @IsString({ each: true })
  professions: string[];
}
export class AffiliatePagesDto {
  @IsString()
  name: string;

  @IsBoolean()
  @IsOptional()
  isVerified: boolean;
}

export class affliatePagesListDto {
  @IsArray()
  affiliatePages: AffiliatePagesDto[];
}

export class socialActivitiesDto {
  @IsArray()
  socialActivities: string[];
}

export class fieldOfStudyDto {
  @IsArray()
  fieldOfStudy: string[];
}

export class offeredServicesDto {
  @IsArray()
  offeredServices: string[];
}

export class causesDto {
  @IsArray()
  causes: string[];
}

export class jobDto {
  @IsNotEmpty()
  @IsEnum(IsJobForEnum)
  isJobFor: IsJobForEnum;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => JobDetailDto)
  jobDetail?: JobDetailDto;

  // @IsOptional()
  // whoCanSeeRespondToListing: AreYouDataDto[];

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => whatDoesThisProjectInvolveDto)
  whatDoesThisProjectInvolve?: whatDoesThisProjectInvolveDto;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => yourInfoDto)
  yourInfo?: yourInfoDto;
}

export class profileViewDto {
  @IsString()
  viewerId: string;
}

export class searchDto {
  @IsEnum(hashtagSearchData)
  type: hashtagSearchData;

  @IsString()
  search: string;

  @IsNumber()
  page: number;

  @IsNumber()
  perPage: number;
}

export class SignupData {
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  slug: string[];
}

export class GetNotificationsDto {
  @IsStringValidation('page', 3, true)
  readonly page?: string | number;

  @IsStringValidation('per page', 3, true)
  readonly perPage?: string | number;
}

export class GetUserListing {
  @IsStringValidation('search', 32, false)
  readonly search: string;

  @IsStringValidation('page', 10, false)
  readonly page: string;
  @IsStringValidation('perPage', 10, false)
  readonly perPage: string;
}

export class AddClientDto {
  @IsString()
  readonly clientId: string;
}

export class GetUserSelectionListingDto {
  @IsArray()
  @IsEnum(WhoAreYou, { each: true })
  @Type(() => String)
  readonly listOfType: WhoAreYou[];

  @IsOptional()
  @IsBoolean()
  isFakeAccount: boolean;

  @IsOptional()
  @IsBoolean()
  isFunding: boolean;

  @IsOptional()
  @IsBoolean()
  include: boolean;
}
