# Organization Migration - Complete Guide

## 🎯 What This Does

This migration system allows you to:
1. **Delete all existing organizations** from your database
2. **Import new organizations** from an Excel file with 3 columns
3. **Automatically categorize** organizations by main category and subcategory
4. **Set up proper flows** for organization APIs and search functionality

## 📁 Files Created

- `migrate_organizations_from_excel.js` - Main migration script
- `quick_start_migration.js` - Interactive migration guide
- `test_organization_migration.js` - Test script for verification
- `ORGANIZATION_MIGRATION_GUIDE.md` - Detailed documentation
- `README_ORGANIZATION_MIGRATION.md` - This file

## 🚀 Quick Start

### Option 1: Interactive Guide (Recommended)
```bash
node quick_start_migration.js
```
This will guide you through the entire process step by step.

### Option 2: Direct Migration
```bash
# Create sample file first
node migrate_organizations_from_excel.js

# Then run with your Excel file
node migrate_organizations_from_excel.js your_organizations.xlsx
```

### Option 3: Test First
```bash
node test_organization_migration.js
```

## 📊 Excel File Format

Your Excel file must have exactly 3 columns:

| Main Category | Sub Category | Organization |
|---------------|--------------|--------------|
| Union | Actors Union | SAG-AFTRA |
| Union | Directors Union | DGA |
| Affiliate Business | Production Company | Warner Bros |
| School/Training | Film School | USC School of Cinematic Arts |

**Requirements:**
- First row contains headers
- No empty rows
- Descriptive category names
- Avoid special characters

## 🔄 Migration Process

### 1. **Data Cleanup**
- Removes all existing organizations
- Clears organization hierarchy
- Ensures clean slate

### 2. **Data Import**
- Reads Excel file
- Validates format
- Creates user records
- Sets `isFakeAccount: true`

### 3. **Hierarchy Creation**
- Groups by categories
- Creates relationships
- Enables API queries

### 4. **Data Normalization**
- Converts to URL-friendly slugs
- Ensures unique usernames
- Standardizes format

## 📋 What Gets Created

### User Records
```json
{
  "businessOrganizationName": "SAG-AFTRA",
  "iAmMember": "unionAffiliateOrganizationBusinessSchoolsTrainingFacility",
  "isFakeAccount": true,
  "organizationMainCategory": "union",
  "organizationSubcategory": "actors_union",
  "userName": "sagaftra_0",
  "email": "<EMAIL>"
}
```

### Hierarchy Records
```json
{
  "mainCategory": "union",
  "subcategory": "actors_union",
  "organizationName": "SAG-AFTRA",
  "organizationId": "ObjectId...",
  "organizationCount": 1
}
```

## 🌐 API Integration

After migration, these APIs work automatically:

- `GET /api/v1/user/organization-categories` - Main categories
- `GET /api/v1/user/organization-subcategories/:mainCategorySlug` - Subcategories  
- `GET /api/v1/user/organizations-by-subcategory/:subcategorySlug` - Organizations

## ⚠️ Important Notes

1. **Backup First**: Always backup your database before running migrations
2. **Data Loss**: This will DELETE ALL existing organizations
3. **Fake Accounts**: Organizations are created as `isFakeAccount: true`
4. **Conversion**: Real users can claim organizations during signup
5. **Temporary Passwords**: Default password is `TempPassword123!`

## 🛠️ Troubleshooting

### Common Issues

1. **Excel Format Errors**
   - Check headers match exactly
   - Remove empty rows
   - Validate data format

2. **Database Connection**
   - Verify MongoDB connection in `.env`
   - Check database accessibility
   - Ensure proper permissions

3. **Duplicate Usernames**
   - Script handles automatically
   - Check for very long names

### Error Recovery

- Script is idempotent (safe to re-run)
- Check console output for specific errors
- Fix Excel file if needed
- Re-run migration

## 📚 Detailed Documentation

- **`ORGANIZATION_MIGRATION_GUIDE.md`** - Complete technical guide
- **`migrate_organizations_from_excel.js`** - Source code with comments
- **`quick_start_migration.js`** - Interactive helper script

## 🔍 Testing

After migration:

1. **Verify APIs work**:
   ```bash
   curl http://localhost:3000/api/v1/user/organization-categories
   ```

2. **Check search results**:
   - Organizations appear in global search
   - Category filtering works
   - Organization profiles accessible

3. **Test user flows**:
   - Real users can sign up
   - Organizations convert from fake to real
   - Categories are properly assigned

## 🎉 Success Indicators

- Migration script completes without errors
- Console shows correct count of organizations
- APIs return expected data
- Search functionality works
- No database errors in logs

## 🆘 Support

If you encounter issues:

1. Check console output for error messages
2. Verify Excel file format
3. Ensure dependencies are installed
4. Check database connectivity
5. Review the detailed guide in `ORGANIZATION_MIGRATION_GUIDE.md`

---

**Ready to migrate?** Start with `node quick_start_migration.js` for the easiest experience!
