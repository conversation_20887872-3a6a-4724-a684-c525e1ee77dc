import { Document, FilterQuery, Model, UpdateQuery } from 'mongoose';

export abstract class AbstractRepository<T extends Document> {
  constructor(protected readonly model: Model<T>) {}

  async create(data: Partial<T>): Promise<T> {
    return new this.model(data).save();
  }

  async findById(id: string): Promise<T | null> {
    return this.model.findById(id).exec();
  }

  async findByIdAndUpdate(
    id: string,
    updateData: UpdateQuery<T>,
  ): Promise<T | null> {
    return this.model
      .findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      })
      .exec();
  }

  async findByFilterAndUpdate(
    filter: FilterQuery<T>,
    updateData: UpdateQuery<T>,
  ): Promise<T | null> {
    return this.model
      .findOneAndUpdate(filter, updateData, {
        new: true,
        runValidators: true,
      })
      .exec();
  }

  async findAll(): Promise<T[]> {
    return this.model.find().exec();
  }

  async find(
    filter: FilterQuery<T>,
    sortObj: any,
    skipData: number,
    limitData: number,
  ): Promise<T[]> {
    return this.model
      .find(filter)
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();
  }

  async countDocuments(filter: FilterQuery<T>): Promise<number> {
    return this.model.countDocuments(filter).exec();
  }

  async deleteById(id: string): Promise<void> {
    await this.model.findByIdAndDelete(id).exec();
  }
}
