import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { JobPost, JobPostDocument } from 'src/Models/jobPost.schema';
import { JobApplicationDocument } from 'src/Models/jobApplication.schema';
import { AbstractRepository } from './abstract.repository';

@Injectable()
export class JobPostRepository extends AbstractRepository<JobPostDocument> {
  constructor(
    @InjectModel(JobPost.name) jobPostModel: Model<JobPostDocument>,
    @InjectModel('JobApplication')
    private readonly jobApplicationModel: Model<JobApplicationDocument>,
  ) {
    super(jobPostModel);
  }

  async applyForJob(data: any): Promise<JobApplicationDocument> {
    const application = await new this.jobApplicationModel(data).save();
    await this.model.findByIdAndUpdate(data.jobId, {
      $push: { applications: application._id },
    });
    return application;
  }

  async getAllJobPostsOfUser(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ): Promise<JobPost[]> {
    return this.model
      .find(filter)
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({
        path: 'applications',
        select: '-jobId',
        options: { sort: { createdAt: -1 }, limit: 1 },
        populate: {
          path: 'userId',
          match: { isFakeAccount: false },
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        },
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();
  }

  async getJobApplications(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ): Promise<JobApplicationDocument[]> {
    return this.jobApplicationModel
      .find(filter)
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({ path: 'jobId', select: '-applications' })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();
  }

  async getJobApplicationById(
    applicationId: string,
  ): Promise<JobApplicationDocument | null> {
    return this.jobApplicationModel
      .findById(applicationId)
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({ path: 'jobId', select: '-applications' })
      .exec();
  }

  async updateJobApplicationStatus(
    applicationId: string,
    status: string,
  ): Promise<JobApplicationDocument | null> {
    return this.jobApplicationModel
      .findByIdAndUpdate(applicationId, { status }, { new: true })
      .exec();
  }
}
