import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import CONSTANT from '../../../common/constant/common.constant';
import { StreamChat } from 'stream-chat';
import { InjectModel } from '@nestjs/mongoose';
import { userSignupDataDocument } from 'src/Models/userSignupData.schema';
import mongoose, { Model } from 'mongoose';
import { userDocument } from 'src/Models/user.schema';
import { WhoCanMessageValidatorService } from '../helper/whoCanMessageValidator';

@Injectable()
export class StreamChatService {
  constructor(
    private readonly configService: ConfigService,
    @InjectModel('UserSignupData')
    private readonly userSignupDataModel: Model<userSignupDataDocument>,
    @InjectModel('User')
    private readonly userModel: Model<userDocument>,
    private readonly whoCanMessageValidatorService: WhoCanMessageValidatorService,
  ) {}

  // instantiate your stream client using the API key and secret
  // the secret is only used server side and gives you full access to the API
  serverClient = StreamChat.getInstance(
    this.configService.get<string>('STREAM_APP_KEY'),
    this.configService.get<string>('STREAM_APP_SECRET'),
  );

  //For getting STREAM.IO chat token
  async getChatToken(req) {
    try {
      const { user: loggedInUser } = req;
      // generate a token for the userId
      const token = this.serverClient.createToken(`${loggedInUser._id}`);
      return successResponse({ token }, CONSTANT.TOKEN, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  //Create user in stream for chat
  async createOrUpdateUserForChat({
    id,
    profile_image,
    name,
    userName,
    businessOrganizationName,
  }) {
    try {
      const userChatData = {
        id,
        role: 'user',
        image: profile_image,
        name,
        username: userName,
        businessOrganizationName: businessOrganizationName,
      };
      Object.keys(userChatData).forEach((key) => {
        if (!userChatData[key]) {
          delete userChatData[key];
        }
      });

      await this.serverClient.upsertUser({ ...userChatData });
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  //Create channel when user message another user
  async createChannel(req, userId) {
    try {
      const { user: loggedInUser } = req;

      const channel = await this.serverClient.channel('messaging', {
        members: [`${loggedInUser._id}`, `${userId}`],
        created_by_id: `${loggedInUser._id}`,
      });
      const result = await channel.create();
      console.log('result=', result);

      return successResponse(
        result,
        CONSTANT.CREATED_SUCCESSFULLY('Channel'),
        HttpStatus.CREATED,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  // Send message and create channel if it doesn't exist
  async sendMessageCreateChannel(req, userId) {
    try {
      const { user: loggedInUser } = req;

      if (!loggedInUser._id || !userId) {
        throw new HttpException(
          { message: 'Invalid user IDs' },
          HttpStatus.BAD_REQUEST,
        );
      }

      const targetUser: any = await this.userModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(userId),
          },
        },
        {
          $unwind: {
            path: '$signUpData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'signUpData.itemId',
            foreignField: '_id',
            as: 'signUpDetails',
            // pipeline: [
            //   {
            //     $match: {
            //       title: 'who_can_message',
            //     },
            //   },
            // ],
          },
        },
        {
          $unwind: {
            path: '$signUpDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'signUpData.itemId': {
              _id: '$signUpDetails._id',
              title: '$signUpDetails.title',
              slug: '$signUpDetails.slug',
              itemText: '$signUpDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: '$_id',
            user: { $first: '$$ROOT' }, // grab first user fields
            signUpData: { $push: '$signUpData' }, // re-assemble signUpData array
          },
        },
        {
          $addFields: {
            'user.signUpData': '$signUpData',
          },
        },
        {
          $replaceRoot: {
            newRoot: '$user',
          },
        },
      ]);

      if (!targetUser) {
        throw new HttpException(
          { message: 'User not found' },
          HttpStatus.NOT_FOUND,
        );
      }
      const currentUser: any = await this.userModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(loggedInUser._id.toString()),
          },
        },
        {
          $unwind: {
            path: '$signUpData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'signUpData.itemId',
            foreignField: '_id',
            as: 'signUpDetails',
            // pipeline: [
            //   {
            //     $match: {
            //       title: 'who_can_message',
            //     },
            //   },
            // ],
          },
        },
        {
          $unwind: {
            path: '$signUpDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'signUpData.itemId': {
              _id: '$signUpDetails._id',
              title: '$signUpDetails.title',
              slug: '$signUpDetails.slug',
              itemText: '$signUpDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: '$_id',
            user: { $first: '$$ROOT' }, // grab first user fields
            signUpData: { $push: '$signUpData' }, // re-assemble signUpData array
          },
        },
        {
          $addFields: {
            'user.signUpData': '$signUpData',
          },
        },
        {
          $replaceRoot: {
            newRoot: '$user',
          },
        },
      ]);

      const isValidated: any =
        await this.whoCanMessageValidatorService.validate(
          currentUser[0],
          targetUser[0],
        );

      if (!isValidated.status) {
        throw new HttpException(
          { message: isValidated.message || 'You cannot message this user' },
          HttpStatus.BAD_REQUEST,
        );
      }

      const sortedIds = [loggedInUser._id.toString(), userId.toString()].sort();
      const channelId = `${sortedIds[0]}-${sortedIds[1]}`;

      const channel = this.serverClient.channel('messaging', channelId, {
        members: sortedIds,
        created_by_id: `${loggedInUser._id}`,
      });

      const channelState = await channel.query();

      if (
        !channelState.members.some(
          (member) => member.user_id === loggedInUser._id.toString(),
        )
      ) {
        await channel.create();
      }

      return successResponse(channelId, '', HttpStatus.CREATED);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  //Delete channel between two users
  async deleteChannel(channelId) {
    try {
      await this.serverClient.deleteChannels([channelId], {
        hard_delete: true,
      });

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Channel'),
        HttpStatus.CREATED,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  //Create channel when user message another user
  async createGroupChannel(req, groupChatData) {
    try {
      const { user: loggedInUser } = req;
      console.log(groupChatData.users);
      const channel = await this.serverClient.channel('messaging', {
        name: groupChatData.groupName,
        members: groupChatData.users,
        created_by_id: `${loggedInUser._id}`,
      });
      const result = await channel.create();

      return successResponse(
        result,
        CONSTANT.CREATED_SUCCESSFULLY('Group Channel'),
        HttpStatus.CREATED,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  //User Leave Group channel
  async leaveGroupChannel(req, leaveChannelData) {
    try {
      const { user: loggedInUser } = req;

      const data = await this.serverClient.queryChannels({
        type: 'messaging',
        id: { $eq: `${leaveChannelData.groupId}` },
      });
      console.log('data=', data);
      const result = await data[0].removeMembers([
        `${loggedInUser._id.toString()}`,
      ]);
      console.log('result=', result);

      return successResponse(null, CONSTANT.LEAVE_CHANNEL, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  //Adding Group memebers
  async addUserToGroupChat(groupData) {
    try {
      const data = await this.serverClient.queryChannels({
        type: 'messaging',
        id: { $eq: `${groupData.groupId}` },
      });
      // await channel.addMembers(['thierry', 'josh']);

      const result = await data[0].addMembers(groupData.users);
      console.log(result);
      // await result.create()
      return successResponse(
        null,
        CONSTANT.ADDED_SUCCESSFULLY('Group members'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  // async generateTokenForAll() {
  //   try {
  //     const users = await this.serverClient.queryUsers({}, {}, { limit: 100 });

  //     const userIds = users.users.map((user) => user.id);

  //     const tokens = userIds.map((userId) => {
  //       return {
  //         userId,
  //         token: this.serverClient.createToken(userId),
  //       };
  //     });
  //     return successResponse(tokens, CONSTANT.TOKEN, HttpStatus.OK);
  //   } catch (error) {
  //     throw new HttpException({ message: error.message }, error?.status || 500);
  //   }
  // }
}
