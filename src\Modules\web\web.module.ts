import { <PERSON>du<PERSON> } from '@nestjs/common';
import { WebController } from './controller/web.controller';
import { WebService } from './services/web.service';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { BetaLeadSchema } from 'src/Models/beta_leads.schema';
import { ContactUsSchema } from 'src/Models/contactus.schema';
import { TwillioService } from 'src/common/services/twillio.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'BetaLead', schema: BetaLeadSchema },
      { name: 'ContactUs', schema: ContactUsSchema },
    ]),
    CommonModule,
  ],
  controllers: [WebController],
  providers: [WebService, TwillioService],
})
export class WebModule {}
