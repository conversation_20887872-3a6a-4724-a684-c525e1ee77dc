// MongoDB script to insert profile visibility default settings
// Run this script in your MongoDB database

// Insert Profile Viewing Options
db.defaultsettings.insertOne({
  category: "profileVisibility",
  settingName: "Profile viewing options",
  settingDescription: "Control what profile information is visible",
  settings: [
    {
      "Name": "Your name headline",
      "showNameHeadline": true
    },
    {
      "Name": "Private profile character",
      "showPrivateProfile": false
    }
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Insert Profile Discovery with Contact
db.defaultsettings.insertOne({
  category: "profileVisibility",
  settingName: "Profile discovery with contact",
  settingDescription: "Control who can discover your profile",
  settings: [
    {
      "Name": "Everyone",
      "showEveryone": false
    },
    {
      "Name": "My Connections",
      "showmyConnections": false
    },
    {
      "Name": "People I Follow",
      "showPeopleIFollow": false
    },
    {
      "Name": "No One",
      "showNoOne": true
    }
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

// Insert Show Active Status
db.defaultsettings.insertOne({
  category: "profileVisibility",
  settingName: "Show active status",
  settingDescription: "Control who can see your active status",
  settings: [
    {
      "Name": "Everyone",
      "showEveryone": false
    },
    {
      "Name": "My Connections",
      "showmyConnections": false
    },
    {
      "Name": "People I Follow",
      "showPeopleIFollow": false
    },
    {
      "Name": "No One",
      "showNoOne": true
    }
  ],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

print("Profile visibility default settings inserted successfully!");
print("You can now test the /api/v1/profile-visibility/default endpoint."); 