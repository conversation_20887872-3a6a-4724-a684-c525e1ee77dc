import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type recordDocument = Record & Document;

export const demographicsArr = [
  'Bilingual',
  'Military veteran',
  'Multilingual',
  'Parent / Caregiver',
  'Polyglot',
  'Senior Citizen',
  'Trlingual',
];

export const professionArr = [
  'Agent',
  'Artist',
  'Artisan',
  'Artistic Consultant',
  'Backstage worker',
  'Broadcast professional',
  'Camera crew',
  'Consultant',
  'Choreographer',
  'Craftsperson',
  'Creator',
  'Dance Instructor',
  'Dancer',
  'Decorator',
  'Director',
  'Event Planner',
  'Executive',
  'Filmmaker',
  'Host',
  'Impresario',
  'Influencer',
  'Intern',
  'Interpreter',
  'Manager',
  'Model',
  'Music professional',
  'Musician',
  'Other',
  'Performance Artist',
  'Performer',
  'President',
  'Singer',
  'Singer-Songwriter',
  'Singer/Vocalist',
  'Student',
  'Teacher',
  'Translator',
  'Technician',
  'Visual artist',
  'Voice Actor',
  'Voiceover Artist',
  'Writer',
];

@Schema({ timestamps: true, versionKey: false })
export class Record {
  @Prop({ type: [String], default: null })
  americanUnions: string[];

  @Prop({ type: [String], default: null })
  globalUnions: string[];

  @Prop({ type: [String], default: null })
  americanAffiliateOrganization: string[];

  @Prop({ type: [String], default: null })
  globalAffiliateOrganization: string[];

  @Prop({ type: [String], default: null })
  nonProfitPhilanthropicInstitution: string[];

  @Prop({ type: [String], default: null })
  americanCateringCrafties: string[];

  @Prop({ type: [String], default: null })
  artGalleries: string[];

  @Prop({ type: [String], default: null })
  cateringCraftServicesGlobally: string[];

  @Prop({ type: [String], default: null })
  museums: string[];

  @Prop({ type: [String], default: null })
  talentRepsAgenciesManagementCompanies: string[];

  @Prop({ type: [String], default: null })
  globalTalentRepsAgenciesManagementCompanies: string[];

  @Prop({ type: [String], default: null })
  talentRepsAgenciesManagementCompaniesForSpeakers: string[];

  @Prop({ type: [String], default: null })
  entertainmentLawFirms: string[];

  @Prop({ type: [String], default: null })
  globalComedyClubs: string[];

  @Prop({ type: [String], default: null })
  audioProductionRecordingStudios: string[];

  @Prop({ type: [String], default: null })
  americanComedyClubs: string[];

  @Prop({ type: [String], default: null })
  globalTheaters: string[];

  @Prop({ type: [String], default: null })
  americanTheaters: string[];

  @Prop({ type: [String], default: null })
  casting: string[];

  @Prop({ type: [String], default: null })
  globalRecordingStudios: string[];

  @Prop({ type: [String], default: null })
  lifeCoachAgencies: string[];

  @Prop({ type: [String], default: null })
  animalActorsAgenciesCompanies: string[];

  @Prop({ type: [String], default: null })
  publications: string[];

  @Prop({ type: [String], default: null })
  investmentEntities: string[];

  @Prop({ type: [String], default: null })
  productionAcquisitionDistributionCompanies: string[];

  @Prop({ type: [String], default: null })
  schoolsTrainingInArtsEntertainment: string[];

  @Prop({ type: [String], default: null })
  visualArt: string[];

  @Prop({ type: [String], default: null })
  performingArt: string[];

  @Prop({ type: [String], default: null })
  dance: string[];

  @Prop({ type: [String], default: null })
  acting: string[];

  @Prop({ type: [String], default: null })
  music: string[];

  @Prop({ type: [String], default: null })
  filmMedia: string[];

  @Prop({ type: [String], default: null })
  design: string[];

  @Prop({ type: [String], default: null })
  literaryArt: string[];

  @Prop({ type: [String], default: null })
  crafts: string[];

  @Prop({ type: [String], default: null })
  appliedArt: string[];

  @Prop({ type: [String], default: null })
  other: string[];

  @Prop({ type: [String], default: null })
  feedbackCategories: string[];

  // @Prop({ type: [String], default: null })
  // productionCompanies: string[];

  // @Prop({ type: [String], default: null })
  // profession: string[];

  // @Prop({ type: [String], default: null })
  // degree: string[];
  // @Prop({ type: [String], default: null })
  // passportCountries: string[];
  // @Prop({ type: [String], default: null })
  // currency: string[];

  // @Prop({ type: [String], default: null })
  // interests: string[];
  @Prop({ type: [String], default: null })
  ethnicity: string[];
  @Prop({ type: [String], default: null })
  nationality: string[];
  @Prop({ type: [String], default: null })
  disability: string[];
  @Prop({ type: [String], default: null })
  languagesSpokenSigned: string[];
  @Prop({ type: [String], default: null })
  proficiencyLevel: string[];
  @Prop({ type: [String], default: null })
  selfIother: string[];
  @Prop({ type: [String], default: null })
  age: string[];
  @Prop({ type: [String], default: null })
  selfIgender: string[];
  @Prop({ type: [String], default: null })
  gender: string[];
  @Prop({ type: [String], default: null })
  skills: string[];
  @Prop({ type: [String], default: null })
  fieldOfStudy: string[];
  @Prop({ type: [String], default: null })
  degree: string[];

  @Prop({ type: [String], default: null })
  fanDegree: string[];

  @Prop({ type: [String], default: null })
  industry: string[];
  @Prop({ type: [String], default: null })
  specialization: string[];
  @Prop({ type: [String], default: null })
  companySize: string[];
  @Prop({ type: [String], default: null })
  organizationType: string[];
  @Prop({ type: [String], default: null })
  pronouns: string[];

  @Prop({ type: [String], default: null })
  causes: string[];

  @Prop({ type: [String], default: null })
  professions: string[];

  @Prop({ type: [String], default: null })
  positions: string[];
}
export const RecordSchema = SchemaFactory.createForClass(Record);
