import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { JobTypeEnum } from './jobList.schema';
import { JobPostWorkplaceType } from 'src/common/constant/enum';

export type experienceDocument = Experience & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class Experience {
  @Prop({ type: String })
  title: string;

  @Prop({ type: String })
  companyName: string;

  @Prop({ type: String })
  employmentType: JobTypeEnum;

  @Prop({ type: Boolean })
  isCurrentlyWorking: boolean;

  @Prop({ type: Date })
  startDate: Date;

  @Prop({ type: Date })
  endDate: Date;

  @Prop({ type: String })
  location: string;

  @Prop({ type: String })
  locationType: JobPostWorkplaceType;

  @Prop({ type: String })
  description: string;

  @Prop({ type: [String] })
  skills: string[];

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const ExperienceSchema = SchemaFactory.createForClass(Experience);
