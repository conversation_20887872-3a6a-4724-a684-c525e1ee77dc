import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from './user.schema';
import { Post } from './post.schema';

export type commentDocument = Comment & Document;

@Schema({ _id: false })
export class TaggedUsers {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  user: string;

  @Prop({
    type: String,
    trim: true,
    required: true,
  })
  username: string;
}

@Schema({ timestamps: false, _id: false })
export class ReactionCounts {
  @Prop({ type: Number, default: 0 })
  like: number;

  @Prop({ type: Number, default: 0 })
  love: number;

  @Prop({ type: Number, default: 0 })
  support: number;

  @Prop({ type: Number, default: 0 })
  insightFull: number;

  @Prop({ type: Number, default: 0 })
  curious: number;

  @Prop({ type: Number, default: 0 })
  clap: number;

  @Prop({ type: Number, default: 0 })
  star: number;

  @Prop({ type: Number, default: 0 })
  thumb: number;
}

export enum ReactionEnum {
  LIKE = 'like',
  LOVE = 'love',
  SUPPORT = 'support',
  INSIGHTFULL = 'insightFull',
  CURIOUS = 'curious',
  CLAP = 'clap',
  STAR = 'star',
  THUMB = 'thumb',
}

@Schema({ timestamps: true, _id: false })
export class ReactionSchema {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: string;

  @Prop({
    type: String,
    enum: ReactionEnum,
  })
  type: ReactionEnum;
}

@Schema({ timestamps: true })
export class Comment {
  @Prop({ default: null })
  comment: string;

  @Prop({ default: false })
  isEdited: boolean;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Post' })
  postId: Post;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Comment', default: null })
  parentId: Comment;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: User;

  @Prop({
    type: [TaggedUsers],
  })
  taggedUsers: TaggedUsers[];

  @Prop({ type: Number, default: 0 })
  totalReactions: number;

  @Prop({ type: ReactionCounts, default: { ReactionCounts } })
  reactionCounts: ReactionCounts;

  @Prop({
    type: [ReactionSchema],
  })
  reactions: [ReactionSchema];
}

export const CommentSchema = SchemaFactory.createForClass(Comment);
