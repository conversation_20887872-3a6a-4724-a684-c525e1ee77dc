import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { validFileToUpload } from 'src/Custom/helpers/upload';
import { getRecordsDto } from 'src/Modules/admin/dtos/record.dtos';
import { AuthGuard } from 'src/Modules/auth/auth.guard';
import CONSTANT from '../../../common/constant/common.constant';
import { groupChatDto, leaveChannelDto } from '../dtos/streamChat.dto';
import {
  affliatePagesListDto,
  changePasswordDto,
  connectionRequestDto,
  countryCityStateDto,
  editProfileDto,
  emailDto,
  feedbackDto,
  fileUploadDto,
  followUnfollowDto,
  connectionRequestAcceptRejectDto,
  genderDto,
  getFollowersFollowingDto,
  GetNotificationsDto,
  GetUserListing,
  jobDto,
  privacySettingDto,
  profileDto,
  profileViewDto,
  publicationDto,
  resetPasswordDto,
  searchDto,
  selfIdentifyDto,
  signInDto,
  SignupData,
  signUpDto,
  socialMediaDto,
  userNameDto,
  verifyOtpDto,
  visibilitySettingDto,
  socialActivitiesDto,
  fieldOfStudyDto,
  offeredServicesDto,
  causesDto,
  clientRequestDto,
  clientRequestAcceptRejectDto,
  UpdateSelfIdentifyLanguagesDTO,
  professionDto,
  GetUserSelectionListingDto,
  memberVerificationDto,
  subscribeDto,
  hirerVerificationDTO,
  AcceptRejectfollowDto,
} from '../dtos/user.dtos';
import { StreamChatService } from '../services/streamChat.service';
import { UserService } from '../services/user.service';
import { ProjectService } from '../services/project.service';
import { projectDto } from 'src/Modules/user/dtos/project.dto';
import { CareerService } from 'src/Modules/user/services/career.service';
import {
  educationDto,
  volunteerExperienceDto,
  HonorAndRewardDto,
  licenseCertificationDto,
  experienceDto,
} from 'src/Modules/user/dtos/career.dto';
import { JobPostService } from '../services/job.service';
import {
  JobApplicationDto,
  JobApplicationStatusDto,
  JobPostDto,
} from '../dtos/jobPost.dto';
import { EventDto } from '../dtos/event.dto';
import { EventService } from '../services/event.service';
import { FeaturedService } from '../services/featured.service';
import { featuredDto } from '../dtos/featured.dto';
import { FileSizeValidationPipe } from 'src/Custom/pipes/file-validator.pipe';
import { Response } from 'express';

@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly streamChatService: StreamChatService,
    private readonly projectService: ProjectService,

    private readonly careerService: CareerService,

    private readonly jobPostService: JobPostService,

    private readonly eventService: EventService,
    private readonly featuredService: FeaturedService,
  ) {}

  @Post('signup')
  async signup(@Body() signupData: signUpDto) {
    return this.userService.signUp(signupData);
  }

  @Post('signin')
  @HttpCode(HttpStatus.OK)
  async signin(@Body() signinData: signInDto) {
    return this.userService.signIn(signinData);
  }

  @Post('fake-account')
  @HttpCode(HttpStatus.OK)
  async createFakeAccounts() {
    return this.userService.createFakeAccounts();
  }

  @Post('username')
  @HttpCode(HttpStatus.OK)
  async usernameAvailability(@Body() userNameData: userNameDto) {
    return this.userService.userNameAvailability(userNameData);
  }

  @Post('send-otp')
  async sendotp(@Body() sendOtpData: emailDto) {
    return this.userService.sendOtp(sendOtpData);
  }

  @Post('verify-otp')
  async verifyotp(@Body() verifyOtpData: verifyOtpDto) {
    return this.userService.verifyOtp(verifyOtpData);
  }

  @Post('reset-password')
  async sendresetpasswordmail(@Body() resetPasswordData: resetPasswordDto) {
    return this.userService.resetPassword(resetPasswordData);
  }

  @Get('country-city-state')
  async getCountryIsoDialCode(
    @Body() countryCityStateData: countryCityStateDto,
  ) {
    return this.userService.countryStateCity(countryCityStateData);
  }

  @Post('signupdata')
  @HttpCode(HttpStatus.OK)
  async getSignupData(@Body() body: SignupData) {
    return this.userService.getSignupData(body);
  }

  @UseGuards(AuthGuard)
  @Get('account/:id')
  async getoneuser(@Req() req: Request, @Param('id') id: string) {
    return this.userService.getOneUser(req, id);
  }

  // @UseGuards(AuthGuard)
  @Post('records')
  @HttpCode(HttpStatus.OK)
  async getprofession(@Body() recordsData: getRecordsDto) {
    return this.userService.getRecords(recordsData);
  }

  @UseGuards(AuthGuard)
  @Patch('privacy')
  async privacysetting(
    @Req() req: Request,
    @Body() privacySettingData: privacySettingDto,
  ) {
    return this.userService.privacySetting(req, privacySettingData);
  }

  @UseGuards(AuthGuard)
  @Patch('visibility')
  async visibilitysetting(
    @Req() req: Request,
    @Body() visibilitySettingData: visibilitySettingDto,
  ) {
    return this.userService.visibilitySetting(req, visibilitySettingData);
  }

  @UseGuards(AuthGuard)
  @Patch('social-media')
  async socialmediasetting(
    @Req() req: Request,
    @Body() socialMediaData: socialMediaDto,
  ) {
    return this.userService.socialMediaSetting(req, socialMediaData);
  }

  @UseGuards(AuthGuard)
  @Patch('gender')
  async gendersetting(@Req() req: Request, @Body() genderData: genderDto) {
    return this.userService.genderSetting(req, genderData);
  }

  @UseGuards(AuthGuard)
  @UseInterceptors(FilesInterceptor('file'))
  @Post('file-upload')
  async fileupload(
    @UploadedFiles(new FileSizeValidationPipe())
    file: Array<Express.Multer.File>,
    @Req() req: Request,
    @Body() fileUploadData: fileUploadDto,
  ) {
    return await this.uploadFile(file, req, fileUploadData);
  }

  @UseInterceptors(FilesInterceptor('file'))
  @Post('upload-logo')
  async uploadLogo(
    @UploadedFiles(new FileSizeValidationPipe())
    file: Array<Express.Multer.File>,
    @Req() req: Request,
    @Body() fileUploadData: fileUploadDto,
  ) {
    if (fileUploadData.uploadType !== 'LOGO') {
      throw new HttpException(
        { message: 'Invalid upload type' },
        HttpStatus.BAD_REQUEST,
      );
    }
    return await this.uploadFile(file, req, fileUploadData);
  }

  async uploadFile(
    file: Array<Express.Multer.File>,
    req: Request,
    fileUploadData,
  ) {
    const uploadConfig = {
      PROFILE: { maxFiles: 1, allowedTypes: /\.(png|jpg|jpeg|gif)$/ },
      STORY: {
        maxFiles: 1,
        allowedTypes: /\.(png|jpg|jpeg|gif|mp4|mkv|mpeg|mov|webm)$/,
      },
      POST: {
        maxFiles: 5,
        allowedTypes: /\.(png|jpg|jpeg|gif|mp4|mkv|mpeg|mov|webm)$/,
      },
      GROUP_POST: {
        maxFiles: 5,
        allowedTypes: /\.(png|jpg|jpeg|gif|mp4|mkv|mpeg|mov|webm)$/,
      },
      PORTFOLIO: {
        maxFiles: 6,
        allowedTypes: /\.(png|jpg|jpeg|gif|mp4|mkv|mpeg|mov|webm)$/,
      },
      MEMBERID: { maxFiles: 1, allowedTypes: /\.(png|jpg|jpeg|gif)$/ },
      MEMBERDOC: { maxFiles: 1, allowedTypes: /\.(png|jpg|jpeg|gif)$/ },
      GROUP_COVER: { maxFiles: 1, allowedTypes: /\.(png|jpg|jpeg|gif)$/ },
      LOGO: { maxFiles: 1, allowedTypes: /\.(png|jpg|jpeg|gif)$/ },
      PROFILE_PROJECT: {
        maxFiles: 5,
        allowedTypes: /\.(png|jpg|jpeg|gif|mp4|mkv|mpeg|mov|webm)$/,
      },
      HONOR_AND_AWARD: {
        maxFiles: 1,
        allowedTypes: /\.(png|jpg|jpeg)$/,
      },
      JOB_RESUME: {
        maxFiles: 1,
        allowedTypes: /\.(png|jpg|jpeg|pdf|doc|docx|txt)$/,
      },
      WEB: {
        maxFiles: 1,
        allowedTypes: /\.(png|jpg|jpeg|gif|mp4|mkv|mpeg|mov|webm)$/,
      },
    };

    const config = uploadConfig[fileUploadData.uploadType];

    if (config === undefined) {
      // Handle unsupported upload types
      throw new HttpException(
        { message: 'Invalid upload type' },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (file.length > config.maxFiles) {
      throw new HttpException(
        { message: `Maximum of ${config.maxFiles} files allowed for upload` },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!file[0].originalname.toLowerCase().match(config.allowedTypes)) {
      throw new HttpException(
        {
          message:
            fileUploadData.uploadType === 'PROFILE'
              ? CONSTANT.IMAGE_TYPE_ERROR
              : CONSTANT.FILE_TYPE_ERROR,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Call the middleware before reaching your service logic
    await new Promise<void>((resolve, reject) => {
      validFileToUpload(req, null, (err) => {
        if (err) {
          return reject(err);
        }
        resolve();
      });
    });

    return this.userService.fileUpload(file, req, fileUploadData);
  }

  // Get Profile for loggedin user
  @UseGuards(AuthGuard)
  @Get('profile')
  async getProfile(@Req() req: Request) {
    return this.userService.getProfile(req);
  }

  @UseGuards(AuthGuard)
  @Patch('profile')
  async profilesetting(@Req() req: Request, @Body() profileData: profileDto) {
    return this.userService.profileSetting(req, profileData);
  }

  @UseGuards(AuthGuard)
  @Patch('profile/publications')
  async addOrUpdatePublication(
    @Req() req: Request,
    @Body() publicationData: publicationDto,
  ) {
    return this.userService.addOrUpdatePublication(req, publicationData);
  }

  @UseGuards(AuthGuard)
  @Patch('profile/professions')
  async addOrUpdateProfessions(
    @Req() req: Request,
    @Body() data: professionDto,
  ) {
    return this.userService.addOrUpdateProfessions(req, data);
  }

  @UseGuards(AuthGuard)
  @Get('profile/publications/:userId')
  async getAllPublications(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getAllPublications(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('profile/professions/:userId')
  async getAllProfessions(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getAllProfessions(req, userId);
  }

  @UseGuards(AuthGuard)
  @Patch('profile/affiliate-pages')
  async addOrUpdateAffiliatePages(
    @Req() req: Request,
    @Body() pagesData: affliatePagesListDto,
  ) {
    return this.userService.addOrUpdateAffiliatePages(req, pagesData);
  }

  @UseGuards(AuthGuard)
  @Patch('profile/affiliate-pages/verify/:id')
  async verifyAffiliatePage(@Req() req: Request, @Param('id') id: string) {
    return this.userService.verifyAffiliatePage(req, id);
  }

  @UseGuards(AuthGuard)
  @Post('profile/social-activity')
  async addOrUpdateSocialActivities(
    @Req() req: Request,
    @Body() pagesData: socialActivitiesDto,
  ) {
    return this.userService.addOrUpdateSocialActivities(req, pagesData);
  }

  @UseGuards(AuthGuard)
  @Post('profile/field-of-study')
  async addOrUpdatefieldOfStudy(
    @Req() req: Request,
    @Body() pagesData: fieldOfStudyDto,
  ) {
    return this.userService.addOrUpdatefieldOfStudy(req, pagesData);
  }

  @UseGuards(AuthGuard)
  @Post('profile/offered-services')
  async addOrUpdateOfferedServices(
    @Req() req: Request,
    @Body() pagesData: offeredServicesDto,
  ) {
    return this.userService.addOrUpdateOfferedServices(req, pagesData);
  }

  @UseGuards(AuthGuard)
  @Post('profile/causes')
  async addOrUpdateCauses(@Req() req: Request, @Body() pagesData: causesDto) {
    return this.userService.addOrUpdateCauses(req, pagesData);
  }

  // remove affiliate pages
  @UseGuards(AuthGuard)
  @Delete('profile/affiliate-pages/:name')
  async removeAffiliatePage(@Req() req: Request, @Param('name') name: string) {
    return this.userService.removeAffiliatePage(req, name);
  }

  @UseGuards(AuthGuard)
  @Delete('profile/social-activity/:name')
  async removeSocialActivity(@Req() req: Request, @Param('name') name: string) {
    return this.userService.removeSocialActivity(req, name);
  }

  @UseGuards(AuthGuard)
  @Delete('profile/field-of-study/:name')
  async removeFieldOfStudy(@Req() req: Request, @Param('name') name: string) {
    return this.userService.removeFieldOfStudy(req, name);
  }

  @UseGuards(AuthGuard)
  @Delete('profile/offered-services/:name')
  async removeOfferedService(@Req() req: Request, @Param('name') name: string) {
    return this.userService.removeOfferedService(req, name);
  }

  @UseGuards(AuthGuard)
  @Delete('profile/causes/:name')
  async removeCauses(@Req() req: Request, @Param('name') name: string) {
    return this.userService.removeCause(req, name);
  }

  @UseGuards(AuthGuard)
  @Delete('profile/professions/:name')
  async removeProfessions(@Req() req: Request, @Param('name') name: string) {
    return this.userService.removeProfessions(req, name);
  }

  @UseGuards(AuthGuard)
  @Get('profile/affiliate-pages/:userId')
  async getAllAffiliatePages(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getAllAffiliatePages(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('profile/social-activity/:userId')
  async getAllSocialActivities(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getAllSocialActivities(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('profile/field-of-study/:userId')
  async getAllFieldOfStudy(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getAllFieldOfStudy(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('profile/offered-services/:userId')
  async getAllOfferedServices(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getAllOfferedServices(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('profile/causes/:userId')
  async getAllCauses(@Req() req: Request, @Param('userId') userId: string) {
    return this.userService.getAllCauses(req, userId);
  }

  @UseGuards(AuthGuard)
  @Delete('profile/publications/:id')
  async removePublication(
    @Req() req: Request,
    @Param('id') publicationId: string,
  ) {
    return this.userService.removePublication(req, publicationId);
  }

  @UseGuards(AuthGuard)
  @Patch('profile/analytics/visibility')
  async updateAnalyticsVisibility(
    @Req() req: Request,
    @Body() body: { visibility: 'public' | 'private' },
  ) {
    return this.userService.updateAnalyticsVisibility(req, body);
  }

  @UseGuards(AuthGuard)
  @Patch('self-identify')
  async selfidentifysetting(
    @Req() req: Request,
    @Body() selfIdentifyData: selfIdentifyDto,
  ) {
    return this.userService.selfIdentifySetting(req, selfIdentifyData);
  }

  @UseGuards(AuthGuard)
  @Get('self-identify/languages/:userId')
  async getSelfIdentifyLanguagesOfUser(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.userService.getSelfIdentifyLanguagesOfUser(req, userId);
  }

  // update languages
  @UseGuards(AuthGuard)
  @Patch('self-identify/languages')
  async updateSelfIdentifyLanguages(
    @Req() req: Request,
    @Body() selfIdentifyData: UpdateSelfIdentifyLanguagesDTO,
  ) {
    return this.userService.updateSelfIdentifyLanguages(req, selfIdentifyData);
  }

  @UseGuards(AuthGuard)
  @Get('self-identify')
  async getSelfIdentifySettingOfUser(@Req() req: Request) {
    return this.userService.getSelfIdentifySettingOfUser(req);
  }

  @UseGuards(AuthGuard)
  @Get('home')
  async homedata(@Req() req: Request, @Query() queryParam) {
    return this.userService.homeData(req, queryParam);
  }

  @UseGuards(AuthGuard)
  @Patch('skip')
  async skipscreen() {
    return this.userService.skipScreen();
  }

  @UseGuards(AuthGuard)
  @Patch('edit-profile')
  async editprofile(
    @Req() req: Request,
    @Body() editProfileData: editProfileDto,
  ) {
    return this.userService.editProfile(req, editProfileData);
  }

  @UseGuards(AuthGuard)
  @Post('follow-unfollow')
  async followunfollow(
    @Req() req: Request,
    @Body() followUnfollowData: followUnfollowDto,
  ) {
    return this.userService.followUnfollow(req, followUnfollowData);
  }

  // accept reject follow request
  @UseGuards(AuthGuard)
  @Post('follow/accept-reject')
  async acceptRejectFollowRequest(
    @Req() req: Request,
    @Body() followUnfollowData: AcceptRejectfollowDto,
  ) {
    return this.userService.acceptRejectFollowRequest(req, followUnfollowData);
  }

  @UseGuards(AuthGuard)
  @Post('connection-request')
  async sendConnectionrequest(
    @Req() req: Request,
    @Body() data: connectionRequestDto,
  ) {
    return this.userService.sentConnectionRequest(req, data);
  }

  @UseGuards(AuthGuard)
  @Get('connection/requests')
  async getConnectionRequests(@Req() req: Request) {
    return this.userService.getConnectionRequests(req);
  }

  @UseGuards(AuthGuard)
  @Get('connections/:id')
  async getConnectionslist(@Req() req: Request, @Param('id') userId: number) {
    return this.userService.getConnectionslist(req, userId);
  }

  @UseGuards(AuthGuard)
  @Post('connection/accept-reject')
  async connectionrequestacceptreject(
    @Req() req: Request,
    @Body() data: connectionRequestAcceptRejectDto,
  ) {
    return this.userService.connectionRequestAcceptReject(req, data);
  }

  @UseGuards(AuthGuard)
  @Get('followers-followings/:id')
  async getfollowerfollowing(
    @Req() req: Request,
    @Param('id') userId: string,
    @Query() followersFollowingData: getFollowersFollowingDto,
  ) {
    followersFollowingData.userId = userId;
    return this.userService.getFollowersFollowingList(
      req,
      followersFollowingData,
    );
  }

  @UseGuards(AuthGuard)
  @Post('logout')
  async logout(@Req() req: Request) {
    return this.userService.logout(req);
  }

  @UseGuards(AuthGuard)
  @Post('change-password')
  async changepassword(
    @Req() req: Request,
    @Body() changePasswordData: changePasswordDto,
  ) {
    return this.userService.changePassword(req, changePasswordData);
  }

  @UseGuards(AuthGuard)
  @Post('feedback')
  async feedback(@Req() req: Request, @Body() feedbackData: feedbackDto) {
    return this.userService.feedback(req, feedbackData);
  }

  @UseGuards(AuthGuard)
  @Post('job')
  async job(@Req() req: Request, @Body() jobData: jobDto) {
    return this.userService.addJobPost(req, jobData);
  }

  @UseGuards(AuthGuard)
  @Post('hashtag/search')
  async hashtagsearch(@Req() req: Request, @Body() searchData: searchDto) {
    return this.userService.hashtagSearch(req, searchData);
  }

  /***
   * For stream chat
   */
  @UseGuards(AuthGuard)
  @Get('chat-token')
  async getchattoken(@Req() req: Request) {
    return this.streamChatService.getChatToken(req);
  }

  @UseGuards(AuthGuard)
  @Post('single-chat/:userid')
  async createsinglechat(@Req() req: Request, @Param('userid') userid: number) {
    return this.streamChatService.createChannel(req, userid);
  }

  @UseGuards(AuthGuard)
  @Post('delete-chat/:cid')
  async deletesinglechat(@Req() req: Request, @Param('cid') channelId: number) {
    return this.streamChatService.deleteChannel(channelId);
  }

  @UseGuards(AuthGuard)
  @Post('group-chat')
  async creategroupchat(
    @Req() req: Request,
    @Body() groupChatData: groupChatDto,
  ) {
    return this.streamChatService.createGroupChannel(req, groupChatData);
  }

  @UseGuards(AuthGuard)
  @Post('leave-group-chat')
  async leavegroupchat(
    @Req() req: Request,
    @Body() leaveChannelData: leaveChannelDto,
  ) {
    return this.streamChatService.leaveGroupChannel(req, leaveChannelData);
  }

  @UseGuards(AuthGuard)
  @Post('profile/view')
  async profileView(@Req() req: Request, @Body() viewData: profileViewDto) {
    return this.userService.profileView(req, viewData);
  }

  @UseGuards(AuthGuard)
  @Get('user-signup-data')
  async getUserSignupData(@Req() req: Request) {
    return this.userService.getUserSignupData(req);
  }

  // Get Users Notifications
  @UseGuards(AuthGuard)
  @Get('notifications')
  @HttpCode(HttpStatus.OK)
  getUsersNotifications(
    @Req() req: Request,
    @Query() queryParam: GetNotificationsDto,
  ) {
    return this.userService.getUsersNotifications(req, queryParam);
  }

  // Mark Notifications as read
  @UseGuards(AuthGuard)
  @Get('notifications/mark-read')
  @HttpCode(HttpStatus.OK)
  markNotificationsAsRead(@Req() req: Request) {
    return this.userService.markNotificationAsRead(req);
  }

  // Get user listing
  @UseGuards(AuthGuard)
  @Get('list/search')
  @HttpCode(HttpStatus.OK)
  async getUserList(@Req() req: Request, @Query() queryParam: GetUserListing) {
    return this.userService.getUserListing(queryParam);
  }

  //Project
  @UseGuards(AuthGuard)
  @Post('/project')
  async addProject(@Req() req: Request, @Body() viewData: projectDto) {
    return this.projectService.addProject(req, viewData);
  }

  // delete Project
  @UseGuards(AuthGuard)
  @Delete('/project/:id')
  async deleteProject(@Param('id') id: string) {
    return this.projectService.deleteProject(id);
  }

  //Education
  @UseGuards(AuthGuard)
  @Post('/education')
  async addEditEducation(@Req() req: Request, @Body() viewData: educationDto) {
    return this.careerService.addEditEducation(req, viewData);
  }
  //Project
  @UseGuards(AuthGuard)
  @Post('/license_certification')
  async addEditLicense(
    @Req() req: Request,
    @Body() viewData: licenseCertificationDto,
  ) {
    return this.careerService.addEditLicenseCertification(req, viewData);
  }

  @UseGuards(AuthGuard)
  @Get('/license_certification/:userId')
  async getAllLicenseCertification(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.careerService.getAllLicenseCertification(req, userId);
  }

  @UseGuards(AuthGuard)
  @Post('/honor_and_awards')
  async addEditHonorAndAward(
    @Req() req: Request,
    @Body() viewData: HonorAndRewardDto,
  ) {
    return this.careerService.addEditHonorAndAward(req, viewData);
  }

  @UseGuards(AuthGuard)
  @Get('/honor_and_awards/:userId')
  async getAllHonorAndAward(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.careerService.getAllHonorAndAward(req, userId);
  }

  @UseGuards(AuthGuard)
  @Delete('/honor_and_awards/:id')
  async removeHonorAndAward(@Param('id') id: string) {
    return this.careerService.removeHonorAndAward(id);
  }

  @UseGuards(AuthGuard)
  @Get('/projects/:userId')
  async getProjects(@Param('userId') userId: string, @Req() req: Request) {
    return this.projectService.getProjects(userId, req);
  }

  //Project
  @UseGuards(AuthGuard)
  @Post('/volunteer-experience')
  async addEditVolunteerExperience(
    @Req() req: Request,
    @Body() viewData: volunteerExperienceDto,
  ) {
    return this.careerService.addEditVolunteerExperience(req, viewData);
  }

  //Project
  @UseGuards(AuthGuard)
  @Post('/experience')
  async addEditExperience(
    @Req() req: Request,
    @Body() viewData: experienceDto,
  ) {
    return this.careerService.addEditExperience(req, viewData);
  }

  @UseGuards(AuthGuard)
  @Get('/experience/:userId')
  async getAllExperiences(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.careerService.getAllExperiences(req, userId);
  }

  @UseGuards(AuthGuard)
  @Delete('/experience/:id')
  async removeExperience(@Req() req: Request, @Param('id') id: string) {
    return this.careerService.removeExperience(req, id);
  }

  @UseGuards(AuthGuard)
  @Delete('/volunteer-experience/:id')
  async removeVolunteerExperience(
    @Req() req: Request,
    @Param('id') id: string,
  ) {
    return this.careerService.removeVolunteerExperience(req, id);
  }

  @UseGuards(AuthGuard)
  @Get('/volunteer-experience/:userId')
  async getAllVolunteerExperiences(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    return this.careerService.getAllVolunteerExperiences(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('/education/:userId')
  async getAllEducations(@Req() req: Request, @Param('userId') userId: string) {
    return this.careerService.getAllEducations(req, userId);
  }

  @UseGuards(AuthGuard)
  @Delete('/education/:id')
  async removeEducation(@Req() req: Request, @Param('id') id: string) {
    return this.careerService.removeEducation(req, id);
  }

  @UseGuards(AuthGuard)
  @Delete('/license_certification/:id')
  async removeLicenseCertification(
    @Req() req: Request,
    @Param('id') id: string,
  ) {
    return this.careerService.removeLicenseCertification(req, id);
  }

  @UseGuards(AuthGuard)
  @Post('/job-post')
  async addJobPost(@Req() req: Request, @Body() viewData: JobPostDto) {
    return this.jobPostService.addEditJobPost(req, viewData);
  }

  @UseGuards(AuthGuard)
  @Delete('/job-post/:id')
  async removeJobPost(@Req() req: Request, @Param('id') id: string) {
    return this.jobPostService.removeJobPost(req, id);
  }

  @UseGuards(AuthGuard)
  @Get('/job-post/:userId')
  async getAllJobPosts(@Req() req: Request, @Param('userId') userid: string) {
    return this.jobPostService.getAllJobPost(req, userid);
  }

  @UseGuards(AuthGuard)
  @Post('/job-application')
  async applyJob(@Req() req: Request, @Body() payload: JobApplicationDto) {
    return this.jobPostService.applyForJob(req, payload);
  }

  @UseGuards(AuthGuard)
  @Patch('/job-application/status')
  async updateJobApplicationStatus(
    @Req() req: Request,
    @Body() payload: JobApplicationStatusDto,
  ) {
    return this.jobPostService.updateJobApplicationStatus(req, payload);
  }

  @UseGuards(AuthGuard)
  @Get('/job-application/:jobId')
  async getJobApplications(@Req() req: Request, @Param('jobId') jobId: string) {
    return this.jobPostService.getAllJobApplications(req, jobId);
  }

  @UseGuards(AuthGuard)
  @Post('/event')
  async addEvent(@Req() req: Request, @Body() viewData: EventDto) {
    return this.eventService.addEvent(req, viewData);
  }

  @UseGuards(AuthGuard)
  @Get('/event/:userId')
  async getEvents(@Param('userId') userId: string, @Req() req: Request) {
    return this.eventService.getEvents(userId, req);
  }

  @UseGuards(AuthGuard)
  @Delete('/event/:id')
  async deleteEvent(@Param('id') id: string) {
    return this.eventService.deleteEvent(id);
  }

  @UseGuards(AuthGuard)
  @Post('/event/:id/participate')
  async participateEvent(
    @Param('id') id: string,
    @Req() req: Request,
    @Body() body: { action: 'join' | 'leave' },
  ) {
    return this.eventService.participateEvent(req, id, body.action);
  }

  @UseGuards(AuthGuard)
  @Post('/feature')
  async addFeature(@Req() req: Request, @Body() body: featuredDto) {
    return this.featuredService.addFeature(req, body);
  }

  @UseGuards(AuthGuard)
  @Delete('/feature/:id')
  async deleteFeature(@Param('id') id: string) {
    return this.featuredService.deleteFeature(id);
  }

  @UseGuards(AuthGuard)
  @Get('/feature/:userId')
  async getFeatures(@Param('userId') userId: string, @Req() req: Request) {
    return this.featuredService.getFeatures(userId, req);
  }

  @UseGuards(AuthGuard)
  @Post('/client')
  async addClient(@Req() req: Request, @Body() body: clientRequestDto) {
    return this.userService.sendClientrequest(req, body);
  }

  @UseGuards(AuthGuard)
  @Get('/client/:userId')
  async getClients(@Req() req: Request, @Param('userId') userId: string) {
    return this.userService.getClients(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('/people/:userId')
  async getPeoples(@Req() req: Request, @Param('userId') userId: string) {
    return this.userService.getPeoples(req, userId);
  }

  @UseGuards(AuthGuard)
  @Post('client/accept-reject')
  async clientRequestAcceptReject(
    @Req() req: Request,
    @Body() data: clientRequestAcceptRejectDto,
  ) {
    return this.userService.clientRequestAcceptReject(req, data);
  }

  @UseGuards(AuthGuard)
  @Post('member/accept-reject')
  async memberRequestAcceptReject(
    @Req() req: Request,
    @Body() data: clientRequestAcceptRejectDto,
  ) {
    return this.userService.memberRequestAcceptReject(req, data);
  }

  @UseGuards(AuthGuard)
  @Post('/client/show-toggle')
  async showClientToggleStatus(@Req() req: Request) {
    return this.userService.showClientToggleStatus(req);
  }

  @Post('/unionAffiliateOrganizationBusinessSchoolsTrainingFacility/list')
  async unionAffiliateOrganizationBusinessSchoolsTrainingFacilityList(
    @Req() req: Request,
    @Body() queryParam: GetUserSelectionListingDto,
  ) {
    return this.userService.unionAffiliateOrganizationBusinessSchoolsTrainingFacilityList(
      req,
      queryParam,
    );
  }

  @UseGuards(AuthGuard)
  @Post('/organization-selection/list')
  async organizationSelectionLIst(
    @Req() req: Request,
    @Body() queryParam: GetUserSelectionListingDto,
  ) {
    return this.userService.unionAffiliateOrganizationBusinessSchoolsTrainingFacilityList(
      req,
      queryParam,
    );
  }

  @UseGuards(AuthGuard)
  @Post('/member-verification')
  async sendMemberVerificationRequest(@Body() data: memberVerificationDto) {
    return this.userService.sendMemberVerificationRequest(data);
  }

  @UseGuards(AuthGuard)
  @Post('subscribe-unsubscribe')
  async subscribeUnsubscribe(@Req() req: Request, @Body() data: subscribeDto) {
    return this.userService.subscribeUser(req, data);
  }

  @UseGuards(AuthGuard)
  @Post('create-message-channel/:userId')
  async createMessageChannel(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    const response = await this.streamChatService.sendMessageCreateChannel(
      req,
      userId,
    );

    return response;
  }

  @UseGuards(AuthGuard)
  @UseInterceptors(FilesInterceptor('attachments'))
  @Post('hirer-verification')
  async sentHirerVerificationEmail(
    @UploadedFiles(new FileSizeValidationPipe())
    file: Array<Express.Multer.File>,
    @Req() req: Request,
    @Body() data: hirerVerificationDTO,
  ) {
    return this.userService.sentHirerVerificationEmail(req, data, file);
  }

  @Post('hirer-verification/action/:token')
  async handleHirerVerificationAction(
    @Req() req: Request,
    @Res() res: Response,
  ) {
    const message = await this.userService.handleHirerVerificationAction(req);

    return res.render('hirer_verification_response', { message });
  }
}
