import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import {
  JobPostWorkplaceType,
  JobPostWorkType,
} from 'src/common/constant/enum';

@Schema({ timestamps: true, versionKey: false })
export class JobPost {
  @Prop({ type: String })
  title: string;

  @Prop({ type: String })
  company: string;

  @Prop({ type: String })
  description: string;

  @Prop({
    enum: JobPostWorkplaceType,
  })
  workplaceType: string;

  @Prop({ type: String })
  location: string;

  @Prop({ enum: JobPostWorkType })
  workType: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;

  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'JobApplication' }],
  })
  applications: mongoose.Types.ObjectId[];
}

export type JobPostDocument = JobPost & Document;

export const JobPostSchema = SchemaFactory.createForClass(JobPost);
