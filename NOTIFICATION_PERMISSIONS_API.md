# Unified Notification Permissions API

## Overview

This document describes the unified notification permissions API that consolidates user permissions and default permissions into a single, efficient endpoint to handle 100+ notification items per user type.

## Problem Solved

### Previous Issues:
1. **Two API calls required**: Frontend needed to call both `/user` and `/default` endpoints
2. **Incomplete data**: User endpoint only returned enabled permissions
3. **Hardcoded frontend**: Frontend had to manage merging user preferences with defaults
4. **First-time user confusion**: No clear way to show all available permissions for new users

### Solution:
- **Single unified endpoint**: `/api/v1/notification-permissions`
- **Complete data**: Returns all available permissions with current state
- **Smart defaults**: New users get all permissions disabled by default
- **No frontend hardcoding**: All logic handled on backend

## API Endpoints

### 1. Unified Notification Permissions (Primary)
```
GET /api/v1/notification-permissions?userType=creator_member
```

**Purpose**: Get all available notification permissions for the specified user type with their current state.

**Authentication**: Required (AuthGuard)

**Query Parameters**:
- `userType` (required): The user type to get permissions for (e.g., `creator_member`, `audience_member_fan`, `hirer_employer`, etc.)

**Response**:
```json
{
  "success": true,
  "message": "Notification permissions fetched successfully",
  "data": {
    "userId": "507f1f77bcf86cd799439011",
    "userType": "creator_member",
    "totalPermissions": 13,
    "permissions": [
      {
        "permissionId": "507f1f77bcf86cd799439012",
        "permissionName": "New messages in my inbox",
        "userType": "creator_member",
        "isEnabled": true,
        "isActive": true
      },
      {
        "permissionId": "507f1f77bcf86cd799439013",
        "permissionName": "Someone follows me",
        "userType": "creator_member",
        "isEnabled": false,
        "isActive": true
      }
      // ... all available permissions for user type
    ]
  }
}
```

**Key Features**:
- Returns ALL available permissions for user's type (not just enabled ones)
- Shows current state (enabled/disabled) for each permission
- New users get all permissions disabled by default
- Includes metadata like total count and user type

### 2. Save Notification Permissions
```
POST /api/v1/notification-permissions/save?userType=creator_member
```

**Purpose**: Save user's notification permission preferences.

**Authentication**: Required (AuthGuard)

**Query Parameters**:
- `userType` (required): The user type for the permissions being saved

**Request Body**:
```json
{
  "permissions": [
    {
      "permissionId": "507f1f77bcf86cd799439012",
      "isEnabled": true
    },
    {
      "permissionId": "507f1f77bcf86cd799439013",
      "isEnabled": false
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Notification permissions updated successfully",
  "data": {
    // Updated user settings object
  }
}
```

### 3. Legacy Endpoints (Deprecated but maintained)

#### Get User Permissions (Legacy)
```
GET /api/v1/notification-permissions/user
```
Returns only enabled permissions (legacy behavior).

#### Get Default Permissions (Legacy)
```
GET /api/v1/notification-permissions/default?userType=creator_member
```
Returns all default permissions for a user type (legacy behavior).

## Frontend Implementation Guide

### 1. First Time User Experience
```javascript
// When user opens notification settings for the first time
const userType = 'creator_member'; // Get this from user's profile or selection
const response = await fetch(`/api/v1/notification-permissions?userType=${userType}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});

const { data } = await response.json();

// data.permissions contains ALL available permissions with current state
// For new users, all permissions will have isEnabled: false
```

### 2. Display All Permissions
```javascript
// Show all permissions with toggle switches
data.permissions.forEach(permission => {
  console.log(`${permission.permissionName}: ${permission.isEnabled ? 'ON' : 'OFF'}`);
});
```

### 3. Save User Preferences
```javascript
// Collect all permission states and save
const permissionsToSave = data.permissions.map(permission => ({
  permissionId: permission.permissionId,
  isEnabled: permission.isEnabled // Updated by user via UI
}));

await fetch(`/api/v1/notification-permissions/save?userType=${userType}`, {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ permissions: permissionsToSave })
});
```

## Database Schema

### User Settings Collection
```javascript
{
  _id: ObjectId,
  userId: ObjectId,
  notificationPermissions: {
    permissions: [
      {
        permissionId: ObjectId, // Reference to defaultSettings
        isEnabled: Boolean
      }
    ]
  }
}
```

### Default Settings Collection
```javascript
{
  _id: ObjectId,
  category: "notificationPermissions",
  userType: "creator_member",
  permissionName: "New messages in my inbox",
  isActive: Boolean
}
```

## User Types and Permission Counts

Based on the seeder data:
- **audience_member_fan**: 5 permissions
- **creator_member**: 13 permissions  
- **hirer_employer**: 6 permissions
- **affiliate_organization**: 6 permissions
- **business_school**: 6 permissions
- **training_facility**: 6 permissions
- **union**: 6 permissions

## Migration Strategy

### For Existing Users:
1. Existing user settings are preserved
2. New unified endpoint returns their current preferences
3. Missing permissions are added with `isEnabled: false`

### For New Users:
1. All available permissions for their user type are returned
2. All permissions default to `isEnabled: false`
3. User can enable/disable as needed

## Benefits

1. **Single API Call**: Frontend only needs one call to get complete data
2. **No Hardcoding**: All permission logic handled on backend
3. **Scalable**: Handles 100+ permissions efficiently
4. **User-Friendly**: Clear first-time experience
5. **Backward Compatible**: Legacy endpoints still work
6. **Type Safe**: Proper validation and DTOs

## Error Handling

- **Invalid Permission IDs**: Returns 400 with validation error
- **Wrong User Type**: Permissions must belong to user's type
- **Inactive Permissions**: Only active permissions are returned
- **Authentication**: 401 for unauthenticated requests

## Performance Considerations

- **Indexing**: Ensure indexes on `userId`, `userType`, `category`, `isActive`
- **Caching**: Consider caching default permissions by user type
- **Pagination**: Not needed as permissions are typically < 100 per user type 