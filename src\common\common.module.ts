import { Module } from '@nestjs/common';
import { MailService } from './services/mail.service';
import { JwtAuthService } from './services/jwt.service';
import { OtpGeneratorService } from './services/otpgenerator.service';
import { EncdecService } from './services/encdec.service';
import { JwtModule } from '@nestjs/jwt';
import { FirebaseService } from './services/Firebase.service';
import { PasswordService } from './services/password.service';
import { AwsService } from './services/aws.service';
import { MongooseModule } from '@nestjs/mongoose';
import { StorySchema } from '../Models/story.schema';
import { CronjobService } from './services/cronjob.service';
import { BranchDeeplinkService } from './services/branchDeeplink.service';
import { NotificationService } from './services/notification.service';
import {
  Notifications,
  NotificationsSchema,
} from 'src/Models/notification.schema';
import { GroupRule, GroupRuleSchema } from 'src/Models/groupRule.schema';
import { SeederService } from './services/seeder.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Notifications.name, schema: NotificationsSchema },
      { name: 'Story', schema: StorySchema },
      { name: GroupRule.name, schema: GroupRuleSchema },
    ]),
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: process.env.JWT_EXPIRE },
    }),
  ],
  providers: [
    AwsService,
    MailService,
    JwtAuthService,
    OtpGeneratorService,
    EncdecService,
    FirebaseService,
    PasswordService,
    CronjobService,
    BranchDeeplinkService,
    NotificationService,
    SeederService,
  ],
  exports: [
    AwsService,
    MailService,
    JwtAuthService,
    OtpGeneratorService,
    EncdecService,
    FirebaseService,
    PasswordService,
    CronjobService,
    BranchDeeplinkService,
    NotificationService,
    SeederService,
  ],
})
export class CommonModule {}
