import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';

export type UserNotificationPermissionDocument = UserNotificationPermission & Document;

export interface PermissionSetting {
  permissionId: mongoose.Types.ObjectId;
  isEnabled: boolean;
}

@Schema({ timestamps: true, versionKey: false })
export class UserNotificationPermission {
  @Prop({ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  })
  userId: mongoose.Types.ObjectId;

  @Prop({ 
    type: [{
      permissionId: { type: mongoose.Schema.Types.ObjectId, ref: 'NotificationPermission' },
      isEnabled: { type: Boolean, default: true },
      _id: false
    }],
    default: []
  })
  permissions: PermissionSetting[];


}

export const UserNotificationPermissionSchema = SchemaFactory.createForClass(UserNotificationPermission); 