import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/Modules/auth/auth.guard';
import { NotificationPermissionService } from '../services/notificationPermission.service';
import { SaveUserNotificationPermissionsDto, GetDefaultPermissionsDto } from '../dtos/notificationPermission.dto';

@Controller('notification-permissions')
export class NotificationPermissionController {
  constructor(
    private readonly notificationPermissionService: NotificationPermissionService,
  ) {}

  /**
   * Save user notification permissions
   * POST /api/v1/notification-permissions/save
   */
  @UseGuards(AuthGuard)
  @Post('save')
  async saveUserNotificationPermissions(
    @Req() req: any,
    @Body() data: SaveUserNotificationPermissionsDto,
  ) {
    return this.notificationPermissionService.saveUserNotificationPermissions(req, data);
  }

  /**
   * Get user's notification permissions
   * GET /api/v1/notification-permissions/user
   */
  @UseGuards(AuthGuard)
  @Get('user')
  async getUserNotificationPermissions(@Req() req: any) {
    return this.notificationPermissionService.getUserNotificationPermissions(req);
  }

  /**
   * Get default permissions based on logged-in user's type
   * GET /api/v1/notification-permissions/default?userType=creator_member
   */
  @Get('default')
  async getDefaultPermissions(@Query() query: GetDefaultPermissionsDto) {
    return this.notificationPermissionService.getDefaultPermissions(query);
  }
} 