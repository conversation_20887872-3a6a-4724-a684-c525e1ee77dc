const FUNCTIONS = {
  NOT_FOUND_MESSAGE: (text) => `${text} not found`,
  ALREADY_EXIST: (text) => `${text} already exist`,
  ALREADY_CONNECTED: () => `You are already connected`,
  ALREADY_REGISTERED: (text) => `${text} already registered`,
  ADDED_SUCCESSFULLY: (text) => `${text} added successfully`,
  CREATED_SUCCESSFULLY: (text) => `${text} created successfully`,
  FETCHED_SUCCESSFULLY: (text) => `${text} fetched successfully`,
  RESET_SUCCESSFULLY: (text) => `${text} reset successfully`,
  UPDATED_SUCCESSFULLY: (text) => `${text} updated successfully`,
  DELETED_SUCCESSFULLY: (text) => `${text} deleted successfully`,
  CHANGE_SUCCESSFULLY: (text) => `${text} changed successfully`,
  SENT_MESSAGE: (text) => `${text} sent successfully`,
  REQUIRED: (text) => `${text} is required`,
  NOT_ALLOW: (text) => `${text} not allowed`,
  INVALID: (text: string) => `Invalid ${text}`,
  SUCCESSFULL: (text: string) => `${text} successfully`,
  ALREADY_JOINED: (text: string) => `${text} already joined!`,
  ERROR: (text: string) => `Error while ${text}`,
  CONTACT_US: 'Thank you our team will contact you soon!',
  ACCEPT_REQUEST: (text) => `${text} request accepted successfully`,
  REJECT_REQUEST: (text) => `${text} request rejected successfully`,
  CANCEL_REQUEST: 'connection request canceled successfully',
  REMOVE: (text) => `${text} removed successfully`,
  ALREADY_APPLIED: (text) => `You have already applied for this ${text}`,
  APPLIED_SUCCESSFULLY: (text) =>
    `You have successfully applied for this ${text}`,
  ALREADY_UPDATED: (text) => `You have already updated this ${text}`,
  ALREADY_ACCEPTED: (text) => `You have already accepted ${text} request`,
  ALREADY_REJECTED: (text) => `You have already rejected ${text} request`,
};

export const DUMMY_NUMBERS = [
  '0000000000',
  '1111111111',
  '2222222222',
  '3333333333',
  '4444444444',
  '5555555555',
  '6666666666',
  '7777777777',
  '8888888888',
  '9999999999',
];

const API_MESSAGES = {
  SIGNUP: 'Signup successfully',
  SIGNIN: 'Signin successfully',
  LOGOUT: 'Logout successfully',
  FILE_UPLOADED: 'File uploaded successfully',
  OTP_VERIFIED: 'Otp verified successfully',
  USERNAME_AVAILABLE: 'Username available',
  RESET_PASSWORD: 'Reset password successfully',
  PROFILE_VIEWED: 'Profile view logged successfully',
  LIKE_POST: 'You have like the post successfully',
  UNLIKE_POST: 'You have un-like the post successfully',
  LIKE_STORY: 'You have like the story successfully',
  UNLIKE_STORY: 'You have un-like the story successfully',
  BOOKMARK_REMOVE: 'Bookmark removed successfully',
  SKIP: 'Skip successfully',
  TOKEN: 'Token generated successfully',
  FOLLOW_USER: 'You have followed the user successfully',
  SUBSCRIBE_USER: 'You have subscribed the user successfully',
  UNSUBSCRIBE_USER: 'You have unsubscribed the user successfully',
  UNFOLLOW_USER: 'You have un-followed the user successfully',
  LEAVE_CHANNEL: 'You have successfully left the channel',
  INVITE_SENT: 'Invite sent successfully',
  INVITE_ACCEPT: 'Invitation accepted successfully',
  INVITE_REJECT: 'Invitation rejected successfully',
  INVITATION_CANCEL: 'Invitation cancel successfully',
  FOLLOW_REQUEST_SENT: 'Following request sent successfully',
  FOLLOW_REQUEST_CANCELLED: 'Following request canceled successfully',
  JOINED_SUCCESSFULLY: (text) => `${text} joined successfully`,
};

const ERROR_MESSAGES = {
  IMAGE_UPLOAD_ERROR: 'Error while uploading image',
  VIDEO_UPLOAD_ERROR: 'Error while uploading video',
  THUMBNAIL_ERROR: 'Error while generating thumbnail',
  ALREADY_MEMBER: (text) => `You are already member of ${text}`,
  FILE_UPLOAD_ERROR: 'Error while uploading file',
  ALREADY_PARTICIPATED: 'You have already participated in this event',
  THUMBNAIL_UPLOAD_ERROR: 'Error while uploading thumbnail',
  FILE_SIZE_LARGE: 'File size is too large',
  FILE_SIZE_EXCEED: 'File size limit exceeded',
  FILE_TYPE_NOT_MATCHED: "File type doesn't matched",
  FILE_TYPE_ERROR:
    'Only (PNG, JPG, JPEG, GIF, MP4, MKV, MPEG, MOV, WEBM) files are allowed',
  IMAGE_TYPE_ERROR: 'Only PNG, JPG, JPEG and GIF files are allowed',
  VIDEO_TYPE_ERROR: 'Only mp4, mkv, mpeg, mov and webm files are allowed',
  IMAGE_SIZE_LARGE: 'File size is too large',
  INVALID_FILE_TYPE: 'Invalid file type',
  EMAIL_NOT_VERIFIED: 'Your Email is not Verified',
  INVALID_CREDENTIAL: 'Invalid Credential',
  INCORRECT_OLD_PASSWORD: 'Old password is incorrect',
  OLD_NEW_PASSWORD_CANT_SAME: 'Old password and New password can not be same',
  OTP_MISMATCH: 'Otp does not match',
  OTP_EXPIRED: 'Otp expired please try again.',
  USERNAME_ALREADY_EXIST: 'Username already exist',
  PASSWORD_NOT_MATCH: 'Password and Confirm Password must be same',
  UNAUTHORIZED: 'You are not authorized to access this resource',
  INVITE_NOT_ALLOW: 'You are not allowed to send invitation',
  MEMBER_NOT_FOUND: 'You are not a member of this group',
  POST_NOT_ALLOW: 'You are not allowed to add a post',
  STORY_NOT_ALLOW: 'Fans are not allowed to add stories.',
  ALREADY_CANCELLED: `You have already cancelled connection request`,
  ALREADY_REMOVE: `You have already removed connection`,
  ONLY_ADMIN_CAN_POST_IN_GROUP: 'Only admin is allow to post in the group.',
};

export default {
  ...FUNCTIONS,
  ...API_MESSAGES,
  ...ERROR_MESSAGES,
};
