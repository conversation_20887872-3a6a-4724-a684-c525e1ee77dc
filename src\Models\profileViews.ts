import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';
import { Post } from './post.schema';

export type profileViewDocument = ProfileViews & Document;

@Schema({ timestamps: true, versionKey: false })
export class ProfileViews {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  viewerId: Post;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: User;
}

export const ProfileViewsSchema = SchemaFactory.createForClass(ProfileViews);
