# Post Suggestions API

## Overview
The Post Suggestions API provides suggested posts to display when users open the search screen. This API returns popular and recent posts that users might be interested in, sorted by engagement metrics.

## Endpoint
```
GET /user/search/suggestions
```

## Authentication
This endpoint requires authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| page | number | No | 1 | Page number for pagination |
| perPage | number | No | 10 | Number of posts per page |

## Example Request
```bash
curl -X GET "http://localhost:3000/user/search/suggestions?page=1&perPage=10" \
  -H "Authorization: Bearer <your-jwt-token>"
```

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Post suggestions fetched successfully",
  "data": [
    {
      "_id": "post_id",
      "caption": "Post caption",
      "title": "Post title",
      "media": ["media_urls"],
      "reactionCounts": {
        "like": 10,
        "love": 5,
        "laugh": 2
      },
      "totalComments": 15,
      "totalReactions": 17,
      "postLabel": "post",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "userId": {
        "_id": "user_id",
        "firstName": "John",
        "lastName": "Doe",
        "userName": "johndoe",
        "profileImage": "profile_image_url",
        "businessOrganizationName": "Company Name"
      }
    }
  ],
  "pagination": {
    "totalResults": 100,
    "currentResults": 10,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

### Error Response (400 Bad Request)
```json
{
  "success": false,
  "message": "Error message",
  "statusCode": 400
}
```

## Features

### Post Selection Criteria
- **Engagement-based sorting**: Posts are sorted by total reactions, total comments, and creation date
- **Blocked user filtering**: Posts from users that the current user has blocked are automatically excluded
- **Pagination support**: Supports pagination with configurable page size

### Post Data Included
- Post content (caption, title)
- Media attachments
- Engagement metrics (reactions, comments)
- Post metadata (creation date, post label)
- User information (name, username, profile image, organization name)

## Implementation Details

### Service Method
The API is implemented in the `UserService` class with the method `getPostSuggestions()`.

### Database Query
The API uses MongoDB aggregation to:
1. Filter out posts from blocked users
2. Populate user information
3. Sort by engagement metrics
4. Apply pagination

### Security
- Requires authentication via JWT token
- Respects user blocking preferences
- Validates input parameters

## Usage in Frontend

### React/JavaScript Example
```javascript
const fetchPostSuggestions = async (page = 1, perPage = 10) => {
  try {
    const response = await fetch(
      `/user/search/suggestions?page=${page}&perPage=${perPage}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const data = await response.json();
    
    if (data.success) {
      return data.data; // Array of suggested posts
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('Error fetching post suggestions:', error);
    throw error;
  }
};
```

### Integration with Search Screen
This API should be called when the search screen is opened to show initial post suggestions before the user starts typing their search query.

## Notes
- The API automatically handles user blocking - posts from blocked users are excluded
- Posts are sorted by engagement (reactions + comments) and recency
- The API supports pagination for better performance with large datasets
- All user information is populated for easy display in the UI 