import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';

export type ConnectionInfoDocument = ConnectionInfo & Document;

export enum StatusEnum {
  PENDING = 'pending',
  ACCEPT = 'accept',
  REJECT = 'reject',
  REMOVE = 'remove',
}

@Schema({ timestamps: true })
export class ConnectionInfo {
  @Prop({ default: StatusEnum.PENDING, enum: StatusEnum })
  status: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  userId: User;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  connectionWithId: User;
}

export const ConnectionInfoSchema =
  SchemaFactory.createForClass(ConnectionInfo);
