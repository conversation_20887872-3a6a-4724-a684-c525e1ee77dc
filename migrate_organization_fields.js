const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const db = mongoose.connection;

db.on('error', console.error.bind(console, 'MongoDB connection error:'));
db.once('open', async () => {
  console.log('Connected to MongoDB');
  
  try {
    // Get the users collection
    const usersCollection = db.collection('users');
    
    // Find all organization users that don't have the new fields populated
    const organizationUsers = await usersCollection.find({
      iAmMember: 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility',
      isFakeAccount: false,
      $or: [
        { organizationMainCategory: { $exists: false } },
        { organizationMainCategory: null },
        { organizationSubcategory: { $exists: false } },
        { organizationSubcategory: null }
      ]
    }).toArray();
    
    console.log(`Found ${organizationUsers.length} organization users to migrate`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const user of organizationUsers) {
      try {
        // Try to extract organization info from signUpData
        let mainCategory = null;
        let subcategory = null;
        
        if (user.signUpData && Array.isArray(user.signUpData)) {
          // Look for main category and subcategory in signUpData
          for (const item of user.signUpData) {
            if (item.isSelected && item.slug) {
              // Check if this is a main category (parentSlug contains "4")
              if (item.title === 'who_are_you' && item.subCategory === 'main_category') {
                mainCategory = item.slug;
              }
              // Check if this is a subcategory
              else if (item.subCategory === 'sub_type') {
                subcategory = item.slug;
              }
            }
          }
        }
        
        // If we couldn't find from signUpData, try to infer from businessOrganizationName
        if (!mainCategory || !subcategory) {
          // Set default values based on common patterns
          if (!mainCategory) {
            mainCategory = 'union_1'; // Default main category
          }
          if (!subcategory) {
            subcategory = 'other'; // Default subcategory
          }
        }
        
        // Update the user document
        const result = await usersCollection.updateOne(
          { _id: user._id },
          {
            $set: {
              organizationMainCategory: mainCategory,
              organizationSubcategory: subcategory
            }
          }
        );
        
        if (result.modifiedCount > 0) {
          updatedCount++;
          console.log(`Updated user: ${user.userName || user.businessOrganizationName} - Main: ${mainCategory}, Sub: ${subcategory}`);
        } else {
          skippedCount++;
          console.log(`Skipped user: ${user.userName || user.businessOrganizationName} - No changes needed`);
        }
        
      } catch (error) {
        console.error(`Error updating user ${user._id}:`, error.message);
        skippedCount++;
      }
    }
    
    console.log('\nMigration completed!');
    console.log(`Updated: ${updatedCount} users`);
    console.log(`Skipped: ${skippedCount} users`);
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed');
  }
});
