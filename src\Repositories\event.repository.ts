import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { EventDocument } from 'src/Models/event.schema';

@Injectable()
export class EventRepository extends AbstractRepository<EventDocument> {
  constructor(@InjectModel('Event') eventModel: Model<EventDocument>) {
    super(eventModel);
  }

  async findEventDetails(eventId: string): Promise<EventDocument> {
    return this.model
      .findById(eventId)
      .populate({
        path: 'collaborators',

        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({
        path: 'participants',

        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .exec();
  }

  async findAllEventWithDetails(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ): Promise<EventDocument[]> {
    return this.model
      .find(filter)
      .populate({
        path: 'collaborators',

        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({
        path: 'participants',

        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .limit(limitData)
      .skip(skipData)
      .sort(sortObj)
      .exec();
  }
}
