import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';

export type licenseCertificationDocument = LicenseCertification & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class LicenseCertification {
  @Prop({ type: String })
  name: string;

  @Prop({ type: String })
  organization: string;

  @Prop({ type: Date })
  issueDate: Date;

  @Prop({ type: Date })
  expirationDate: Date;

  @Prop({ type: String })
  credentialId: string;

  @Prop({ type: String })
  credentialUrl: string;

  @Prop({ type: [String] })
  skills: string[];

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const LicenseCertificationSchema =
  SchemaFactory.createForClass(LicenseCertification);
