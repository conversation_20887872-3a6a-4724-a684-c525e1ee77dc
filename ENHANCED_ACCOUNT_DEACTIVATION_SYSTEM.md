# Enhanced Account Deactivation System

## Overview
This enhanced account deactivation system implements a comprehensive approach to handle user account deactivation with automatic cleanup after 30 days. The system includes proper timestamp tracking, automated background processing, and admin monitoring capabilities.

## Key Features

### 1. **Enhanced User Schema**
- Added `deactivatedOn` Date field to track when account was deactivated
- Maintains existing `isDeactivated` boolean flag for backward compatibility

### 2. **Automatic Cleanup**
- Accounts are automatically deleted 30 days after deactivation
- Daily cron job runs at 2:00 AM to process expired accounts
- Comprehensive cleanup of user data and associated files

### 3. **Admin Monitoring**
- Statistics endpoint to monitor deactivated accounts
- Manual cleanup trigger for administrative control
- Detailed logging and error handling

## Database Schema Changes

### User Schema Updates
```typescript
@Prop({ type: Boolean, default: false })
isDeactivated: boolean;

@Prop({ type: Date, default: null })
deactivatedOn: Date;
```

## API Endpoints

### 1. User Deactivation (Enhanced)
**Endpoint:** `POST /user/deactivate-account`

**Request Body:**
```json
{
  "isDeactivated": true
}
```

**Response:**
```json
{
  "status": true,
  "message": "Account deactivated successfully",
  "data": {
    "isDeactivated": true,
    "deactivatedOn": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. Deactivated Accounts Statistics (Admin)
**Endpoint:** `GET /cms/deactivated-accounts-stats`

**Response:**
```json
{
  "total": 25,
  "expiredCount": 3,
  "daysUntilCleanup": [5, 12, 18, 22, 28]
}
```

### 3. Manual Cleanup Trigger (Admin)
**Endpoint:** `POST /cms/cleanup-expired-accounts`

**Response:**
```json
{
  "message": "Account cleanup process completed successfully",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## System Architecture

### Services Overview

#### 1. AccountCleanupService
**Location:** `src/common/services/accountCleanup.service.ts`

**Key Methods:**
- `cleanupExpiredDeactivatedAccounts()` - Main cleanup process
- `getDeactivatedAccountsStats()` - Statistics and monitoring
- `cleanupAccountData()` - User file cleanup
- `cleanupRelatedData()` - Associated data cleanup

#### 2. Enhanced CronjobService
**Location:** `src/common/services/cronjob.service.ts`

**New Cron Job:**
```typescript
@Cron('0 0 2 * * *') // Daily at 2:00 AM
async cleanupExpiredDeactivatedAccounts() {
  await this.accountCleanupService.cleanupExpiredDeactivatedAccounts();
}
```

## Cleanup Process Flow

### 1. **Account Identification**
```typescript
// Find accounts deactivated more than 30 days ago
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

const expiredAccounts = await this.userModel.find({
  isDeactivated: true,
  deactivatedOn: { $lte: thirtyDaysAgo, $ne: null }
});
```

### 2. **Data Cleanup**
- **User Files:** Profile images, uploaded media
- **S3 Storage:** User-specific folders and files
- **Related Data:** Posts, comments, connections, notifications (configurable)

### 3. **Account Deletion**
- Final removal from User collection
- Logging of cleanup actions

## Implementation Details

### Files Modified/Created

1. **User Schema** (`src/Models/user.schema.ts`)
   - Added `deactivatedOn` field

2. **User Service** (`src/Modules/user/services/user.service.ts`)
   - Enhanced `deactivateAccount()` method to set/clear `deactivatedOn`

3. **Account Cleanup Service** (`src/common/services/accountCleanup.service.ts`)
   - New service for handling account cleanup logic

4. **Cronjob Service** (`src/common/services/cronjob.service.ts`)
   - Added daily cleanup cron job

5. **Admin Controller** (`src/Modules/admin/controller/admin.controller.ts`)
   - Added monitoring and manual trigger endpoints

6. **Common Module** (`src/common/common.module.ts`)
   - Registered new AccountCleanupService

## Configuration

### Cron Schedule
- **Cleanup Job:** Daily at 2:00 AM (`0 0 2 * * *`)
- **Story Cleanup:** Hourly (`* * 1 * * *`) - existing functionality

### Cleanup Thresholds
- **Account Retention:** 30 days after deactivation
- **Grace Period:** Accounts can be reactivated before cleanup

## Monitoring and Logging

### Log Messages
```
--- Starting cleanup of expired deactivated accounts ---
Found 3 expired deactivated accounts for cleanup
Cleaned up account data for user: <EMAIL> (ID: 507f1f77bcf86cd799439011)
Successfully deleted 3 expired deactivated accounts
--- Completed cleanup of expired deactivated accounts ---
```

### Error Handling
- Individual account cleanup failures don't stop the entire process
- Detailed error logging for debugging
- Graceful handling of S3 deletion failures

## Security Considerations

### 1. **Authentication**
- Admin endpoints require valid JWT authentication
- Only authenticated admins can trigger manual cleanup

### 2. **Data Protection**
- 30-day grace period allows account recovery
- Comprehensive file cleanup prevents data leaks
- Audit trail through logging

### 3. **Error Recovery**
- Failed cleanups are logged but don't crash the system
- Manual trigger allows retry of failed operations

## Usage Examples

### Deactivate Account
```bash
curl -X POST http://localhost:3000/user/deactivate-account \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"isDeactivated": true}'
```

### Check Deactivated Account Stats (Admin)
```bash
curl -X GET http://localhost:3000/cms/deactivated-accounts-stats \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

### Manual Cleanup Trigger (Admin)
```bash
curl -X POST http://localhost:3000/cms/cleanup-expired-accounts \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

## Future Enhancements

### 1. **Configurable Retention Period**
- Environment variable for retention days
- Per-user retention settings

### 2. **Enhanced Related Data Cleanup**
- Configurable cleanup of posts, comments, likes
- Preserve anonymized analytics data

### 3. **Notification System**
- Email warnings before account deletion
- Admin notifications for cleanup activities

### 4. **Recovery Mechanisms**
- Soft delete with recovery window
- Account export before deletion

## Troubleshooting

### Common Issues

1. **Cron Jobs Not Running**
   - Verify `@nestjs/schedule` is properly configured
   - Check application logs for cron execution

2. **S3 Cleanup Failures**
   - Verify AWS credentials and permissions
   - Check S3 bucket policies

3. **Database Connection Issues**
   - Ensure User model is properly injected
   - Verify database connectivity

### Debug Commands
```bash
# Check cron job registration
curl -X GET http://localhost:3000/cms/deactivated-accounts-stats

# Manual cleanup trigger for testing
curl -X POST http://localhost:3000/cms/cleanup-expired-accounts
```

## Best Practices

1. **Regular Monitoring**
   - Monitor cleanup statistics regularly
   - Set up alerts for cleanup failures

2. **Testing**
   - Test cleanup process in staging environment
   - Verify file deletion works correctly

3. **Backup Strategy**
   - Consider backup before deletion
   - Implement data recovery procedures

4. **Performance**
   - Monitor database performance during cleanup
   - Consider batch processing for large datasets

## Conclusion

This enhanced account deactivation system provides a robust, automated solution for managing user account lifecycle. The 30-day retention period balances user convenience with data cleanup requirements, while comprehensive monitoring ensures system reliability.
