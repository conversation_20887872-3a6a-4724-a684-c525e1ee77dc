import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';

export type projectDocument = Project & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class Project {
  @Prop({ type: String })
  name: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: [String] })
  skills: string[];

  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }] })
  affiliateOrganizations: mongoose.Types.ObjectId[];

  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  })
  collaborators: mongoose.Types.ObjectId[];

  @Prop({ type: Boolean, default: false })
  currentlyWorking: boolean;

  @Prop({ type: Date })
  startDate: Date;

  @Prop({ type: Date })
  endDate: Date;

  @Prop({ type: String })
  link: string;

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const ProjectSchema = SchemaFactory.createForClass(Project);
