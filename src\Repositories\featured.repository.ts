import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { FeaturedDocument } from 'src/Models/featured.schema';

@Injectable()
export class FeaturedRepository extends AbstractRepository<FeaturedDocument> {
  constructor(@InjectModel('Featured') featuredModel: Model<FeaturedDocument>) {
    super(featuredModel);
  }

  public async findAllFeatures(
    filter: FilterQuery<FeaturedDocument>,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    return this.model
      .find(filter)
      .populate({
        path: 'userId',
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        match: { isFakeAccount: false },
      })
      .sort(sortObj)
      .limit(limitData)
      .skip(skipData)
      .exec();
  }
}
