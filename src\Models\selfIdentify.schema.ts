import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Date } from 'mongoose';
import { User } from './user.schema';

export type selfIdentifyDocument = SelfIdentify & Document;

interface OthersData {
  self_identify: string;
  addTxt: string | null;
}

interface LanguageInfoData {
  language: string;
  proficiencyLevel: string;
}

@Schema({ timestamps: true })
export class SelfIdentify {
  @Prop({ type: [String], default: null })
  ethnicity: string[];

  @Prop({ default: false })
  ethnicityVisibleOnProfile: boolean;

  @Prop({ default: false })
  ethnicitySearchable: boolean;

  @Prop({ type: [String], default: null })
  nationality: string[];

  @Prop({ default: false })
  nationalityVisibleOnProfile: boolean;

  @Prop({ default: false })
  nationalitySearchable: boolean;

  @Prop({ type: [String], default: null })
  disability: string[];

  @Prop({ default: false })
  disabilityVisibleOnProfile: boolean;

  @Prop({ default: false })
  disabilitySearchable: boolean;

  @Prop({
    default: null,
  })
  customDisability: string;

  @Prop({
    type: [{ language: String, proficiencyLevel: String }],
    default: null,
    _id: false,
  })
  languagesSpokenSigned: LanguageInfoData[];

  @Prop({ type: Date, default: null })
  birthDate: Date;

  @Prop({ default: false })
  birthDateVisibleOnProfile: boolean;

  @Prop({ default: false })
  birthDateSearchable: boolean;

  @Prop({ type: [String], default: null })
  skills: string[];

  @Prop({
    type: [String],
  })
  selfIgender: string[];

  @Prop({ default: false })
  selfIgenderVisibleOnProfile: boolean;

  @Prop({ default: false })
  selfIgenderSearchable: boolean;

  @Prop({
    default: null,
  })
  customSelfIgender: string;

  @Prop({ type: [String], default: null })
  other: string[];

  @Prop({ type: String })
  ageRange: string;

  @Prop({ default: false })
  showYearOnYourBirthday: boolean;

  @Prop({ type: String })
  genderPronoun: string;

  @Prop({ type: String })
  customGenderInfo: string;

  @Prop({ default: false })
  otherVisibleOnProfile: boolean;

  @Prop({ default: false })
  otherSearchable: boolean;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: User;

  @Prop({
    type: [String],
    default: [],
  })
  fanDegree: string[];
}

export const SelfIdentifySchema = SchemaFactory.createForClass(SelfIdentify);
