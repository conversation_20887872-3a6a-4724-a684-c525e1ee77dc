import { IsArray, IsBoolean, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { UserTypeEnum } from 'src/Models/notificationPermission.schema';

export class PermissionDto {
  @IsMongoId()
  permissionId: string;
  
  @IsBoolean()
  isEnabled: boolean;
}

export class SaveUserNotificationPermissionsDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PermissionDto)
  permissions: PermissionDto[];
}

export class GetDefaultPermissionsDto {
  @IsString()
  @IsNotEmpty()
  userType: UserTypeEnum;
}

 