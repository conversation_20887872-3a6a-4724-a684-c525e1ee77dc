import { Privacy, Visibility } from '../../../common/constant/enum';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';
import { IsEnum, IsString } from 'class-validator';
import CONSTANT from '../../../common/constant/common.constant';
import { AllowPosting } from 'src/Models/group.schema';

export class CreateGroupDto {
  @IsStringValidation('name', 500, false)
  readonly name: string;

  @IsStringValidation('description', 2000, true)
  readonly description: string;

  @IsStringValidation('privacy', 50, false)
  @IsEnum(Privacy, { each: true, message: CONSTANT.INVALID('privacy') })
  readonly privacy: string;

  @IsStringValidation('visibility', 50, false)
  @IsEnum(Visibility, { each: true, message: CONSTANT.INVALID('visibility') })
  readonly visibility: string;

  @IsStringValidation('coverPhoto', null, true)
  coverPhoto?: string;

  @IsString()
  @IsEnum(AllowPosting)
  allowPosting: AllowPosting;
}
