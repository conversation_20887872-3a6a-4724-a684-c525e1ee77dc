import {
  <PERSON><PERSON>rray,
  IsBoolean,
  <PERSON>Enum,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { PostCollaboratorTagPeopleEnum } from 'src/common/constant/enum';
import { IsOptionalString } from 'src/common/dto/common.dto';
import { IsStringValidation } from 'src/Custom/helpers/dto.helper';
import CONSTANT from '../../../common/constant/common.constant';
import { StatusEnum } from 'src/Models/connectionInfo.schema';

export class MediaDto {
  @IsString()
  mediaType: string;

  @IsString()
  url: string;

  @IsString()
  thumbUrl: string | null;
}

export class acceptRejectCollabRequestDTO {
  @IsString()
  postId: string;

  @IsEnum(StatusEnum)
  @IsString()
  status: StatusEnum;
}

export class CollaborationDto {
  @IsString()
  userId: string;
}

export class LocationDto {
  @IsArray()
  @IsNumber({}, { each: true })
  coordinates: number[];

  @IsString()
  name: string;
}

export class GetPostDto {
  @IsMongoId({ message: CONSTANT.INVALID('postId') })
  @IsStringValidation('postId', 24, false)
  readonly postId: string;

  @IsStringValidation('isGroup', 24, false)
  isGroup: string;
}

export class addPostDto {
  @IsOptionalString()
  caption: string;

  @IsOptionalString()
  title: string;

  @IsOptional()
  location: LocationDto;

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true }) // Ensure that each element in the array is a valid MongoDB ObjectId
  collaborators: string[];
}

export class editPostDto {
  @IsString()
  postId: string;

  @IsOptionalString()
  caption: string;

  @IsOptionalString()
  title: string;

  @IsOptional()
  location: LocationDto;
}

export class postIdDto {
  @IsString()
  postId: string;
}

export class postCollaboratorTagPeopleDto {
  @IsString()
  postId: string;

  @IsEnum(PostCollaboratorTagPeopleEnum)
  type: PostCollaboratorTagPeopleEnum;
}

export class repostUserDto {
  @IsString()
  postId: string;
}

export class likeUnlikePostDto {
  @IsString()
  postId: string;

  @IsString()
  type: boolean;
}

export class likeUnlikeCommentDto {
  @IsString()
  commentId: string;

  @IsString()
  postId: string;

  @IsString()
  type: boolean;
}

export class addCommentPostDto {
  @IsString()
  comment: string;

  @IsString()
  postId: string;
}

export class editPostCommentDto {
  @IsString()
  commentId: string;

  @IsString()
  comment: string;
}

export class bookmarkPostDto {
  @IsString()
  postId: string;

  @IsBoolean()
  isBookmark: boolean;
}

export class addStoryDto {
  @IsNotEmpty()
  media: MediaDto[];
}

export class likeUnlikeStoryDto {
  @IsString()
  storyId: string;

  @IsBoolean()
  isLike: boolean;
}
