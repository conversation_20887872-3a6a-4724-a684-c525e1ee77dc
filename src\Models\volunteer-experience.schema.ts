import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Transform } from 'class-transformer';
import mongoose, { Document } from 'mongoose';

export type volunteerExperienceDocument = VolunteerExperience & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class VolunteerExperience {
  @Prop({ type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }] })
  organization: mongoose.Types.ObjectId[];

  @Prop({ type: String })
  role: string;

  @Prop({ type: String })
  cause: string;

  @Prop({ type: Boolean })
  isVolunteering: boolean;

  @Prop({ type: Date })
  startDate: Date;

  @Prop({ type: Date })
  endDate: Date;

  @Prop({ type: String })
  description: string;

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const VolunteerExperienceSchema =
  SchemaFactory.createForClass(VolunteerExperience);
