import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GroupRule } from '../../Models/groupRule.schema';

@Injectable()
export class SeederService {
  constructor(
    @InjectModel(GroupRule.name)
    private readonly groupRuleModel: Model<GroupRule>,
  ) {}

  async insertGroupRule() {
    try {
      // Parse the JSON data
      const groupRuleData = [
        {
          title: '1. Be Respectful',
          explanation: [
            'Speak kindly and respectfully to and about group members.',
            'Personal attacks, name-calling, and harassment are generally prohibited.',
          ],
        },
        {
          title: '2. No Hate Speech or Bullying',
          explanation: [
            'Posts and comments that are racist, homophobic, sexist, or otherwise discriminatory are not allowed.',
            'Bullying or incitement to violence against any individual or group is forbidden.',
          ],
        },
        {
          title: '3. No Spam or Self-Promotion',
          explanation: [
            'Posting irrelevant links, self-promotional content, or repetitive messages is usually disallowed.',
            'Permission may be required before promoting businesses, pages, or products.',
          ],
        },
        {
          title: '4. Stay On Topic',
          explanation: [
            "Contributions should be relevant to the group's focus and purpose.",
            'Off-topic posts might be deleted or flagged.',
          ],
        },
        {
          title: '5. No Illegal Activities',
          explanation: [
            'Discussions, posts, and sharing of content related to illegal activities are prohibited.',
            'This includes pirated content, drugs, and other unlawful acts.',
          ],
        },
        {
          title: '6. Respect Privacy',
          explanation: [
            'Do not share personal information or photos of other members without consent.',
            'Respect the confidentiality of private messages.',
          ],
        },
        {
          title: '7. Use Appropriate Resources',
          explanation: [
            'Only share verified and credible sources when posting information.',
            'Misinformation or fake news should be avoided.',
          ],
        },
        {
          title: '8. No Graphic Content',
          explanation: [
            'Obscene, sexually explicit, or violent content is generally not allowed.',
            'This includes graphic images or videos without proper warnings.',
          ],
        },
        {
          title: '9. Follow Platform Terms',
          explanation: [
            'Abide by the terms of service and community standards of the social media platform in use.',
            'Breaking platform rules often results in penalties or bans.',
          ],
        },
        {
          title: '10. Report Issues',
          explanation: [
            'Encourage reporting of questionable behavior or posts to administrators or moderators.',
            'Use built-in platform reporting tools when necessary.',
          ],
        },
        {
          title: '11. No Flooding',
          explanation: [
            'Avoid posting multiple, continuous posts in short periods.',
            'This helps maintain a balanced and engaging community environment.',
          ],
        },
        {
          title: '12. Constructive Criticism Only',
          explanation: [
            'Critiques should be helpful, objective, and respectful.',
            'Avoid negative or rude comments that could be harmful.',
            'Engage in discussions that promote knowledge sharing and community welfare.',
          ],
        },
        {
          title: '13. Stay Positive',
          explanation: [
            'Encourage a positive and supportive atmosphere.',
            'Motivational and uplifting posts are highly encouraged.',
          ],
        },
        {
          title: '14. No Politics',
          explanation: [
            "Avoid discussing politically charged topics unless it is the group's focus.",
            'Political posts can often lead to heated arguments.',
          ],
        },
        {
          title: '15. Mind Language',
          explanation: [
            'Use polite and respectful language.',
            'Avoid profanity or crude expressions.',
          ],
        },
        {
          title: '16. No Commercial Solicitation',
          explanation: [
            'Selling goods or services without explicit permission is prohibited.',
            'Casual mentions of personal ventures should be subtle and appropriate.',
          ],
        },
        {
          title: '17. Respect Admin Decisions',
          explanation: [
            'Follow the guidance and decisions of group administrators and moderators.',
            'Questioning or challenging admins publicly can lead to moderation actions.',
          ],
        },
        {
          title: '18. No Trolling',
          explanation: [
            'Do not provoke or bait others into arguments or disagreements.',
            'Trolls often disrupt the group harmony and can be banned.',
          ],
        },
        {
          title: '19. Keep Posts Legal',
          explanation: [
            'Ensure that all shared content complies with local laws and regulations.',
            'This prevents legal complications for the group.',
          ],
        },
        {
          title: '20. Avoid Duplicate Content',
          explanation: [
            'Check existing posts before sharing content to avoid duplicates.',
            "Repetitive content can reduce the group's diversity of discussion.",
          ],
        },
        {
          title: '21. Provide Context',
          explanation: [
            'When sharing links or media, include a brief summary or opinion to provide context.',
            'This fosters more discussion and engagement.',
          ],
        },
        {
          title: '22. Use Descriptive Titles',
          explanation: [
            'Titles of posts should be informative and relevant.',
            'This makes it easier for members to understand and engage with the content.',
          ],
        },
        {
          title: '23. No Personal Advertising',
          explanation: [
            'Do not post advertisements for personal gain, unless allowed by group rules.',
            'This includes MLM schemes and personal fundraisers.',
          ],
        },
        {
          title: '24. Respect Cultural Differences',
          explanation: [
            'Be mindful of cultural sensibilities and differences.',
            'Avoid making assumptions or generalizations about different cultures.',
          ],
        },
        {
          title: '25. Encourage Participation',
          explanation: [
            'Welcome new members and encourage them to participate.',
            'Friendly engagement helps build a strong community.',
          ],
        },
        {
          title: '26. No Derailing Threads',
          explanation: [
            'Stick to the topic when commenting on posts.',
            'Do not hijack threads with unrelated issues.',
          ],
        },
        {
          title: '27. Disclose Affiliations',
          explanation: [
            'If sharing a product or service you are affiliated with, disclose your connection.',
            'Transparency builds trust within the group.',
          ],
        },
        {
          title: '28. Constructive Debates',
          explanation: [
            'Healthy debates are encouraged but keep them respectful and polite.',
            'Personal attacks during debates are prohibited.',
          ],
        },
        {
          title: '29. Language Appropriateness',
          explanation: [
            'Posts should be in the primary language of the group.',
            'This ensures that all members can understand and participate.',
          ],
        },
        {
          title: '30. Update Rules Periodically',
          explanation: [
            'Be open to feedback and willing to update rules as the group evolves.',
            'Regular updates help maintain relevance and order.',
          ],
        },
      ];

      const bulkOps = groupRuleData.map((item) => ({
        updateOne: {
          filter: { title: item.title },
          update: { $set: item },
          upsert: true,
        },
      }));

      await this.groupRuleModel.bulkWrite(bulkOps);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
