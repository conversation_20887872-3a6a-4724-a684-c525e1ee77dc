import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { PeopleDocument } from 'src/Models/peoples.schema';

@Injectable()
export class PeopleRepository extends AbstractRepository<PeopleDocument> {
  constructor(@InjectModel('People') peopleModel: Model<PeopleDocument>) {
    super(peopleModel);
  }
}
