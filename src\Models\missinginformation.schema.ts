import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';

export type MissingInformationDocument = MissingInformation & Document;

@Schema({ timestamps: true })
export class MissingInformation {
  @Prop({ type: String })
  message: string;

  @Prop({ type: [String] })
  sections: string[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Schema.Types.ObjectId;
}

export const MissingInformationSchema =
  SchemaFactory.createForClass(MissingInformation);
