import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { WhoCanFindEventEnum } from 'src/common/constant/enum';

class DemographicDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  title: string;

  @IsString()
  description: string;
}

class PaymentMethodDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  type: string;

  @IsString()
  info: string;
}

class PersonDetailDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  firstName: string;

  @IsOptional()
  @IsString()
  middleName: string;

  @IsString()
  lastName: string;

  @IsString()
  organizationName: string;

  @IsOptional()
  @IsString()
  website: string;

  @IsString()
  jobTitle: string;

  @IsString()
  country: string;

  @IsString()
  postalCode: string;

  @IsBoolean()
  isHidden: boolean;
}

export class EventDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  title: string;

  @IsString()
  organizationName: string;

  @IsString()
  description: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => DemographicDto)
  @IsArray()
  demographics: DemographicDto[];

  @IsBoolean()
  isAccomodationForDisability: boolean;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  schedule: Date;

  @IsBoolean()
  isRecurring: boolean;

  @IsOptional()
  @IsString()
  recurringType: string;

  @IsOptional()
  @IsString()
  reminder: string;

  @IsBoolean()
  isInPerson: boolean;

  @IsBoolean()
  isVertualOrRemote: boolean;

  @IsOptional()
  @IsString()
  location: string;

  @IsOptional()
  @IsString()
  meetingLink: string;

  @IsBoolean()
  isThereFee: boolean;

  @IsOptional()
  @IsNumber()
  feeAmount: number;

  @IsOptional()
  @IsString()
  currency: string;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PaymentMethodDto)
  @IsArray()
  paymentMethods: PaymentMethodDto[];

  @IsOptional()
  @IsString()
  bioOfOrganizer: string;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  expirationDate: Date;

  @IsOptional()
  @ValidateNested()
  @Type(() => PersonDetailDto)
  organizer: PersonDetailDto;

  @IsOptional()
  @IsArray()
  @IsEnum(WhoCanFindEventEnum, { each: true })
  whoCanFind: string[];

  @IsString()
  poster: string;

  @IsOptional()
  @IsString()
  sharableLink: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  collaborators: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  participants: string[];
}
