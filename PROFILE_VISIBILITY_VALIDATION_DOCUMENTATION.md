# Profile Visibility Auto-Correction Documentation

## Overview

This document describes the auto-correction implementation for the `/api/v1/profile-visibility/save` endpoint. Instead of showing validation errors, the system automatically corrects the data to ensure that only one option in each field can be true while others are set to false.

## Auto-Correction Rules

### Core Rule
For each `settingName` group in the profile visibility settings:
- **Exactly one option** will have `isEnabled: true` (automatically corrected)
- **All other options** in the same `settingName` group will be set to `isEnabled: false` (automatically corrected)

### SettingName Groups Subject to Validation
1. **"Profile viewing options"** - Controls who can view profile information
2. **"Profile discovery with contact"** - Controls profile discovery settings  
3. **"Show active status"** - Controls active status visibility

### Supported Formats
The API supports both formats:
- **Legacy Format**: Field-based structure (`profileViewingOptions`, `profileDiscoveryOptions`, `activeStatusOptions`)
- **New Format**: SettingName-based structure (`subMenuDetails` with `settingName` groups)

## Implementation Details

### 1. Auto-Correction Function (`validateAndCorrectProfileVisibilityOptions`)

**Location**: `src/Modules/user/helper/profileVisibilityValidator.ts`

```typescript
const validation = validateAndCorrectProfileVisibilityOptions(profileVisibility);
const correctedData = validation.correctedData;
```

**Functionality**:
- Automatically corrects data to ensure exactly one option per `settingName` group has `isEnabled: true`
- Sets all other options in the same `settingName` group to `isEnabled: false`
- Supports both legacy format (field-based) and new format (settingName-based)
- Returns corrected data instead of throwing errors

### 2. Service-Level Auto-Correction

**Location**: `src/Modules/user/services/userSettings.service.ts`

**Methods Updated**:
- `updateProfileVisibility()` - Full update auto-correction (legacy format)
- `updateProfileVisibilityPartial()` - Partial update auto-correction (legacy format)
- `updateProfileVisibilityUnified()` - New unified format auto-correction (settingName-based)
- `mergeProfileVisibilityOptions()` - Merge logic with auto-correction

**Auto-Correction Logic**:
```typescript
const validation = validateAndCorrectProfileVisibilityOptions(mergedProfileVisibility);
const correctedData = validation.correctedData;
// Use correctedData for database update
```

### 3. Auto-Correction Logic

The `validateAndCorrectProfileVisibilityOptions()` function handles all correction scenarios for both formats:

**For New Format (settingName-based)**:
```typescript
correctedData.subMenuDetails.forEach((subMenu: any) => {
  if (subMenu.settings && Array.isArray(subMenu.settings)) {
    const enabledOptions = subMenu.settings.filter((option: any) => option.isEnabled === true);
    const enabledCount = enabledOptions.length;
    
    if (enabledCount === 0) {
      // No options enabled - enable the first one
      if (subMenu.settings.length > 0) {
        subMenu.settings[0].isEnabled = true;
        // Disable all others
        for (let i = 1; i < subMenu.settings.length; i++) {
          subMenu.settings[i].isEnabled = false;
        }
      }
    } else if (enabledCount > 1) {
      // Multiple options enabled - keep only the first one
      const firstEnabled = enabledOptions[0];
      subMenu.settings.forEach(option => {
        option.isEnabled = option.Name === firstEnabled.Name;
      });
    }
  }
});
```

**For Legacy Format (field-based)**:
```typescript
fieldsToValidate.forEach(fieldName => {
  const options = correctedData[fieldName];
  // Same logic as above but applied to field-based structure
});
```

## API Response Examples

### Valid Request
```json
POST /api/v1/profile-visibility/save
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": true },
    { "Name": "Private profile character", "isEnabled": false }
  ],
  "profileDiscoveryOptions": [
    { "Name": "Everyone", "isEnabled": false },
    { "Name": "My Connections", "isEnabled": true },
    { "Name": "People I Follow", "isEnabled": false },
    { "Name": "No One", "isEnabled": false }
  ],
  "activeStatusOptions": [
    { "Name": "Everyone", "isEnabled": false },
    { "Name": "My Connections", "isEnabled": false },
    { "Name": "People I Follow", "isEnabled": false },
    { "Name": "No One", "isEnabled": true }
  ]
}
```

**Response**: 200 OK with updated settings

### Request with Multiple Options Enabled (Auto-Corrected)
```json
POST /api/v1/profile-visibility/save
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": true },
    { "Name": "Private profile character", "isEnabled": true }  // Will be auto-corrected
  ]
}
```

**Response**: 200 OK with corrected data
```json
{
  "success": true,
  "message": "Profile Visibility Settings updated successfully",
  "data": {
    "profileVisibility": {
      "profileViewingOptions": [
        { "Name": "Your name headline", "isEnabled": true },
        { "Name": "Private profile character", "isEnabled": false }  // Auto-corrected
      ]
    }
  }
}
```

### Request with No Options Enabled (Auto-Corrected)
```json
POST /api/v1/profile-visibility/save
{
  "profileDiscoveryOptions": [
    { "Name": "Everyone", "isEnabled": false },
    { "Name": "My Connections", "isEnabled": false },
    { "Name": "People I Follow", "isEnabled": false },
    { "Name": "No One", "isEnabled": false }  // Will be auto-corrected
  ]
}
```

**Response**: 200 OK with corrected data
```json
{
  "success": true,
  "message": "Profile Visibility Settings updated successfully",
  "data": {
    "profileVisibility": {
      "profileDiscoveryOptions": [
        { "Name": "Everyone", "isEnabled": true },  // Auto-corrected
        { "Name": "My Connections", "isEnabled": false },
        { "Name": "People I Follow", "isEnabled": false },
        { "Name": "No One", "isEnabled": false }
      ]
    }
  }
}
```

### Partial Update - Single Option Set to False (Auto-Corrected)
```json
POST /api/v1/profile-visibility/save
{
  "profileViewingOptions": [
    {
      "Name": "Your name headline",
      "isEnabled": false
    }
  ]
}
```

**Response**: 200 OK with corrected data
```json
{
  "success": true,
  "message": "Profile Visibility Settings updated successfully",
  "data": {
    "profileVisibility": {
      "profileViewingOptions": [
        {
          "Name": "Your name headline",
          "isEnabled": false
        },
        {
          "Name": "Private profile character",
          "isEnabled": true  // Auto-corrected - automatically enabled
        }
      ]
    }
  }
}
```

## Auto-Correction Behavior

### No Error Responses
- **HTTP Status**: Always 200 OK
- **Response Format**: JSON object with corrected data
- **Auto-Correction Types**:
  - Multiple options enabled → keeps first enabled, disables others
  - No options enabled → enables the first option
  - Invalid data structure → corrected to valid structure

### Automatic Correction Rules
The system automatically corrects invalid states:
- If multiple options are enabled → keeps first enabled, disables others
- If no options are enabled → enables the first option
- If exactly one option is enabled → ensures others are false
- If partial update results in no enabled options → automatically enables the first available option

## Testing

### Manual Testing
Use the test file `test-profile-visibility-validation.js` to verify validation logic:

```bash
node test-profile-visibility-validation.js
```

### API Testing
Test the endpoint with various scenarios:
1. Valid requests with different combinations
2. Requests with multiple enabled options (should auto-correct)
3. Requests with no enabled options (should auto-correct)
4. Partial updates that might create invalid states (should auto-correct)

## Frontend Integration

### Auto-Correction on Frontend
Frontend can implement similar auto-correction logic for better UX:

```javascript
function autoCorrectProfileVisibility(data) {
  const correctedData = JSON.parse(JSON.stringify(data));
  
  ['profileViewingOptions', 'profileDiscoveryOptions', 'activeStatusOptions'].forEach(field => {
    const options = correctedData[field];
    if (options && Array.isArray(options)) {
      const enabledOptions = options.filter(opt => opt.isEnabled);
      const enabledCount = enabledOptions.length;
      
      if (enabledCount === 0 && options.length > 0) {
        options[0].isEnabled = true;
        for (let i = 1; i < options.length; i++) {
          options[i].isEnabled = false;
        }
      } else if (enabledCount > 1) {
        const firstEnabled = enabledOptions[0];
        options.forEach(option => {
          option.isEnabled = option.Name === firstEnabled.Name;
        });
      }
    }
  });
  
  return correctedData;
}
```

### User Experience
- No validation errors shown to users
- Data is automatically corrected on the backend
- Users can submit any combination of options
- System ensures data integrity automatically

## Security Considerations

1. **Input Validation**: All inputs are validated and corrected before processing
2. **Data Integrity**: Ensures consistent state in database through auto-correction
3. **Error Handling**: No error responses for invalid data - always corrected
4. **Authorization**: Endpoint requires valid authentication token

## Maintenance

### Adding New Fields
To add new fields that require the same auto-correction:

1. Add the field to the DTO
2. Update the `validateAndCorrectProfileVisibilityOptions` function
3. Update the merge logic if needed
4. Add tests for the new field

### Modifying Auto-Correction Rules
To change auto-correction behavior:

1. Update the `validateAndCorrectProfileVisibilityOptions` function
2. Update service-level auto-correction logic
3. Update tests to reflect new rules
4. Update documentation

## Conclusion

This auto-correction system ensures data integrity and provides a seamless user experience by automatically correcting invalid states instead of showing validation errors. The implementation ensures that only one option can be enabled per field in profile visibility settings, with automatic correction handling all edge cases gracefully. 