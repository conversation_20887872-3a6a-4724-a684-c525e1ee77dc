import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';

export type NotificationPermissionDocument = NotificationPermission & Document;

export enum UserTypeEnum {
  AUDIENCE_MEMBER_FAN = 'audience_member_fan',
  CREATOR_MEMBER = 'creator_member',
  HIRER_EMPLOYER = 'hirer_employer',
  AFFILIATE_ORGANIZATION = 'affiliate_organization',
  BUSINESS_SCHOOL = 'business_school',
  TRAINING_FACILITY = 'training_facility',
  UNION = 'union',
}

@Schema({ timestamps: true, versionKey: false })
export class NotificationPermission {
  @Prop({ type: String, required: true })
  permissionName: string;

  @Prop({ 
    type: String, 
    enum: UserTypeEnum, 
    required: true 
  })
  userType: UserTypeEnum;
}

export const NotificationPermissionSchema = SchemaFactory.createForClass(NotificationPermission); 