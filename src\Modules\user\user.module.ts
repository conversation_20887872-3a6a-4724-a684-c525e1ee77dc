import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserSchema } from 'src/Models/user.schema';
import { UserController } from './controller/user.controller';
import { UserService } from './services/user.service';
import { CommonModule } from '../../common/common.module';
import { OtpLogSchema } from 'src/Models/otpLog.schema';
import { RecordSchema } from 'src/Models/record.schema';
import { PostSchema } from 'src/Models/post.schema';
import { LikeSchema } from 'src/Models/like.schema';
import { BookmarkSchema } from 'src/Models/bookmark.schema';
import { StreamChatService } from './services/streamChat.service';
import { FollowerInfoSchema } from 'src/Models/followerInfo.schema';
import { FeedbackSchema } from 'src/Models/feedback.schema';
import { SelfIdentifySchema } from 'src/Models/selfIdentify.schema';
import { JobListSchema } from 'src/Models/jobList.schema';
import { RecordJobSchema } from 'src/Models/recordJob.schema';
import { ConnectionInfoSchema } from 'src/Models/connectionInfo.schema';
import { UserSignupDataSchema } from 'src/Models/userSignupData.schema';
import {
  Notifications,
  NotificationsSchema,
} from 'src/Models/notification.schema';
import { ProfileViewsSchema } from 'src/Models/profileViews';
import { ProjectSchema } from 'src/Models/project';
import { ProjectService } from 'src/Modules/user/services/project.service';
import { CareerService } from 'src/Modules/user/services/career.service';
import { EducationSchema } from 'src/Models/education.schema';
import { LicenseCertificationSchema } from 'src/Models/licenseCertification.schema';
import { VolunteerExperienceSchema } from 'src/Models/volunteer-experience.schema';
import { HonorAndAwardSchema } from 'src/Models/honorsAndAwards.schema';
import { JobPostSchema } from 'src/Models/jobPost.schema';
import { JobPostService } from './services/job.service';
import { JobApplicationSchema } from 'src/Models/jobApplication.schema';
import { JobPostRepository } from 'src/Repositories/jobPost.repository';
import { ProjectRepository } from 'src/Repositories/project.repository';
import { LicenseCertificationRepository } from 'src/Repositories/licenceCertification.repository';
import { EducationRepository } from 'src/Repositories/education.repository';
import { VolunteerExperienceRepository } from 'src/Repositories/volunteer-experience.repository';
import { HonorAndAwardRepository } from 'src/Repositories/honorAndAward.repository';
import { UserRepository } from 'src/Repositories/user.repository';
import { EventSchema } from 'src/Models/event.schema';
import { EventRepository } from 'src/Repositories/event.repository';
import { EventService } from './services/event.service';
import { FeaturedSchema } from 'src/Models/featured.schema';
import { FeaturedRepository } from 'src/Repositories/featured.repository';
import { FeaturedService } from './services/featured.service';
import { ClientInfoSchema } from 'src/Models/clientsInfo.schema';
import { ExperienceRepository } from 'src/Repositories/experience.repository';
import { ExperienceSchema } from 'src/Models/experience.schema';
import { GroupSchema } from 'src/Models/group.schema';
import { PeopleSchema } from 'src/Models/peoples.schema';
import { Subscriber, SubscriberSchema } from 'src/Models/subscriber.schema';
import { WhoCanMessageValidatorService } from './helper/whoCanMessageValidator';
import { NotificationPermissionSchema } from 'src/Models/notificationPermission.schema';
import { UserNotificationPermissionSchema } from 'src/Models/userNotificationPermission.schema';
import { NotificationPermissionService } from './services/notificationPermission.service';
import { NotificationPermissionController } from './controller/notificationPermission.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'User', schema: UserSchema },
      { name: 'SelfIdentify', schema: SelfIdentifySchema },
      { name: 'OtpLog', schema: OtpLogSchema },
      { name: 'Record', schema: RecordSchema },
      { name: 'RecordJob', schema: RecordJobSchema },
      { name: 'Post', schema: PostSchema },
      { name: 'Like', schema: LikeSchema },
      { name: 'Bookmark', schema: BookmarkSchema },
      { name: 'FollowerInfo', schema: FollowerInfoSchema },
      { name: 'ConnectionInfo', schema: ConnectionInfoSchema },
      { name: 'Feedback', schema: FeedbackSchema },
      { name: 'JobList', schema: JobListSchema },
      { name: 'UserSignupData', schema: UserSignupDataSchema },
      { name: 'ProfileViews', schema: ProfileViewsSchema },
      { name: 'Project', schema: ProjectSchema },
      { name: 'Education', schema: EducationSchema },
      { name: 'LicenseCertification', schema: LicenseCertificationSchema },
      { name: 'VolunteerExperience', schema: VolunteerExperienceSchema },
      { name: 'Experience', schema: ExperienceSchema },
      { name: Notifications.name, schema: NotificationsSchema },
      { name: 'HonorAndAward', schema: HonorAndAwardSchema },
      { name: 'JobPost', schema: JobPostSchema },
      { name: 'JobApplication', schema: JobApplicationSchema },
      { name: 'Event', schema: EventSchema },
      { name: 'Featured', schema: FeaturedSchema },
      { name: 'ClientInfo', schema: ClientInfoSchema },
      { name: 'Group', schema: GroupSchema },
      { name: 'People', schema: PeopleSchema },
      { name: Subscriber.name, schema: SubscriberSchema },
      { name: 'NotificationPermission', schema: NotificationPermissionSchema },
      { name: 'UserNotificationPermission', schema: UserNotificationPermissionSchema },
    ]),
    CommonModule,
  ],
  controllers: [UserController, NotificationPermissionController],
  providers: [
    UserService,
    StreamChatService,
    ProjectService,
    CareerService,
    JobPostService,
    EventService,
    FeaturedService,
    JobPostRepository,
    ProjectRepository,
    LicenseCertificationRepository,
    EducationRepository,
    VolunteerExperienceRepository,
    ExperienceRepository,
    HonorAndAwardRepository,
    UserRepository,
    EventRepository,
    FeaturedRepository,
    WhoCanMessageValidatorService,
    NotificationPermissionService,
  ],
  exports: [WhoCanMessageValidatorService],
})
export class UserModule {}
