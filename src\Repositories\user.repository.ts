import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { userDocument } from 'src/Models/user.schema';
import { PostLabelEnum } from 'src/Models/post.schema';
import { StatusEnum } from 'src/Models/connectionInfo.schema';

@Injectable()
export class UserRepository extends AbstractRepository<userDocument> {
  constructor(@InjectModel('User') userModel: Model<userDocument>) {
    super(userModel);
  }

  async findProfileById(id: string, loggedInUserId: string) {
    return await this.model.aggregate([
      {
        $match: {
          _id: mongoose.Types.ObjectId.createFromHexString(id.toString()),
        },
      },
      {
        $unwind: {
          path: '$aboutYou.schoolTrainingFacility',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$aboutYou.schoolTrainingFacility.iAm',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'usersignupdatas',
          localField: 'aboutYou.schoolTrainingFacility.iAm.itemId',
          foreignField: '_id',
          as: 'aboutYouDetails',
        },
      },
      {
        $unwind: {
          path: '$aboutYouDetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $set: {
          'aboutYou.schoolTrainingFacility.iAm.itemId': {
            _id: '$aboutYouDetails._id',
            title: '$aboutYouDetails.title',
            slug: '$aboutYouDetails.slug',
            itemText: '$aboutYouDetails.itemText',
          },
        },
      },
      {
        $group: {
          _id: {
            itemText: '$aboutYou.schoolTrainingFacility.itemText',
            idNumber: '$aboutYou.schoolTrainingFacility.idNumber',
            memberIdCard: '$aboutYou.schoolTrainingFacility.memberIdCard',
            memberDoc: '$aboutYou.schoolTrainingFacility.memberDoc',
            userId: '$_id',
            iAmMember: '$iAmMember',
            businessOrganizationName: '$businessOrganizationName',
            firstName: '$firstName',
            causes: '$causes',
            lastName: '$lastName',
            middleName: '$middleName',
            additionalName: '$additionalName',
            showClients: '$showClients',
            fieldOfStudy: '$fieldOfStudy',
            socialActivities: '$socialActivities',
            offeredServices: '$offeredServices',
            professions: '$professions',
            position: '$position',
            industry: '$industry',
            pronouns: '$pronouns',
            headline: '$headline',
            userName: '$userName',
            city: '$city',
            state: '$state',
            country: '$country',
            email: '$email',
            isEmailVerified: '$isEmailVerified',
            isMembershipVerified: '$isMembershipVerified',
            hirerEmployerVerifiedStatus: '$hirerEmployerVerifiedStatus',
            isNotificationOn: '$isNotificationOn',
            isEmailOn: '$isEmailOn',
            isFakeAccount: '$isFakeAccount',
            isFunding: '$isFunding',
            businessAddress: '$businessAddress',
            countryCode: '$countryCode',
            phone: '$phone',
            socialMedia: '$socialMedia',
            contactInfo: '$contactInfo',
            signUpData: '$signUpData',
            publications: '$publications',
            profileImage: '$profileImage',
            analytics: '$analytics',
            userProfileAboutYou: '$userProfileAboutYou',
            followers: '$followers',
            following: '$following',
            connections: '$connections',
            accountVerified: '$accountVerified',
            createdAt: '$createdAt',
            updatedAt: '$updatedAt',
          },
          iAm: {
            $push: '$aboutYou.schoolTrainingFacility.iAm',
          },
          aboutYou: {
            $first: '$aboutYou',
          },
        },
      },
      {
        $group: {
          _id: '$_id.userId',
          iAmMember: {
            $first: '$_id.iAmMember',
          },
          businessOrganizationName: {
            $first: '$_id.businessOrganizationName',
          },
          userName: {
            $first: '$_id.userName',
          },
          firstName: {
            $first: '$_id.firstName',
          },
          middleName: {
            $first: '$_id.middleName',
          },
          fieldOfStudy: {
            $first: '$_id.fieldOfStudy',
          },
          showClients: {
            $first: '$_id.showClients',
          },
          businessAddress: {
            $first: '$_id.businessAddress',
          },
          isFunding: {
            $first: '$_id.isFunding',
          },
          isFakeAccount: {
            $first: '$_id.isFakeAccount',
          },
          isMembershipVerified: {
            $first: '$_id.isMembershipVerified',
          },
          professions: {
            $first: '$_id.professions',
          },
          socialActivities: {
            $first: '$_id.socialActivities',
          },
          offeredServices: {
            $first: '$_id.offeredServices',
          },
          additionalName: {
            $first: '$_id.additionalName',
          },
          position: {
            $first: '$_id.position',
          },
          industry: {
            $first: '$_id.industry',
          },
          pronouns: {
            $first: '$_id.pronouns',
          },
          headline: {
            $first: '$_id.headline',
          },
          lastName: {
            $first: '$_id.lastName',
          },
          city: {
            $first: '$_id.city',
          },
          state: {
            $first: '$_id.state',
          },
          country: {
            $first: '$_id.country',
          },
          email: {
            $first: '$_id.email',
          },
          isEmailVerified: {
            $first: '$_id.isEmailVerified',
          },
          hirerEmployerVerifiedStatus: {
            $first: '$_id.hirerEmployerVerifiedStatus',
          },
          isNotificationOn: {
            $first: '$_id.isNotificationOn',
          },
          isEmailOn: {
            $first: '$_id.isEmailOn',
          },
          countryCode: {
            $first: '$_id.countryCode',
          },
          phone: {
            $first: '$_id.phone',
          },
          socialMedia: {
            $first: '$_id.socialMedia',
          },
          contactInfo: {
            $first: '$_id.contactInfo',
          },
          signUpData: {
            $first: '$_id.signUpData',
          },
          profileImage: {
            $first: '$_id.profileImage',
          },
          followers: {
            $first: '$_id.followers',
          },
          following: {
            $first: '$_id.following',
          },
          connections: {
            $first: '$_id.connections',
          },
          analytics: {
            $first: '$_id.analytics',
          },
          userProfileAboutYou: {
            $first: '$_id.userProfileAboutYou',
          },
          accountVerified: {
            $first: '$_id.accountVerified',
          },
          createdAt: {
            $first: '$_id.createdAt',
          },
          updatedAt: {
            $first: '$_id.updatedAt',
          },
          schoolTrainingFacility: {
            $push: {
              itemText: '$_id.itemText',
              idNumber: '$_id.idNumber',
              memberIdCard: '$_id.memberIdCard',
              memberDoc: '$_id.memberDoc',
              iAm: '$iAm',
            },
          },
          causes: {
            $first: '$_id.causes',
          },
          publications: {
            $first: '$_id.publications',
          },
          aboutYou: {
            $first: '$aboutYou',
          },
        },
      },
      {
        $set: {
          'aboutYou.schoolTrainingFacility': '$schoolTrainingFacility',
        },
      },
      {
        $unset: 'schoolTrainingFacility',
      },
      {
        $unwind: {
          path: '$signUpData',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'usersignupdatas',
          localField: 'signUpData.itemId',
          foreignField: '_id',
          as: 'signUpDetails',
        },
      },
      {
        $unwind: {
          path: '$signUpDetails',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          'signUpData.itemId': {
            _id: '$signUpDetails._id',
            title: '$signUpDetails.title',
            slug: '$signUpDetails.slug',
            itemText: '$signUpDetails.itemText',
          },
        },
      },
      {
        $group: {
          _id: {
            _id: '$_id',
            iAmMember: '$iAmMember',
            firstName: '$firstName',
            lastName: '$lastName',
            userName: '$userName',
            middleName: '$middleName',
            additionalName: '$additionalName',
            position: '$position',
            industry: '$industry',
            pronouns: '$pronouns',
            headline: '$headline',
            businessOrganizationName: '$businessOrganizationName',
            city: '$city',
            state: '$state',
            country: '$country',
            showClients: '$showClients',
            socialActivities: '$socialActivities',
            fieldOfStudy: '$fieldOfStudy',
            offeredServices: '$offeredServices',
            professions: '$professions',
            analytics: '$analytics',
            profileImage: '$profileImage',
            userProfileAboutYou: '$userProfileAboutYou',
            email: '$email',
            isEmailVerified: '$isEmailVerified',
            hirerEmployerVerifiedStatus: '$hirerEmployerVerifiedStatus',
            isNotificationOn: '$isNotificationOn',
            isFakeAccount: '$isFakeAccount',
            isMembershipVerified: '$isMembershipVerified',
            isFunding: '$isFunding',
            businessAddress: '$businessAddress',
            isEmailOn: '$isEmailOn',
            countryCode: '$countryCode',
            phone: '$phone',
            socialMedia: '$socialMedia',
            contactInfo: '$contactInfo',
            publications: '$publications',
            accountVerified: '$accountVerified',
            followers: '$followers',
            following: '$following',
            connections: '$connections',
            causes: '$causes',
            aboutYou: {
              $cond: {
                if: {
                  $or: [
                    { $eq: ['$aboutYou.schoolTrainingFacility', null] },
                    { $eq: ['$aboutYou.schoolTrainingFacility', {}] },
                    {
                      $eq: [
                        '$aboutYou.schoolTrainingFacility.iAm.itemId',
                        null,
                      ],
                    },
                    {
                      $eq: ['$aboutYou.schoolTrainingFacility.iAm.itemId', {}],
                    },
                  ],
                },
                then: null,
                else: '$aboutYou',
              },
            },
          },
          signUpData: {
            $push: {
              isSelected: '$signUpData.isSelected',
              itemId: '$signUpData.itemId',
            },
          },
        },
      },
      {
        $project: {
          _id: '$_id._id',
          iAmMember: '$_id.iAmMember',
          firstName: '$_id.firstName',
          lastName: '$_id.lastName',
          analytics: '$_id.analytics',
          userProfileAboutYou: '$_id.userProfileAboutYou',
          userName: '$_id.userName',
          profileImage: '$_id.profileImage',
          middleName: '$_id.middleName',
          additionalName: '$_id.additionalName',
          position: '$_id.position',
          industry: '$_id.industry',
          pronouns: '$_id.pronouns',
          showClients: '$_id.showClients',
          headline: '$_id.headline',
          accountVerified: '$_id.accountVerified',
          hirerEmployerVerifiedStatus: '$_id.hirerEmployerVerifiedStatus',
          isMembershipVerified: '$_id.isMembershipVerified',
          businessAddress: '$_id.businessAddress',
          isFunding: '$_id.isFunding',
          isFakeAccount: '$_id.isFakeAccount',
          socialActivities: '$_id.socialActivities',
          fieldOfStudy: '$_id.fieldOfStudy',
          professions: '$_id.professions',
          offeredServices: '$_id.offeredServices',
          causes: '$_id.causes',
          businessOrganizationName: '$_id.businessOrganizationName',
          city: '$_id.city',
          state: '$_id.state',
          country: '$_id.country',
          email: '$_id.email',
          isEmailVerified: '$_id.isEmailVerified',
          isNotificationOn: '$_id.isNotificationOn',
          isEmailOn: '$_id.isEmailOn',
          countryCode: '$_id.countryCode',
          phone: '$_id.phone',
          socialMedia: '$_id.socialMedia',
          contactInfo: '$_id.contactInfo',
          aboutYou: '$_id.aboutYou',
          followers: '$_id.followers',
          following: '$_id.following',
          connections: '$_id.connections',
          publications: {
            $cond: {
              if: { $isArray: '$_id.publications' },
              then: { $arrayElemAt: ['$_id.publications', 0] },
              else: null,
            },
          },
          whoAreYou: {
            $filter: {
              input: '$signUpData',
              as: 'item',
              cond: {
                $eq: ['$$item.itemId.title', 'who_are_you'],
              },
            },
          },
          moreAboutYou: {
            $filter: {
              input: '$signUpData',
              as: 'item',
              cond: {
                $eq: ['$$item.itemId.title', 'more_about_you'],
              },
            },
          },
          openToPublic: {
            $filter: {
              input: '$signUpData',
              as: 'item',
              cond: {
                $eq: ['$$item.itemId.title', 'profile_open_to'],
              },
            },
          },
          whoCanMessage: {
            $filter: {
              input: '$signUpData',
              as: 'item',
              cond: {
                $eq: ['$$item.itemId.title', 'who_can_message'],
              },
            },
          },
        },
      },
      {
        $addFields: {
          'aboutYou.schoolTrainingFacility': {
            $filter: {
              input: '$aboutYou.schoolTrainingFacility',
              as: 'facility',
              cond: {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: '$$facility.iAm',
                        as: 'iAmItem',
                        cond: {
                          $ne: ['$$iAmItem.itemId', {}],
                        },
                      },
                    },
                  },
                  0,
                ],
              },
            },
          },
        },
      },
      //Highlighted Post
      {
        $lookup: {
          from: 'posts',
          let: { userId: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },

            {
              $addFields: {
                isSuccessCorner: false,
                // {
                //   $eq: ['$postLabel', PostLabelEnum.SUCCESS_CORNER],
                // },
                sortKey: '$totalReactions',
                // {
                //   $cond: {
                //     if: { $eq: ['$postLabel', PostLabelEnum.SUCCESS_CORNER] },
                //     then: '$createdAt',
                //     else: '$totalReactions',
                //   },
                // },
              },
            },
            {
              $addFields: {
                priority: {
                  $cond: {
                    if: { $eq: ['$isSuccessCorner', true] },
                    then: 1,
                    else: 2,
                  },
                },
              },
            },
            {
              $sort: { priority: 1, sortKey: -1 },
            },
            { $limit: 1 },
            {
              $lookup: {
                from: 'users',
                localField: 'fundraisers',
                foreignField: '_id',
                as: 'fundraisers',
                pipeline: [
                  {
                    $match: {
                      isFakeAccount: false,
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      businessOrganizationName: 1,
                      userName: 1,
                      profileImage: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,
                    },
                  },
                ],
              },
            },
            {
              $project: {
                group: 0,
                isSuccessCorner: 0,
                sortKey: 0,
                priority: 0,
              },
            },
            {
              $addFields: {
                isLike: {
                  $cond: {
                    if: {
                      $in: [
                        loggedInUserId,
                        {
                          $ifNull: [
                            {
                              $map: {
                                input: {
                                  $filter: {
                                    input: '$reactions',
                                    as: 'reaction',
                                    cond: { $eq: ['$$reaction.type', 'like'] },
                                  },
                                },
                                as: 'filteredReaction',
                                in: '$$filteredReaction.userId',
                              },
                            },
                            [],
                          ],
                        },
                      ],
                    },
                    then: true,
                    else: false,
                  },
                },
                currentUserReaction: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$reactions',
                            as: 'reaction',
                            cond: {
                              $eq: ['$$reaction.userId', loggedInUserId],
                            },
                          },
                        },
                        as: 'userReaction',
                        in: '$$userReaction.type',
                      },
                    },
                    0,
                  ],
                },
              },
            },
          ],
          as: 'highlightedPost',
        },
      },
      {
        $unwind: {
          path: '$highlightedPost',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'highlightedPost.userId',
          foreignField: '_id',
          as: 'highlightedPost.userId',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$highlightedPost.userId',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'highlightedPost.repostBy',
          foreignField: '_id',
          as: 'highlightedPost.repostBy',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
            {
              $addFields: {
                collaborators: {
                  $filter: {
                    input: '$collaborators',
                    as: 'collab',
                    cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
                  },
                },
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'highlightedPost.collaborators.id',
          foreignField: '_id',
          as: 'highlightedPost.collaborators',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
                'highlightedPost.collaborators.status': StatusEnum.ACCEPT,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'highlightedPost.taggedPeople',
          foreignField: '_id',
          as: 'highlightedPost.taggedPeople',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$highlightedPost.repostBy',
          preserveNullAndEmptyArrays: true,
        },
      },
      // Page Post
      {
        $lookup: {
          from: 'posts',
          let: { userId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$userId', '$$userId'] },
                $or: [{ repostBy: { $exists: false } }, { repostBy: null }],
              },
            },

            { $project: { group: 0 } },
            {
              $lookup: {
                from: 'users',
                localField: 'fundraisers',
                foreignField: '_id',
                as: 'fundraisers',
                pipeline: [
                  {
                    $match: {
                      isFakeAccount: false,
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      businessOrganizationName: 1,
                      userName: 1,
                      profileImage: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,
                    },
                  },
                ],
              },
            },
            {
              $addFields: {
                isLike: {
                  $cond: {
                    if: {
                      $in: [
                        loggedInUserId,
                        {
                          $ifNull: [
                            {
                              $map: {
                                input: {
                                  $filter: {
                                    input: '$reactions',
                                    as: 'reaction',
                                    cond: { $eq: ['$$reaction.type', 'like'] },
                                  },
                                },
                                as: 'filteredReaction',
                                in: '$$filteredReaction.userId',
                              },
                            },
                            [],
                          ],
                        },
                      ],
                    },
                    then: true,
                    else: false,
                  },
                },
                currentUserReaction: {
                  $arrayElemAt: [
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$reactions',
                            as: 'reaction',
                            cond: {
                              $eq: ['$$reaction.userId', loggedInUserId],
                            },
                          },
                        },
                        as: 'userReaction',
                        in: '$$userReaction.type',
                      },
                    },
                    0,
                  ],
                },
              },
            },
            {
              $addFields: {
                collaborators: {
                  $filter: {
                    input: '$collaborators',
                    as: 'collab',
                    cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
                  },
                },
              },
            },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
            {
              $project: {
                group: 0,
              },
            },
          ],
          as: 'pagePost',
        },
      },
      {
        $unwind: {
          path: '$pagePost',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'pagePost.userId',
          foreignField: '_id',
          as: 'pagePost.userId',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$pagePost.userId',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'pagePost.repostBy',
          foreignField: '_id',
          as: 'pagePost.repostBy',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$pagePost.repostBy',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'pagePost.collaborators.id',
          foreignField: '_id',
          as: 'pagePost.collaborators',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
                'pagePost.collaborators.status': StatusEnum.ACCEPT,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'pagePost.taggedPeople',
          foreignField: '_id',
          as: 'pagePost.taggedPeople',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          'highlightedPost.collaborators': {
            $cond: {
              if: { $gt: [{ $size: '$highlightedPost.collaborators' }, 0] },
              then: '$highlightedPost.collaborators',
              else: '$$REMOVE',
            },
          },
          'highlightedPost.taggedPeople': {
            $cond: {
              if: { $gt: [{ $size: '$highlightedPost.taggedPeople' }, 0] },
              then: '$highlightedPost.taggedPeople',
              else: '$$REMOVE',
            },
          },
          'pagePost.collaborators': {
            $cond: {
              if: { $gt: [{ $size: '$pagePost.collaborators' }, 0] },
              then: '$pagePost.collaborators',
              else: '$$REMOVE',
            },
          },
          'pagePost.taggedPeople': {
            $cond: {
              if: { $gt: [{ $size: '$pagePost.taggedPeople' }, 0] },
              then: '$pagePost.taggedPeople',
              else: '$$REMOVE',
            },
          },
        },
      },
      {
        $addFields: {
          pagePost: {
            $cond: {
              if: {
                $or: [{ $gt: [{ $size: { $objectToArray: '$pagePost' } }, 0] }],
              },
              then: '$pagePost',
              else: null,
            },
          },
          highlightedPost: {
            $cond: {
              if: {
                $or: [
                  {
                    $gt: [{ $size: { $objectToArray: '$highlightedPost' } }, 0],
                  },
                ],
              },
              then: '$highlightedPost',
              else: null,
            },
          },
        },
      },
      {
        $lookup: {
          from: 'projects',
          let: { userId: '$_id', skills: '$userProfileAboutYou.skills' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
            {
              $addFields: {
                skills: '$$skills',
              },
            },
            {
              $lookup: {
                from: 'users',
                localField: 'affiliateOrganizations',
                foreignField: '_id',
                as: 'affiliateOrganizations',
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      userName: 1,
                      profileImage: 1,
                      businessOrganizationName: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,
                    },
                  },
                ],
              },
            },
          ],
          as: 'projects',
        },
      },
      {
        $unwind: {
          path: '$projects',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'projects.collaborators',
          foreignField: '_id',
          as: 'projects.collaborators',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'educations',
          let: { userId: '$_id', skills: '$userProfileAboutYou.skills' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $project: { userId: 0 } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
            {
              $lookup: {
                from: 'users',
                localField: 'school',
                foreignField: '_id',
                as: 'school',
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      userName: 1,
                      profileImage: 1,
                      businessOrganizationName: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,
                      'userProfileAboutYou.skills': 1,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$school',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                skills: '$$skills',
              },
            },
          ],
          as: 'educations',
        },
      },
      {
        $unwind: {
          path: '$educations',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'volunteerexperiences',
          let: { userId: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $project: { userId: 0 } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
            {
              $lookup: {
                from: 'users',
                localField: 'organization',
                foreignField: '_id',
                as: 'organization',
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      userName: 1,
                      profileImage: 1,
                      businessOrganizationName: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,
                    },
                  },
                ],
              },
            },
          ],
          as: 'volunteerexperiences',
        },
      },
      {
        $unwind: {
          path: '$volunteerexperiences',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'experiences',
          let: { userId: '$_id', skills: '$userProfileAboutYou.skills' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $addFields: { skills: '$$skills' } },
            { $project: { userId: 0 } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
          ],
          as: 'experiences',
        },
      },
      {
        $unwind: {
          path: '$experiences',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'licensecertifications',
          let: { userId: '$_id', skills: '$userProfileAboutYou.skills' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $addFields: { skills: '$$skills' } },
            { $project: { userId: 0 } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
          ],
          as: 'licensecertifications',
        },
      },
      {
        $unwind: {
          path: '$licensecertifications',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'honorandawards',
          let: { userId: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $project: { userId: 0 } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
          ],
          as: 'honorAndAwards',
        },
      },
      {
        $unwind: {
          path: '$honorAndAwards',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'featureds',
          let: { userId: '$_id' },
          pipeline: [
            { $match: { $expr: { $eq: ['$userId', '$$userId'] } } },
            { $project: { userId: 0 } },
            { $sort: { createdAt: -1 } },
            { $limit: 1 },
          ],
          as: 'featureds',
        },
      },
      {
        $unwind: {
          path: '$featureds',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
  }

  async updateSkills(userId: string, skills: string[]) {
    const user = await this.model.findById(userId);

    if (!user) {
      throw new Error('User not found');
    }

    user.userProfileAboutYou.skills = skills;

    await user.save();

    return user.userProfileAboutYou.skills;
  }

  async updateFieldOfStudy(userId: string, fieldOfStudy: string[]) {
    const user = await this.model.findById(userId);

    if (!user) {
      throw new Error('User not found');
    }

    const existingfieldOfStudy = user.fieldOfStudy || [];

    const newfieldOfStudy = fieldOfStudy.filter(
      (field) => !existingfieldOfStudy.includes(field),
    );

    if (newfieldOfStudy.length === 0) {
      return user;
    }

    existingfieldOfStudy.push(...fieldOfStudy);

    user.fieldOfStudy = existingfieldOfStudy;

    return await user.save();
  }
}
