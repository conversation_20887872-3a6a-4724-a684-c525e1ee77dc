const mongoose = require('mongoose');
const XLSX = require('xlsx');
const fs = require('fs');
require('dotenv').config();

// NOTE: This script has been modified to SKIP existing organizations instead of deleting them.
// It will only insert new organizations that don't already exist in the database.
// This prevents duplicate data and preserves existing organization records.
// 
// IMPORTANT: This script now includes a cleanup step that will DELETE all incorrectly 
// categorized subcategories and organizations before creating new ones with proper categorization.
// Make sure to backup your data before running this script.

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://pepliadmin:<EMAIL>/pepli_db';

// Define schemas
const userSchema = new mongoose.Schema({
  businessOrganizationName: String,
  iAmMember: String,
  isFakeAccount: Boolean,
  isEmailVerified: <PERSON><PERSON><PERSON>,
  isMembershipVerified: Boolean,
  hirerEmployerVerifiedStatus: String,
  phone: String,
  signUpData: Array,
  aboutYou: {
    visualArts: [String],
    performingArts: [String],
    dance: [String],
    acting: [String],
    music: [String],
    filmMedia: [String],
    design: [String],
    literaryArts: [String],
    crafts: [String],
    appliedArts: [String],
    other: [String],
    union: [String],
    affiliateOrganization: [String],
    affiliateBusiness: [String],
    schoolTrainingFacility: [String]
  },
  profileImage: String,
  businessAddress: String,
  followers: Number,
  following: Number,
  connections: Number,
  showClients: Boolean,
  isFunding: Boolean,
  isNotificationOn: Boolean,
  isEmailOn: Boolean,
  posts: Number,
  professions: [String],
  fieldOfStudy: [String],
  socialActivities: [String],
  offeredServices: [String],
  causes: [String],
  accountVerified: Boolean,
  contactInfo: [String],
  publications: [String],
  affiliatePages: [String],
  isDeactivated: Boolean
}, { timestamps: true, versionKey: false });

const userSignupDataSchema = new mongoose.Schema({
  title: String,
  parentSlug: [String],
  itemText: String,
  slug: String,
  selectionType: String,
  subCategory: String
}, { timestamps: true, versionKey: false });

const User = mongoose.model('User', userSchema);
const UserSignupData = mongoose.model('UserSignupData', userSignupDataSchema, 'usersignupdatas');

// Main migration function
async function migrateOrganizationsFromExcel(excelFilePath) {
  try {
    console.log('🚀 Starting organization migration from Excel...\n');
    
    // Step 1: Read Excel file
    console.log('📖 Step 1: Reading Excel file...');
    const excelData = readExcelFile(excelFilePath);
    
    // Store Excel data globally for helper functions
    global.excelData = excelData;
    
    console.log(`✅ Excel data loaded: ${excelData.length} organizations`);
    
    // Step 2: Ensure main categories exist
    console.log('\n🏗️  Step 2: Ensuring main categories exist...');
    const mainCategories = await ensureMainCategoriesExist();
    console.log(`✅ Main categories ready: ${Object.keys(mainCategories).length}`);
    
    // Step 3: Ensure subcategories exist and auto-add new titles to enum
    console.log('\n📋 Step 3: Ensuring subcategories exist...');
    const subcategoryMap = await ensureSubcategoriesExist(mainCategories);
    console.log(`✅ Subcategories ready: ${Object.keys(subcategoryMap).length}`);
    
    // Step 4: Create organizations
    console.log('\n🏢 Step 4: Creating organizations...');
    const organizations = await createOrganizations(excelData, subcategoryMap);
    console.log(`✅ Organizations created: ${organizations.length}`);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   • Main Categories: ${Object.keys(mainCategories).length}`);
    console.log(`   • Subcategories: ${Object.keys(subcategoryMap).length}`);
    console.log(`   • Organizations: ${organizations.length}`);
    
    return {
      mainCategories: Object.keys(mainCategories).length,
      subcategories: Object.keys(subcategoryMap).length,
      organizations: organizations.length
    };
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    throw error;
  } finally {
    // Clean up global data
    delete global.excelData;
  }
}

// Clean up incorrectly categorized data
async function cleanupIncorrectData() {
  try {
    console.log('  🗑️  Deleting incorrectly categorized subcategories...');
    
    // Delete all subcategories with incorrect title pattern
    const deleteSubcategoriesResult = await UserSignupData.deleteMany({
      title: { $regex: /^.*_subcategory$/ },
      subCategory: 'sub_type'
    });
    console.log(`  ✅ Deleted ${deleteSubcategoriesResult.deletedCount} incorrectly categorized subcategories`);
    
    console.log('  🗑️  Deleting organizations with incorrect categorization...');
    
    // Delete organizations that were created with incorrect subcategory references
    const deleteOrganizationsResult = await User.deleteMany({
      iAmMember: 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility',
      businessOrganizationName: { $exists: true, $ne: null, $ne: '' }
    });
    console.log(`  ✅ Deleted ${deleteOrganizationsResult.deletedCount} incorrectly categorized organizations`);
    
    console.log('  ✅ Cleanup completed');
    
  } catch (error) {
    console.error('  ❌ Error during cleanup:', error);
    throw error;
  }
}

// Helper function to get subcategories from Excel for a specific main category
function getExcelSubcategories(mainCategorySlug) {
  const excelData = global.excelData || [];
  const subcategories = new Set();
  
  excelData.forEach(row => {
    const rowMainCategorySlug = mapExcelCategoryToSlug(row.mainCategory);
    if (rowMainCategorySlug === mainCategorySlug) {
      subcategories.add(row.subCategory.trim());
    }
  });
  
  return Array.from(subcategories);
}

// Create organizations
async function createOrganizations(excelData, subcategoryMap) {
  console.log('\n🏢 Creating organizations...');
  
  const createdOrganizations = [];
  const skippedCount = 0;
  
  for (const row of excelData) {
    try {
      const mainCategorySlug = mapExcelCategoryToSlug(row.mainCategory);
      const subcategoryName = row.subCategory.trim();
      const organizationName = row.organization.trim();
      
      const subcategoryKey = `${mainCategorySlug}_${subcategoryName.toLowerCase().replace(/\s+/g, '_')}`;
      const subcategory = subcategoryMap[subcategoryKey];
      
      if (!subcategory) {
        console.log(`⚠️  Warning: Subcategory not found for ${subcategoryName}, skipping organization ${organizationName}`);
        skippedCount++;
        continue;
      }
      
      const organizationSlug = `${subcategory.slug}_${organizationName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
      
      // Check if organization already exists
      const existingOrg = await UserSignupData.findOne({
        slug: organizationSlug,
        subCategory: "organization"
      });
      
      if (existingOrg) {
        console.log(`⏭️  Skipping ${organizationName} - already exists`);
        skippedCount++;
        continue;
      }
      
      // Auto-generate organization title based on subcategory
      let organizationTitle;
      if (subcategory.title === 'union_subcategory') {
        organizationTitle = 'union_organization';
      } else if (subcategory.title === 'affiliate_org_subcategory') {
        organizationTitle = 'affiliate_org_organization';
      } else if (subcategory.title === 'affiliate_business_subcategory') {
        organizationTitle = 'affiliate_business_organization';
      } else if (subcategory.title === 'school_training_subcategory') {
        organizationTitle = 'school_training_organization';
      } else {
        // Auto-generate for new subcategory types
        organizationTitle = `${subcategory.title.replace('_subcategory', '')}_organization`;
        console.log(`  🔧 Auto-generated organization title: ${organizationTitle} for ${subcategory.title}`);
      }
      
      // Create organization entry
      const organization = new UserSignupData({
        title: organizationTitle,
        parentSlug: [subcategory.slug],
        itemText: organizationName,
        slug: organizationSlug,
        selectionType: 'only',  // Changed from 'multiple' to 'only'
        subCategory: 'organization'
      });
      
      await organization.save();
      createdOrganizations.push(organization);
      console.log(`  ✅ Created: ${organizationName} -> ${subcategory.slug}`);
      
    } catch (error) {
      console.error(`❌ Error creating organization ${row.organization}:`, error.message);
    }
  }
  
  console.log(`\n📊 Organizations created: ${createdOrganizations.length}`);
  console.log(`⏭️  Organizations skipped: ${skippedCount}`);
  
  return createdOrganizations;
}

// Ensure subcategories exist and auto-add new titles to enum
async function ensureSubcategoriesExist(mainCategories) {
  console.log('\n📋 Ensuring subcategories exist...');
  
  const subcategoryMap = {};
  
  for (const [mainCategorySlug, mainCategory] of Object.entries(mainCategories)) {
    console.log(`\n🔍 Processing subcategories for ${mainCategory.itemText} (${mainCategorySlug})`);
    
    // Get subcategories from Excel for this main category
    const excelSubcategories = getExcelSubcategories(mainCategorySlug);
    
    for (const subcategoryName of excelSubcategories) {
      const subcategorySlug = subcategoryName.toLowerCase().replace(/\s+/g, '_');
      
      // Determine the correct title based on main category
      let subcategoryTitle;
      switch (mainCategorySlug) {
        case 'union_1':
          subcategoryTitle = 'union_subcategory';
          break;
        case 'affiliate_organization_1':
          subcategoryTitle = 'affiliate_org_subcategory';
          break;
        case 'affiliate_business_1':
          subcategoryTitle = 'affiliate_business_subcategory';
          break;
        case 'school_training_facility_1':
          subcategoryTitle = 'school_training_subcategory';
          break;
        default:
          // Auto-generate title for new main categories
          subcategoryTitle = `${mainCategorySlug.replace('_1', '')}_subcategory`;
          console.log(`  🔧 Auto-generated title: ${subcategoryTitle} for ${mainCategorySlug}`);
      }
      
      let subcategory = await UserSignupData.findOne({
        title: subcategoryTitle,
        slug: subcategorySlug
      });
      
      if (!subcategory) {
        console.log(`  ➕ Creating subcategory: ${subcategoryName} under ${mainCategorySlug}`);
        
        const subcategoryData = {
          title: subcategoryTitle,
          parentSlug: [mainCategorySlug],
          itemText: subcategoryName,
          slug: subcategorySlug,
          selectionType: 'only',  // Changed from 'multiple' to 'only'
          subCategory: 'sub_type'
        };
        
        subcategory = new UserSignupData(subcategoryData);
        await subcategory.save();
        console.log(`  ✅ Subcategory created with ID: ${subcategory._id}`);
      } else {
        console.log(`  ✅ Subcategory exists: ${subcategoryName}`);
      }
      
      subcategoryMap[`${mainCategorySlug}_${subcategorySlug}`] = subcategory;
    }
  }
  
  return subcategoryMap;
}

// Ensure main categories exist
async function ensureMainCategoriesExist() {
  const mainCategoryData = [
    {
      title: 'who_are_you',
      itemText: 'Union (an Organization / Not an individual member)',
      slug: 'union_1',
      subCategory: 'main_category',
      selectionType: 'only'
    },
    {
      title: 'who_are_you',
      itemText: 'Affiliate (Organization)',
      slug: 'affiliate_organization_1',
      subCategory: 'main_category',
      selectionType: 'only'
    },
    {
      title: 'who_are_you',
      itemText: 'Affiliate (Business)',
      slug: 'affiliate_business_1',
      subCategory: 'main_category',
      selectionType: 'only'
    },
    {
      title: 'who_are_you',
      itemText: 'School / Training Facility',
      slug: 'school_training_facility_1',
      subCategory: 'main_category',
      selectionType: 'only'
    }
  ];

  const mainCategories = {};
  
  for (const categoryData of mainCategoryData) {
    let category = await UserSignupData.findOne({
      title: categoryData.title,
      slug: categoryData.slug
    });
    
    if (!category) {
      console.log(`  ➕ Creating main category: ${categoryData.itemText}`);
      category = new UserSignupData(categoryData);
      await category.save();
      console.log(`  ✅ Main category created with ID: ${category._id}`);
    } else {
      console.log(`  ✅ Main category exists: ${categoryData.itemText}`);
    }
    
    mainCategories[categoryData.slug] = category;
  }
  
  return mainCategories;
}

// Helper functions
function mapExcelCategoryToSlug(excelCategory) {
  const mapping = {
    // Unions
    'Unions': 'union_1',
    'Union': 'union_1',
    
    // Affiliate Organizations
    'Affiliate Organizations': 'affiliate_organization_1',
    'Affiliate Organization': 'affiliate_organization_1',
    'Organizations': 'affiliate_organization_1',
    'Organization': 'affiliate_organization_1',
    
    // Affiliate Businesses
    'Affiliate Business': 'affiliate_business_1',
    'Affiliate Businesses': 'affiliate_business_1',
    'Businesses': 'affiliate_business_1',
    'Business': 'affiliate_business_1',
    
    // Schools and Training
    'School/Training': 'school_training_facility_1',
    'Schools/Training': 'school_training_facility_1',
    'Training': 'school_training_facility_1',
    'Schools': 'school_training_facility_1',
    'Schools / Training': 'school_training_facility_1',
    'Schools / Training Facilities': 'school_training_facility_1',
    'School / Training Facilities': 'school_training_facility_1',
    'Schools / Training in Arts & Entertainment': 'school_training_facility_1',
    'School / Training in Arts & Entertainment': 'school_training_facility_1'
  };
  
  const normalizedCategory = excelCategory.toString().trim();
  const mappedSlug = mapping[normalizedCategory];
  
  if (!mappedSlug) {
    console.log(`⚠️  Warning: Unknown category "${normalizedCategory}", defaulting to union_1`);
    return 'union_1';
  }
  
  return mappedSlug;
}

function normalizeSlug(text) {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .trim();
}

// Excel reading function
function readExcelFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel file not found: ${filePath}`);
    }

    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    const headers = jsonData[0];
    const dataRows = jsonData.slice(1);
    
    // Validate headers
    const expectedHeaders = ['Main Category', 'Sub Category', 'Organization'];
    const headerValidation = expectedHeaders.every(header => 
      headers.some(h => h && h.toString().toLowerCase().includes(header.toLowerCase()))
    );
    
    if (!headerValidation) {
      throw new Error(`Invalid headers. Expected: ${expectedHeaders.join(', ')}. Found: ${headers.join(', ')}`);
    }
    
    // Map data to expected format
    const organizations = dataRows
      .filter(row => row.length >= 3 && row[0] && row[1] && row[2])
      .map(row => ({
        mainCategory: row[0].toString().trim(),
        subCategory: row[1].toString().trim(),
        organization: row[2].toString().trim()
      }));
    
    console.log(`📊 Read ${organizations.length} organizations from Excel file`);
    return organizations;
    
  } catch (error) {
    console.error('❌ Error reading Excel file:', error.message);
    throw error;
  }
}

// Run migration
if (require.main === module) {
  const excelFilePath = process.argv[2];
  
  if (!excelFilePath) {
    console.log('Usage: node migrate_organizations_from_excel.js <excel_file_path>');
    console.log('Example: node migrate_organizations_from_excel.js organizations.xlsx');
    process.exit(0);
  }

  migrateOrganizationsFromExcel(excelFilePath)
    .then(() => {
      console.log('\n🎉 Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateOrganizationsFromExcel };
