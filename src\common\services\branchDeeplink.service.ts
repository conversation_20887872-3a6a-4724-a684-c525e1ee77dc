import { HttpException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

const sdk = require('api')('@developers-hub/v5.8#3q5mug24lo68ovlb');

@Injectable()
export class BranchDeeplinkService {
  constructor(private readonly configService: ConfigService) {}

  async generateDeepLink(post_Id, datas: any, isPost = true): Promise<string> {
    try {
      let response;
      if (isPost) {
        response = await sdk.createDeepLinkUrl({
          branch_key: this.configService.get<string>('BRANCH_KEY'),
          branch_secret: this.configService.get<string>('BRANCH_SECRET'),
          duration: 1440,
          alias: post_Id,
          data: {
            postId: post_Id,
            $og_title: datas.title,
            $og_description: datas.description,
            // Remove the image URL if you don't have access
            // $og_image_url: datas.image_url,
          },
        });
      } else {
        response = await sdk.createDeepLinkUrl({
          branch_key: this.configService.get<string>('BRANCH_KEY'),
          branch_secret: this.configService.get<string>('BRANCH_SECRET'),
          duration: 1440,
          alias: `group/${post_Id}`,
          data: {
            groupId: post_Id,
            $og_title: datas.title,
            $og_description: datas.description,
            // Remove the image URL if you don't have access
            // $og_image_url: datas.image_url,
          },
        });
      }

      //response.data.url contains the generated deep link
      return response.data.url;
    } catch (error) {
      console.error(error);
      throw new HttpException('Failed to generate deep link', 400, {
        cause: error,
      });
    }
  }
}
