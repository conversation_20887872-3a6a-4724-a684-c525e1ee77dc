# Deactivate Account API Documentation

## Overview
This API allows users to deactivate or reactivate their accounts. When an account is deactivated, the `isDeactivated` flag is set to `true` in the user collection, and this flag is included in the login response.

## API Endpoint

### Deactivate/Reactivate Account
- **URL**: `POST /user/deactivate-account`
- **Method**: `POST`
- **Authentication**: Required (JWT Token)

### Request Body
```json
{
  "isDeactivated": true
}
```

### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| isDeactivated | boolean | Yes | Set to `true` to deactivate account, `false` to reactivate |

### Response
#### Success Response (200 OK)
```json
{
  "status": true,
  "message": "Account deactivated successfully",
  "data": {
    "isDeactivated": true
  }
}
```

#### Error Response (404 Not Found)
```json
{
  "status": false,
  "message": "User not found",
  "data": null
}
```

#### Error Response (500 Internal Server Error)
```json
{
  "status": false,
  "message": "Internal server error",
  "data": null
}
```

## Login Response Update

The login API (`POST /user/signin`) now includes the `isDeactivated` field in the response:

```json
{
  "status": true,
  "message": "Sign in successful",
  "data": {
    "_id": "user_id",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "isDeactivated": false,
    // ... other user fields
    "token": "jwt_token"
  }
}
```

## Database Schema Update

The `User` schema has been updated to include the `isDeactivated` field:

```typescript
@Prop({ type: Boolean, default: false })
isDeactivated: boolean;
```

## Usage Examples

### Deactivate Account
```bash
curl -X POST http://localhost:3000/user/deactivate-account \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"isDeactivated": true}'
```

### Reactivate Account
```bash
curl -X POST http://localhost:3000/user/deactivate-account \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"isDeactivated": false}'
```

## Implementation Details

### Files Modified
1. **User Schema** (`src/Models/user.schema.ts`)
   - Added `isDeactivated` field with default value `false`

2. **User DTOs** (`src/Modules/user/dtos/user.dtos.ts`)
   - Added `DeactivateAccountDto` class for request validation

3. **User Service** (`src/Modules/user/services/user.service.ts`)
   - Added `deactivateAccount` method
   - Updated `signIn` method to include `isDeactivated` in response

4. **User Controller** (`src/Modules/user/controller/user.controller.ts`)
   - Added `deactivate-account` endpoint
   - Added `DeactivateAccountDto` import

### Security Considerations
- The API requires authentication via JWT token
- Users can only deactivate/reactivate their own account
- The `isDeactivated` flag is included in login responses for frontend handling

### Frontend Integration
The frontend should check the `isDeactivated` flag in the login response and handle the UI accordingly:
- If `isDeactivated: true`, show appropriate messaging or redirect to reactivation flow
- If `isDeactivated: false`, proceed with normal application flow 