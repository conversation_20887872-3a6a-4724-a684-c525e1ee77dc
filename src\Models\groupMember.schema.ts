import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';
import { Group } from './group.schema';

export type GroupMemberDocument = GroupMember & Document;

@Schema({ timestamps: true, versionKey: false })
export class GroupMember {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Group' })
  group: Group;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  member: User;

  @Prop({ type: Boolean, default: false })
  isBlocked: boolean;
}

export const GroupMemberSchema = SchemaFactory.createForClass(GroupMember);
