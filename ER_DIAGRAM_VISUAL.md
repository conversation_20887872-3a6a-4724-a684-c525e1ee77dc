# Pepli Node Database - Visual ER Diagram

## Complete Database Schema Visualization

```mermaid
erDiagram
    %% Core User Entity
    User {
        ObjectId _id PK
        String userName UK
        String email UK
        String firstName
        String lastName
        String password
        String profileImage
        String position
        String industry
        String headline
        Number followers
        Number following
        Number connections
        Number posts
        Boolean isEmailVerified
        Boolean accountVerified
        Object socialMedia
        Object aboutYou
        Object analytics
        Array publications
        Array affiliatePages
        Array professions
        Array fieldOfStudy
        Array skills
        Array offeredServices
        Array causes
    }

    %% Social Networking Relationships
    ConnectionInfo {
        ObjectId _id PK
        ObjectId userId FK
        ObjectId connectionWithId FK
        String status
        Date createdAt
        Date updatedAt
    }

    FollowerInfo {
        ObjectId _id PK
        ObjectId followerId FK
        ObjectId followingId FK
        String status
        Date createdAt
        Date updatedAt
    }

    People {
        ObjectId _id PK
        ObjectId parentUserId FK
        ObjectId childUserId FK
        String status
        String email
        String idNumber
        String document
        Boolean isAlumni
        Boolean isStudent
        Date createdAt
        Date updatedAt
    }

    ClientInfo {
        ObjectId _id PK
        ObjectId userId FK
        ObjectId clientId FK
        String status
        Date createdAt
        Date updatedAt
    }

    %% Content Management
    Post {
        ObjectId _id PK
        String caption
        String title
        Array taggedCaptionUsers
        String repostCaption
        Array repostTaggedCaptionUsers
        Object location
        Array media
        String shareableLink
        Array collaborators
        Array whoCanComment
        Boolean isTurnOffComment
        Boolean isHideLikeViews
        Boolean isDisableRepostShare
        String postLabel
        Array reactions
        Array taggedPeople
        Array fundraisers
        Array taggedProduct
        Array aiLabels
        ObjectId userId FK
        ObjectId repostBy FK
        ObjectId post FK
        ObjectId group FK
        Object reactionCounts
        Number totalBookmarks
        Number totalReactions
        Number totalComments
        Number repostCount
        Date createdAt
        Date updatedAt
    }

    Comment {
        ObjectId _id PK
        String comment
        Boolean isEdited
        ObjectId postId FK
        ObjectId parentId FK
        ObjectId userId FK
        Array taggedUsers
        Number totalReactions
        Object reactionCounts
        Array reactions
        Date createdAt
        Date updatedAt
    }

    Like {
        ObjectId _id PK
        ObjectId postId FK
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    Bookmark {
        ObjectId _id PK
        ObjectId postId FK
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    Story {
        ObjectId _id PK
        String mediaType
        String url
        String thumbUrl
        String shareableLink
        Array likeUsers
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    %% Group Management
    Group {
        ObjectId _id PK
        String name
        String description
        String joinPurpose
        String coverPhoto
        String privacy
        String visibility
        Array groupRule
        String allowPosting
        String shareableLink
        ObjectId createdBy FK
        Number posts
        Date createdAt
        Date updatedAt
    }

    GroupMember {
        ObjectId _id PK
        ObjectId group FK
        ObjectId member FK
        Boolean isBlocked
        Date createdAt
        Date updatedAt
    }

    GroupRule {
        ObjectId _id PK
        String rule
        Date createdAt
        Date updatedAt
    }

    %% Professional Profile
    Education {
        ObjectId _id PK
        ObjectId school FK
        String degree
        String fieldOfStudy
        Date startDate
        Date endDate
        Boolean isAlum
        String activities
        String description
        Array skills
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    Experience {
        ObjectId _id PK
        String title
        String companyName
        String employmentType
        Boolean isCurrentlyWorking
        Date startDate
        Date endDate
        String location
        String locationType
        String description
        Array skills
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    Project {
        ObjectId _id PK
        String name
        String description
        Array skills
        Array affiliateOrganizations
        Array collaborators
        Boolean currentlyWorking
        Date startDate
        Date endDate
        String link
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    Featured {
        ObjectId _id PK
        String title
        String url
        String description
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    %% Job & Career
    JobPost {
        ObjectId _id PK
        String title
        String company
        String description
        String workplaceType
        String location
        String workType
        ObjectId userId FK
        Array applications
        Date createdAt
        Date updatedAt
    }

    JobApplication {
        ObjectId _id PK
        ObjectId jobId FK
        ObjectId userId FK
        Date appliedDate
        String description
        String resume
        String status
        Date createdAt
        Date updatedAt
    }

    JobList {
        ObjectId _id PK
        String title
        String description
        String category
        Array requirements
        Array benefits
        String location
        String salary
        String employmentType
        Date createdAt
        Date updatedAt
    }

    %% Events & Activities
    Event {
        ObjectId _id PK
        String title
        String organizationName
        String description
        Array demographics
        Boolean isAccomodationForDisability
        Date startDate
        Date endDate
        Date schedule
        Boolean isRecurring
        String recurringType
        String reminder
        Boolean isInPerson
        Boolean isVertualOrRemote
        String location
        String meetingLink
        Boolean isThereFee
        Number feeAmount
        String currency
        Array paymentMethods
        String bioOfOrganizer
        Date expirationDate
        Object organizer
        Array whoCanFind
        String poster
        String sharableLink
        Array collaborators
        Array participants
        Date createdAt
        Date updatedAt
    }

    %% Notifications & Communication
    Notification {
        ObjectId _id PK
        String title
        String notificationMessage
        String notificationType
        String redirectionType
        Boolean isRead
        ObjectId sender FK
        ObjectId receiver FK
        ObjectId group FK
        ObjectId postId FK
        ObjectId storyId FK
        ObjectId connectionId FK
        ObjectId clientId FK
        ObjectId memberId FK
        String action
        Boolean isGroupMember
        Date createdAt
        Date updatedAt
    }

    %% System & Support
    Feedback {
        ObjectId _id PK
        String category
        String title
        String description
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    ContactUs {
        ObjectId _id PK
        String name
        String email
        String phone
        String message
        Date createdAt
        Date updatedAt
    }

    OtpLog {
        ObjectId _id PK
        String otp
        String email
        String phone
        Boolean isMatch
        Date otpExpires
        Date deletedAt
        Date createdAt
        Date updatedAt
    }

    UserSignupData {
        ObjectId _id PK
        String title
        Array parentSlug
        String itemText
        String slug
        String selectionType
        Date createdAt
        Date updatedAt
    }

    Record {
        ObjectId _id PK
        Array americanUnions
        Array globalUnions
        Array americanAffiliateOrganization
        Array globalAffiliateOrganization
        Array nonProfitPhilanthropicInstitution
        Array americanCateringCrafties
        Array artGalleries
        Array cateringCraftServicesGlobally
        Array museums
        Array talentRepsAgenciesManagementCompanies
        Array globalTalentRepsAgenciesManagementCompanies
        Array talentRepsAgenciesManagementCompaniesForSpeakers
        Array entertainmentLawFirms
        Array globalComedyClubs
        Array audioProductionRecordingStudios
        Array americanComedyClubs
        Array globalTheaters
        Array americanTheaters
        Array casting
        Array globalRecordingStudios
        Array lifeCoachAgencies
        Array animalActorsAgenciesCompanies
        Array publications
        Array investmentEntities
        Array productionAcquisitionDistributionCompanies
        Array schoolsTrainingInArtsEntertainment
        Array visualArt
        Array performingArt
        Array dance
        Array acting
        Array music
        Array filmMedia
        Array design
        Array literaryArt
        Array crafts
        Array appliedArt
        Array other
        Array feedbackCategories
        Array ethnicity
        Array nationality
        Array disability
        Array languagesSpokenSigned
        Array proficiencyLevel
        Array selfIother
        Array age
        Array selfIgender
        Array gender
        Array skills
        Array fieldOfStudy
        Array degree
        Array fanDegree
        Array industry
        Array specialization
        Array companySize
        Array organizationType
        Array pronouns
        Array causes
        Array professions
        Array positions
        Date createdAt
        Date updatedAt
    }

    %% Additional Schemas
    ProfileViews {
        ObjectId _id PK
        ObjectId userId FK
        Number viewCount
        Date createdAt
        Date updatedAt
    }

    MissingInformation {
        ObjectId _id PK
        String field
        String value
        Date createdAt
        Date updatedAt
    }

    Subscriber {
        ObjectId _id PK
        String email
        Boolean isActive
        Date createdAt
        Date updatedAt
    }

    BetaLeads {
        ObjectId _id PK
        String name
        String email
        String phone
        String organization
        String role
        String interest
        Date createdAt
        Date updatedAt
    }

    SelfIdentify {
        ObjectId _id PK
        String category
        String value
        String description
        Date createdAt
        Date updatedAt
    }

    VolunteerExperience {
        ObjectId _id PK
        String organization
        String role
        String description
        Date startDate
        Date endDate
        Boolean isCurrentlyVolunteering
        Array skills
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    LicenseCertification {
        ObjectId _id PK
        String name
        String issuingOrganization
        String credentialId
        Date issueDate
        Date expirationDate
        String credentialUrl
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    HonorsAndAwards {
        ObjectId _id PK
        String title
        String issuer
        String description
        Date issueDate
        Array media
        ObjectId userId FK
        Date createdAt
        Date updatedAt
    }

    %% Relationships
    User ||--o{ Post : "creates"
    User ||--o{ Comment : "makes"
    User ||--o{ Story : "creates"
    User ||--o{ Event : "creates"
    User ||--o{ JobPost : "posts"
    User ||--o{ Education : "has"
    User ||--o{ Experience : "has"
    User ||--o{ Project : "has"
    User ||--o{ Featured : "has"
    User ||--o{ Feedback : "submits"
    User ||--o{ Group : "creates"
    User ||--o{ Notification : "receives"
    User ||--o{ Notification : "sends"
    User ||--o{ ProfileViews : "has"
    User ||--o{ VolunteerExperience : "has"
    User ||--o{ LicenseCertification : "has"
    User ||--o{ HonorsAndAwards : "has"

    Post ||--o{ Comment : "has"
    Post ||--o{ Like : "has"
    Post ||--o{ Bookmark : "has"
    Post ||--o{ Post : "reposts"
    Comment ||--o{ Comment : "replies_to"

    Group ||--o{ GroupMember : "has"
    Group ||--o{ Post : "contains"
    Group ||--o{ Notification : "triggers"

    JobPost ||--o{ JobApplication : "receives"

    Event ||--o{ Notification : "triggers"

    ConnectionInfo }o--|| User : "connects_to"
    ConnectionInfo }o--|| User : "connected_by"

    FollowerInfo }o--|| User : "follows"
    FollowerInfo }o--|| User : "followed_by"

    People }o--|| User : "parent_of"
    People }o--|| User : "child_of"

    ClientInfo }o--|| User : "provides_service_to"
    ClientInfo }o--|| User : "receives_service_from"

    GroupMember }o--|| User : "is_member"
    GroupMember }o--|| Group : "belongs_to"

    Education }o--|| User : "attended_by"
    Education }o--|| User : "school_organization"

    Experience }o--|| User : "worked_by"

    Project }o--|| User : "created_by"
    Project }o--o{ User : "collaborates_with"

    Featured }o--|| User : "featured_by"

    JobPost }o--|| User : "posted_by"
    JobApplication }o--|| JobPost : "applies_to"
    JobApplication }o--|| User : "applied_by"

    Story }o--|| User : "created_by"
    Story }o--o{ User : "liked_by"

    Notification }o--|| Post : "about_post"
    Notification }o--|| Story : "about_story"
    Notification }o--|| ConnectionInfo : "about_connection"
    Notification }o--|| ClientInfo : "about_client"
    Notification }o--|| People : "about_member"

    Feedback }o--|| User : "submitted_by"

    ProfileViews }o--|| User : "viewed_profile"

    VolunteerExperience }o--|| User : "volunteered_by"

    LicenseCertification }o--|| User : "certified_to"

    HonorsAndAwards }o--|| User : "awarded_to"
```

## Key Relationship Types

### One-to-Many (1:N)
- **User → Posts**: One user can create many posts
- **User → Comments**: One user can make many comments
- **User → Stories**: One user can create many stories
- **Post → Comments**: One post can have many comments
- **Group → GroupMembers**: One group can have many members
- **JobPost → JobApplications**: One job can have many applications

### Many-to-Many (M:N)
- **Users ↔ Users** (via ConnectionInfo, FollowerInfo, People, ClientInfo)
- **Users ↔ Posts** (via Like, Bookmark)
- **Users ↔ Groups** (via GroupMember)
- **Users ↔ Events** (via participants/collaborators)
- **Users ↔ Projects** (via collaborators)

### Self-Referencing
- **Post → Post**: Posts can repost other posts
- **Comment → Comment**: Comments can have nested replies

## Database Collections Summary

| Category | Collections | Count |
|----------|-------------|-------|
| **Core** | User, UserSignupData | 2 |
| **Social** | ConnectionInfo, FollowerInfo, People, ClientInfo | 4 |
| **Content** | Post, Comment, Like, Bookmark, Story | 5 |
| **Groups** | Group, GroupMember, GroupRule | 3 |
| **Professional** | Education, Experience, Project, Featured | 4 |
| **Jobs** | JobPost, JobApplication, JobList | 3 |
| **Events** | Event | 1 |
| **Notifications** | Notification | 1 |
| **System** | Feedback, ContactUs, OtpLog, Record | 4 |
| **Additional** | ProfileViews, MissingInformation, Subscriber, BetaLeads, SelfIdentify, VolunteerExperience, LicenseCertification, HonorsAndAwards | 8 |

**Total Collections: 35**

This comprehensive schema supports a full-featured social networking platform with professional networking, content management, job posting, event management, and community building capabilities. 