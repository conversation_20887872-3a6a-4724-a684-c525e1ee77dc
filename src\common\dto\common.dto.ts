import {
  IsOptional,
  IsBoolean,
  IsString,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function IsOptionalBoolean() {
  return (target: any, key: string) => {
    IsOptional()(target, key);
    IsBoolean()(target, key);
  };
}

export function IsOptionalString() {
  return (target: any, key: string) => {
    IsOptional()(target, key);
    IsString()(target, key);
  };
}

export function IsNullOrString(validationOptions?: ValidationOptions) {
  return function (object, propertyName: string) {
    registerDecorator({
      name: 'isNullOrString',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          if (value === null) {
            return true; // Allow null or undefined
          }

          if (typeof value !== 'string') {
            return false; // Must be a string
          }

          return value.trim().length > 0; //Non-empty string
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} should be either null or a non-empty string`;
        },
      },
    });
  };
}
