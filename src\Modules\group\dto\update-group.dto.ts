import { Privacy, Visibility } from 'src/common/constant/enum';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';
import CONSTANT from '../../../common/constant/common.constant';
import { AllowPosting } from 'src/Models/group.schema';

export class GroupRuleDTO {
  @IsString()
  itemId: string;

  @IsBoolean()
  isSelected: boolean;
}

export class UpdateGroupDto {
  @IsStringValidation('name', 500, true)
  readonly name: string;

  @IsStringValidation('description', 2000, true)
  readonly description: string;

  @IsStringValidation('privacy', 50, true)
  @IsEnum(Privacy, { each: true, message: CONSTANT.INVALID('privacy') })
  readonly privacy: string;

  @IsStringValidation('visibility', 50, true)
  @IsEnum(Visibility, { each: true, message: CONSTANT.INVALID('visibility') })
  readonly visibility: string;

  @IsStringValidation('coverPhoto', null, true)
  coverPhoto?: string;

  @IsOptional()
  @IsArray()
  groupRule: GroupRuleDTO[];

  @IsString()
  @IsEnum(AllowPosting)
  allowPosting: AllowPosting;
}
