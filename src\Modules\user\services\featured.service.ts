import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import CONSTANT from '../../../common/constant/common.constant';
import mongoose from 'mongoose';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { featuredDto } from '../dtos/featured.dto';
import { FeaturedRepository } from 'src/Repositories/featured.repository';

@Injectable()
export class FeaturedService {
  constructor(private readonly featuredRepository: FeaturedRepository) {}

  public async addFeature(req, body: featuredDto) {
    try {
      const { user: loggedInUser } = req;
      const { _id: id, ...restData } = body;

      let feature;

      if (id) {
        const isExist = await this.featuredRepository.findById(id);
        if (!isExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Feature'),
            HttpStatus.BAD_REQUEST,
          );
        }

        feature = await this.featuredRepository.findByIdAndUpdate(id, {
          ...restData,
          userId: loggedInUser._id,
        });

        return successResponse(
          feature,
          CONSTANT.UPDATED_SUCCESSFULLY('Feature'),
          HttpStatus.OK,
        );
      }

      const data = {
        ...restData,
        userId: loggedInUser._id,
      };

      feature = await this.featuredRepository.create(data);

      return successResponse(
        feature,
        CONSTANT.ADDED_SUCCESSFULLY('Feature'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getFeatures(userId: string, req: any) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;
      const searchFields = ['title'];

      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const features = await this.featuredRepository.findAllFeatures(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      const totalResults = await this.featuredRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults,
        currentResults: features?.length,
        totalPages: Math.ceil(totalResults / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        features,
        CONSTANT.FETCHED_SUCCESSFULLY('Features'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async deleteFeature(id: string) {
    try {
      const isExist = await this.featuredRepository.findById(id);
      if (!isExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Feature'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.featuredRepository.deleteById(id);

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Feature'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
