#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

console.log('🚀 Organization Migration Quick Start\n');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function quickStart() {
  try {
    console.log('This script will help you migrate organizations from Excel to your database.\n');
    
    // Step 1: Check if Excel file exists
    console.log('Step 1: Checking for Excel file...');
    
    const excelFiles = fs.readdirSync('.').filter(file => 
      file.endsWith('.xlsx') || file.endsWith('.xls')
    );
    
    if (excelFiles.length === 0) {
      console.log('❌ No Excel files found in current directory');
      console.log('Creating sample Excel file for you...\n');
      
      const { createSampleExcelFile } = require('./migrate_organizations_from_excel');
      createSampleExcelFile();
      
      console.log('✅ Sample file created: sample_organizations.xlsx');
      console.log('Please edit this file with your organization data and run this script again.\n');
      console.log('Expected format:');
      console.log('| Main Category | Sub Category | Organization |');
      console.log('| Union | Actors Union | SAG-AFTRA |');
      console.log('| Affiliate Business | Production Company | Warner Bros |\n');
      
      rl.close();
      return;
    }
    
    // Step 2: Show available Excel files
    console.log('Found Excel files:');
    excelFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file}`);
    });
    
    // Step 3: Ask user to select file
    const answer = await askQuestion('\nEnter the number of the Excel file to use (or press Enter for first file): ');
    const selectedIndex = answer.trim() === '' ? 0 : parseInt(answer) - 1;
    const selectedFile = excelFiles[selectedIndex];
    
    if (!selectedFile || selectedIndex < 0 || selectedIndex >= excelFiles.length) {
      console.log('❌ Invalid selection');
      rl.close();
      return;
    }
    
    console.log(`✅ Selected: ${selectedFile}\n`);
    
    // Step 4: Confirm migration
    console.log('⚠️  WARNING: This will DELETE ALL existing organizations and replace them with data from your Excel file!');
    const confirm = await askQuestion('Are you sure you want to continue? (yes/no): ');
    
    if (confirm.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled');
      rl.close();
      return;
    }
    
    // Step 5: Run migration
    console.log('\n🚀 Starting migration...\n');
    
    const { migrateOrganizationsFromExcel } = require('./migrate_organizations_from_excel');
    await migrateOrganizationsFromExcel(selectedFile);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Test your organization APIs');
    console.log('2. Verify organizations appear in search results');
    console.log('3. Check that category filtering works correctly');
    
  } catch (error) {
    console.error('❌ Quick start failed:', error.message);
    console.log('\nTroubleshooting:');
    console.log('1. Check your Excel file format');
    console.log('2. Ensure MongoDB is running and accessible');
    console.log('3. Verify your .env file has correct database connection');
  } finally {
    rl.close();
  }
}

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

// Run quick start
quickStart();
