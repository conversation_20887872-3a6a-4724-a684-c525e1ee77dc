import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { experienceDocument } from 'src/Models/experience.schema';

@Injectable()
export class ExperienceRepository extends AbstractRepository<experienceDocument> {
  constructor(
    @InjectModel('Experience') experienceModel: Model<experienceDocument>,
  ) {
    super(experienceModel);
  }

  async findExperiences(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const results: any = await this.model
      .find(filter)
      .select('-__v -userId')
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified userProfileAboutYou.skills',
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();

    return results.map((experience) => {
      if (experience.userId?.userProfileAboutYou?.skills) {
        experience = {
          ...experience.toObject(),
          skills: experience.userId.userProfileAboutYou.skills,
        };
      }
      experience.userId = experience.userId._id;
      return experience;
    });
  }
}
