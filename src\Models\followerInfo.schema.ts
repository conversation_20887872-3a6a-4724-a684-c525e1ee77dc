import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';

export type followerInfoDocument = FollowerInfo & Document;

export enum StatusEnum {
  PENDING = 'pending',
  ACCEPT = 'accept',
  REJECT = 'reject',
}

@Schema({ timestamps: true })
export class FollowerInfo {
  @Prop({ default: StatusEnum.PENDING, enum: StatusEnum })
  status: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  followerId: User;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  followingId: User;
}

export const FollowerInfoSchema = SchemaFactory.createForClass(FollowerInfo);
