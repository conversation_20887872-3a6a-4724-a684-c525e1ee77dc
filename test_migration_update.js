#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Updated Migration Function\n');

// Test data with new subcategories
const testData = [
  {
    mainCategory: 'Union',
    subCategory: 'New Union Type',
    organization: 'Test Union Organization'
  },
  {
    mainCategory: 'Affiliate Organization', 
    subCategory: 'New Affiliate Type',
    organization: 'Test Affiliate Organization'
  },
  {
    mainCategory: 'Affiliate Business',
    subCategory: 'New Business Type', 
    organization: 'Test Business Organization'
  },
  {
    mainCategory: 'School / Training',
    subCategory: 'New Training Type',
    organization: 'Test Training Organization'
  }
];

// Test the title generation logic
function testTitleGeneration() {
  console.log('🔧 Testing Title Generation Logic:\n');
  
  testData.forEach((row, index) => {
    const mainCategorySlug = mapExcelCategoryToSlug(row.mainCategory);
    const subcategoryName = row.subCategory.trim();
    const subcategorySlug = subcategoryName.toLowerCase().replace(/\s+/g, '_');
    
    // Test subcategory title generation
    let subcategoryTitle;
    switch (mainCategorySlug) {
      case 'union_1':
        subcategoryTitle = 'union_subcategory';
        break;
      case 'affiliate_organization_1':
        subcategoryTitle = 'affiliate_org_subcategory';
        break;
      case 'affiliate_business_1':
        subcategoryTitle = 'affiliate_business_subcategory';
        break;
      case 'school_training_facility_1':
        subcategoryTitle = 'school_training_subcategory';
        break;
      default:
        subcategoryTitle = `${mainCategorySlug.replace('_1', '')}_subcategory`;
    }
    
    // Test organization title generation
    let organizationTitle;
    if (subcategoryTitle === 'union_subcategory') {
      organizationTitle = 'union_organization';
    } else if (subcategoryTitle === 'affiliate_org_subcategory') {
      organizationTitle = 'affiliate_org_organization';
    } else if (subcategoryTitle === 'affiliate_business_subcategory') {
      organizationTitle = 'affiliate_business_organization';
    } else if (subcategoryTitle === 'school_training_subcategory') {
      organizationTitle = 'school_training_organization';
    } else {
      organizationTitle = `${subcategoryTitle.replace('_subcategory', '')}_organization`;
    }
    
    console.log(`Test ${index + 1}:`);
    console.log(`  Main Category: ${row.mainCategory} -> ${mainCategorySlug}`);
    console.log(`  Subcategory: ${row.subCategory} -> ${subcategoryTitle}`);
    console.log(`  Organization: ${row.organization} -> ${organizationTitle}`);
    console.log(`  Selection Type: only (instead of multiple)`);
    console.log('');
  });
}

// Test the mapping function
function mapExcelCategoryToSlug(excelCategory) {
  const mapping = {
    // Unions
    'Unions': 'union_1',
    'Union': 'union_1',
    
    // Affiliate Organizations
    'Affiliate Organizations': 'affiliate_organization_1',
    'Affiliate Organization': 'affiliate_organization_1',
    'Organizations': 'affiliate_organization_1',
    'Organization': 'affiliate_organization_1',
    
    // Affiliate Businesses
    'Affiliate Businesses': 'affiliate_business_1',
    'Affiliate Business': 'affiliate_business_1',
    'Businesses': 'affiliate_business_1',
    'Business': 'affiliate_business_1',
    
    // Schools and Training
    'School/Training': 'school_training_facility_1',
    'Schools/Training': 'school_training_facility_1',
    'Training': 'school_training_facility_1',
    'Schools': 'school_training_facility_1',
    'Schools / Training': 'school_training_facility_1',
    'Schools / Training Facilities': 'school_training_facility_1',
    'School / Training Facilities': 'school_training_facility_1',
    'Schools / Training in Arts & Entertainment': 'school_training_facility_1',
    'School / Training in Arts & Entertainment': 'school_training_facility_1'
  };
  
  const normalizedCategory = excelCategory.toString().trim();
  const mappedSlug = mapping[normalizedCategory];
  
  if (!mappedSlug) {
    console.log(`⚠️  Warning: Unknown category "${normalizedCategory}", defaulting to union_1`);
    return 'union_1';
  }
  
  return mappedSlug;
}

// Run tests
testTitleGeneration();

console.log('✅ Title generation tests completed!');
console.log('\n📋 Summary of Changes:');
console.log('   • All selectionType values changed from "multiple" to "only"');
console.log('   • Title enum updated with all existing subcategory and organization titles');
console.log('   • Migration function now auto-generates titles for new subcategory types');
console.log('   • Business logic enforced: users can only select ONE subcategory');
