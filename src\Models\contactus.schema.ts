import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type ContactUsDocument = ContactUs & Document;

@Schema({ timestamps: true })
export class ContactUs {
  @Prop({ type: String })
  name: string;

  @Prop({ type: String })
  email: string;

  @Prop({ type: String })
  phone: string;

  @Prop({ type: String })
  message: string;
}

export const ContactUsSchema = SchemaFactory.createForClass(ContactUs);
