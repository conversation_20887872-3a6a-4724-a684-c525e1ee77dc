[{"title": "who_are_you", "parentSlug": ["1"], "itemText": "Union Member", "slug": "union_member_1", "selectionType": "none"}, {"title": "who_are_you", "parentSlug": ["1"], "itemText": "Affiliate Member", "slug": "affiliate_member_1", "selectionType": "none"}, {"title": "who_are_you", "parentSlug": ["1"], "itemText": "Professor / Teacher / Instructor / Coach", "slug": "pro_tea_int_coach_1", "selectionType": "none"}, {"title": "who_are_you", "parentSlug": ["1"], "itemText": "Student", "slug": "student_1", "selectionType": "none"}, {"title": "who_are_you", "parentSlug": ["2"], "itemText": "Non-Union Individuals or Hobbyists", "slug": "non_union_hobbyists_1", "selectionType": "none"}, {"title": "who_are_you", "parentSlug": ["3"], "itemText": "Audience Members / Fans", "slug": "audience_fans_1", "selectionType": "none"}, {"title": "who_are_you", "parentSlug": ["4"], "itemText": "Union (an Organization / Not an individual member)", "slug": "union_1", "selectionType": "only"}, {"title": "who_are_you", "parentSlug": ["4"], "itemText": "Affiliate (Organization)", "slug": "affiliate_organization_1", "selectionType": "only"}, {"title": "who_are_you", "parentSlug": ["4"], "itemText": "Affiliate (Business)", "slug": "affiliate_business_1", "selectionType": "only"}, {"title": "who_are_you", "parentSlug": ["4"], "itemText": "School / Training Facility", "slug": "school_training_facility_1", "selectionType": "only"}, {"title": "more_about_you", "parentSlug": ["1", "2"], "itemText": "Performer (you perform in front of an audience)", "slug": "performer_1", "selectionType": "none"}, {"title": "more_about_you", "parentSlug": ["1", "2"], "itemText": "Artist (Non Performer)", "slug": "artist_1", "selectionType": "none"}, {"title": "more_about_you", "parentSlug": ["1", "2"], "itemText": "Non Performer (entirely behind the scenes. For example, <PERSON><PERSON>, Personal Assistant, Business Manager etc.)", "slug": "non_performer_1", "selectionType": "none"}, {"title": "more_about_you", "parentSlug": ["1", "2"], "itemText": "Employer / Hirer (or liason to the employment of others. For example: Producer, Casting Director, Art Commissioner etc.)", "slug": "employer_1", "selectionType": "none"}, {"title": "more_about_you", "parentSlug": ["2"], "itemText": "Professor / Teacher / Instructor / Coach", "slug": "pro_tea_int_coach_2", "selectionType": "none"}, {"title": "more_about_you", "parentSlug": ["2"], "itemText": "Student", "slug": "student_2", "selectionType": "none"}, {"title": "about_you", "parentSlug": ["pro_tea_int_coach_1", "student_1"], "itemText": "I am a current student", "slug": "i_current_student_1", "selectionType": "none"}, {"title": "about_you", "parentSlug": ["pro_tea_int_coach_1", "student_1"], "itemText": "I am a professor / teacher / instructor / coach", "slug": "i_pro_tea_int_coach_1", "selectionType": "none"}, {"title": "profile_open_to", "parentSlug": ["1", "2", "3", "4"], "itemText": "Public / Everyone", "slug": "public_1", "selectionType": "only"}, {"title": "profile_open_to", "parentSlug": ["1", "2", "3", "4"], "itemText": "Private", "slug": "private_1", "selectionType": "only"}, {"title": "profile_open_to", "parentSlug": ["1", "2", "4"], "itemText": "Only potential Employers / Hirers (or liasons to employment like Producers, Casting Directors etc.) can see my Profile", "slug": "potential_1", "selectionType": "only"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "non_union_hobbyists_1", "union_1", "affiliate_organization_1", "affiliate_business_1", "school_training_facility_1", "audience_fans_1"], "itemText": "Anyone", "slug": "anyone_1", "selectionType": "all"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "audience_fans_1"], "itemText": "Connections in My Network", "slug": "connection_in_network_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "non_union_hobbyists_1", "union_1", "affiliate_organization_1", "affiliate_business_1", "school_training_facility_1", "audience_fans_1"], "itemText": "Followers", "slug": "follower_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "union_1"], "itemText": "Members in My Union(s)", "slug": "member_in_union_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "union_1"], "itemText": "All Union Members", "slug": "all_union_member_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "union_1"], "itemText": "Affiliate Members", "slug": "affiliate_member_2", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "school_training_facility_1"], "itemText": "Professor / Teachers / Instructors / Coaches", "slug": "pro_tea_int_coach_3", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1"], "itemText": "Students", "slug": "student_3", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "non_union_hobbyists_1", "affiliate_organization_1", "affiliate_business_1"], "itemText": "Audience Members / Fans", "slug": "audience_fans_2", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "union_1", "affiliate_organization_1", "affiliate_business_1"], "itemText": "Non-Union Individuals or Hobbyists", "slug": "non_union_hobbyists_2", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1"], "itemText": "Contact only through my (Talent) Representative", "slug": "contact_through_talent_1", "selectionType": "only"}, {"title": "who_can_message", "parentSlug": ["union_member_1", "affiliate_member_1", "non_union_hobbyists_1", "school_training_facility_1"], "itemText": "Only (Potential) Employers / Hirers can message me (or liason to the employment of others, For example: Producers, Casting Directors, Art Commisioners, etc.)", "slug": "potential_2", "selectionType": "only"}, {"title": "who_can_message", "parentSlug": ["affiliate_member_1", "affiliate_organization_1", "affiliate_business_1"], "itemText": "Union Members", "slug": "union_member_2", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["affiliate_member_1", "affiliate_organization_1", "affiliate_business_1"], "itemText": "Members in My Affiliate Organization(s)", "slug": "member_in_organization_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["affiliate_member_1", "affiliate_organization_1", "affiliate_business_1"], "itemText": "All Affiliated Members", "slug": "all_affiliate_member_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["non_union_hobbyists_1", "union_1", "affiliate_organization_1", "affiliate_business_1", "school_training_facility_1"], "itemText": "My Connections", "slug": "my_connection_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["affiliate_organization_1", "affiliate_business_1"], "itemText": "Members in My Affiliate Business", "slug": "member_in_business_1", "selectionType": "none"}, {"title": "who_can_message", "parentSlug": ["school_training_facility_1"], "itemText": "Alumni & Students", "slug": "alumni_student_1", "selectionType": "none"}]