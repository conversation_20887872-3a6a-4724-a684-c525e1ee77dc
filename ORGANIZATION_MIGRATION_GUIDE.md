# Organization Migration Guide - Excel to Database

## Overview

This guide explains how to migrate organizations from an Excel file into the Pepli Node database, replacing all existing organizations with new data.

## Prerequisites

1. **Excel File Format**: Your Excel file must have exactly 3 columns:
   - **Column A**: Main Category (e.g., "Union", "Affiliate Business", "School/Training")
   - **Column B**: Sub Category (e.g., "Actors Union", "Production Company", "Film School")
   - **Column C**: Organization Name (e.g., "SAG-AFTRA", "Warner Bros", "USC School of Cinematic Arts")

2. **Dependencies**: Ensure `xlsx` package is installed (already included in package.json)

3. **Database Access**: MongoDB connection string in your `.env` file

## Migration Process

### Step 1: Prepare Your Excel File

Create an Excel file with the following structure:

| Main Category | Sub Category | Organization |
|---------------|--------------|--------------|
| Union | Actors Union | SAG-AFTRA |
| Union | Directors Union | DGA |
| Union | Writers Union | WGA |
| Affiliate Business | Production Company | Warner Bros |
| Affiliate Business | Talent Agency | CAA |
| School/Training | Film School | USC School of Cinematic Arts |

**Important Notes:**
- First row should contain headers
- No empty rows between data
- Categories and organization names should be descriptive and consistent
- Avoid special characters that might cause issues

### Step 2: Run the Migration Script

#### Option A: Run with your Excel file
```bash
node migrate_organizations_from_excel.js your_organizations.xlsx
```

#### Option B: Create sample file first (for testing)
```bash
node migrate_organizations_from_excel.js
# This will create sample_organizations.xlsx for testing
```

### Step 3: Verify Migration Results

The script will provide a detailed summary including:
- Total organizations processed
- Organizations inserted
- Hierarchy entries created
- Categories breakdown
- Sample of inserted organizations

## What the Migration Script Does

### 1. **Data Cleanup**
- Deletes all existing organizations from the `users` table
- Clears the `organizationHierarchy` collection
- Ensures clean slate for new data

### 2. **Data Insertion**
- Reads Excel data and validates format
- Creates new user records for each organization
- Sets `isFakeAccount: true` (these are predefined organizations)
- Generates unique usernames and temporary emails
- Assigns proper category slugs

### 3. **Hierarchy Creation**
- Creates organization hierarchy entries
- Groups organizations by main category and subcategory
- Maintains proper relationships for API queries

### 4. **Data Normalization**
- Converts category names to URL-friendly slugs
- Ensures uniqueness in usernames
- Standardizes data format

## Generated Data Structure

### User Records
```json
{
  "businessOrganizationName": "SAG-AFTRA",
  "iAmMember": "unionAffiliateOrganizationBusinessSchoolsTrainingFacility",
  "isFakeAccount": true,
  "organizationMainCategory": "union",
  "organizationSubcategory": "actors_union",
  "userName": "sagaftra_0",
  "email": "<EMAIL>",
  "password": "TempPassword123!"
}
```

### Hierarchy Records
```json
{
  "mainCategory": "union",
  "subcategory": "actors_union",
  "organizationName": "SAG-AFTRA",
  "organizationId": "ObjectId...",
  "organizationCount": 1
}
```

## Post-Migration Steps

### 1. **Update Organization Flows**
The existing organization APIs will automatically work with the new data:
- `GET /api/v1/user/organization-categories` - Returns main categories
- `GET /api/v1/user/organization-subcategories/:mainCategorySlug` - Returns subcategories
- `GET /api/v1/user/organizations-by-subcategory/:subcategorySlug` - Returns organizations

### 2. **Test the System**
- Verify organizations appear in search results
- Check category filtering works correctly
- Ensure organization profiles are accessible

### 3. **User Management**
- Organizations are created as "fake accounts" (`isFakeAccount: true`)
- When real users sign up with the same organization name, the account converts to real
- Temporary passwords can be changed on first login

## Troubleshooting

### Common Issues

1. **Excel Format Errors**
   - Ensure headers match exactly: "Main Category", "Sub Category", "Organization"
   - Check for empty rows or malformed data

2. **Database Connection Issues**
   - Verify MongoDB connection string in `.env`
   - Ensure database is accessible

3. **Duplicate Username Errors**
   - The script automatically adds indices to ensure uniqueness
   - If issues persist, check for very long organization names

### Error Recovery

If migration fails:
1. Check the error message for specific issues
2. Fix the Excel file if needed
3. Re-run the migration script
4. The script is idempotent - it will clean up and start fresh each time

## API Integration

After migration, your existing organization APIs will work seamlessly:

### Category API
```typescript
// Returns main categories from your Excel data
GET /api/v1/user/organization-categories
```

### Subcategory API
```typescript
// Returns subcategories for a main category
GET /api/v1/user/organization-subcategories/union
```

### Organization API
```typescript
// Returns organizations in a subcategory
GET /api/v1/user/organizations-by-subcategory/actors_union
```

## Best Practices

1. **Backup First**: Always backup your database before running migrations
2. **Test with Sample Data**: Use the sample file first to verify the process
3. **Validate Excel Data**: Ensure your Excel file is clean and well-formatted
4. **Monitor Migration**: Watch the console output for any warnings or errors
5. **Verify Results**: Test the APIs after migration to ensure everything works

## Support

If you encounter issues:
1. Check the console output for specific error messages
2. Verify your Excel file format matches the requirements
3. Ensure all dependencies are properly installed
4. Check database connectivity and permissions
