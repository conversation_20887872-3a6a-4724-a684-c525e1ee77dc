import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
// import { config } from 'aws-sdk';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AllExceptionsFilter } from './common/filters/exception.filter';
import path from 'path';
import { copyDir } from './Custom/helpers/common';
// import { SeederService } from './common/services/seeder.service';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    rawBody: true,
  });
  // config.update({
  //   accessKeyId: process.env.AWS_ACCESS_ID,
  //   secretAccessKey: process.env.AWS_SECRET,
  //   region: process.env.AWS_REGION,

  // });

  // app.useGlobalInterceptors(new HttpInterceptor());
  app.enableCors();
  app.setGlobalPrefix('api/v1');

  // Set EJS as the view engine
  app.setBaseViewsDir(path.join(__dirname, '..', 'views'));
  app.setViewEngine('ejs');

  app.useGlobalPipes(new ValidationPipe());
  app.useBodyParser('json', { limit: '10mb' });
  app.useGlobalFilters(new AllExceptionsFilter());

  // Remove comment from seeder when project move into production
  // const seederService = app.get(SeederService);
  // await seederService.insertGroupRule();
 app.use((req, res, next) => {
    console.log(`${req.method} ${req.url}`, 'Request');
    next();
  });
  await app.listen(process.env.PORT, async () => {
    console.log('server listening on port ' + process.env.PORT);

    // ✅ Copy views using core modules
    const src = path.join(__dirname, '..', '..', 'src', 'views');
    const dest = path.join(__dirname, '..', 'views');

    try {
      await copyDir(src, dest);
      console.log('✅ Views copied successfully');
    } catch (err) {
      console.error('❌ Failed to copy views:', err);
    }
  });
}
bootstrap();
