import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
// import { Privacy, Visibility } from 'src/common/constant/enum';
import { User } from './user.schema';
import { GroupRule } from './groupRule.schema';
import { Privacy, Visibility } from '../common/constant/enum';
export type GroupDocument = Group & Document;

@Schema({ timestamps: false, _id: false, versionKey: false })
export class Rule {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'GroupRule',
  })
  itemId: GroupRule;

  @Prop({ type: Boolean, trim: true, required: true })
  isSelected: boolean;
}

export enum AllowPosting {
  Members = 'members',
  Admin = 'admin',
}

@Schema({ timestamps: true, versionKey: false })
export class Group {
  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  joinPurpose: string;

  @Prop({ type: String })
  coverPhoto: string;

  @Prop({ type: String, default: Privacy.PUBLIC, enum: Privacy })
  privacy: string;

  @Prop({ type: String, default: Visibility.VISIBLE, enum: Visibility })
  visibility: string;

  @Prop({ type: [Rule] })
  groupRule: [Rule];

  @Prop({ enum: AllowPosting, default: AllowPosting.Members })
  allowPosting: AllowPosting;

  @Prop({ type: String, trim: true })
  shareableLink: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'User',
  })
  createdBy: mongoose.Schema.Types.ObjectId;

  @Prop({ type: Number, default: 0 })
  posts: number;
}

export const GroupSchema = SchemaFactory.createForClass(Group);
