import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, PipelineStage } from 'mongoose';
import { AllowPosting, Group } from 'src/Models/group.schema';
import { CreateGroupDto } from './dto/create-group.dto';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import CONSTANT from '../../common/constant/common.constant';
import { BranchDeeplinkService } from 'src/common/services/branchDeeplink.service';
import { GetGroupsDto } from './dto/get-groups.dto';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { AwsService } from 'src/common/services/aws.service';
import { UpdateGroupDto } from './dto/update-group.dto';
import { GetSuggestedUsersDto } from './dto/get-suggested-users.dto';
import { iAmMemberEnum, User } from 'src/Models/user.schema';
import { InviteUserDto } from './dto/invite-user.dto';
import { NotificationService } from 'src/common/services/notification.service';
import {
  InvitationType,
  NotificationsType,
  Privacy,
  RedirectionType,
  SuggestUsersEnum,
} from 'src/common/constant/enum';
import {
  blockUnblockMemberDto,
  InviteAcceptRejectDto,
  removeGroupMemberDto,
} from './dto/invite-accept-reject.dto';
import { Notifications } from 'src/Models/notification.schema';
import { GroupMember } from 'src/Models/groupMember.schema';
import { GetGroupPostsDto } from './dto/get-group-posts.dto';
import { addGroupPostDto, repostInGroupDto } from './dto/add-post-group.dto';
import { Bookmark, bookmarkDocument } from 'src/Models/bookmark.schema';
import { Post, postDocument } from 'src/Models/post.schema';
import { GroupRule, GroupRuleDocument } from 'src/Models/groupRule.schema';
import {
  FollowerInfo,
  followerInfoDocument,
} from 'src/Models/followerInfo.schema';
import {
  InviteUserViaEmialDto,
  JoinGroupDTO,
} from './dto/invite-user-email.dto';
import { MailService, templates } from 'src/common/services/mail.service';
import { PostService } from '../post/services/post.service';
import {
  NOTIFICATION_MESSAGES,
  notificationMessage,
} from 'src/Custom/helpers/message.helper';
import { connectionRequestStatus } from '../user/dtos/user.dtos';
import { StatusEnum } from 'src/Models/connectionInfo.schema';

@Injectable()
export class GroupService {
  constructor(
    @InjectModel(Post.name) private readonly postModel: Model<postDocument>,
    @InjectModel(Group.name)
    private groupModel: Model<Group>,
    @InjectModel(GroupMember.name)
    private groupMemberModel: Model<GroupMember>,
    @InjectModel(User.name)
    private userModel: Model<User>,
    @InjectModel(Notifications.name)
    private notificationModel: Model<Notifications>,
    @InjectModel(Bookmark.name)
    private readonly bookmarkModel: Model<bookmarkDocument>,
    @InjectModel(GroupRule.name)
    private readonly groupRuleModel: Model<GroupRuleDocument>,
    @InjectModel(FollowerInfo.name)
    private readonly followerInfoModel: Model<followerInfoDocument>,
    @Inject(forwardRef(() => PostService))
    private PostService: PostService,
    private readonly branchDeeplinkService: BranchDeeplinkService,
    private readonly awsService: AwsService,
    private readonly mailService: MailService,
    private notificationService: NotificationService,
  ) {}

  // Create Group
  async createGroup(req: any, body: CreateGroupDto) {
    try {
      const { user: loggedInUser } = req;
      const {
        name,
        description,
        privacy,
        visibility,
        coverPhoto,
        allowPosting,
      } = body;

      // Creating group
      const createdGroup = await this.groupModel.create({
        name,
        description,
        privacy,
        visibility,
        createdBy: loggedInUser._id,
        coverPhoto: coverPhoto,
        allowPosting,
      });

      const deepLink = await this.branchDeeplinkService.generateDeepLink(
        createdGroup._id.toString(),
        {
          title: createdGroup.name,
          description: createdGroup.description,
          image_url: createdGroup.coverPhoto,
        },
        false,
      );

      await this.groupModel.findByIdAndUpdate(createdGroup._id, {
        shareableLink: deepLink,
      });

      // Creating group member
      await this.groupMemberModel.create({
        group: createdGroup._id,
        member: loggedInUser._id,
      });

      return successResponse(
        null,
        CONSTANT.CREATED_SUCCESSFULLY('Group'),
        HttpStatus.CREATED,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  // Get groups
  async getAllGroups(req: any, queryParam: GetGroupsDto) {
    try {
      const { user: loggedInUser } = req;
      const { search, sortBy, sort, page, perPage, userId } = queryParam;

      // Default fields
      const searchFields = ['name'];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      // Create Search, Filter, Sort and Pagination
      const { searchObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      let user = null;

      if (userId) {
        user = await this.userModel.findById(userId);
        if (!user)
          throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      // Get the groups the loggedInUser is a member of
      const groupIds = await this.groupMemberModel
        .find({
          member: userId ? user._id : loggedInUser._id,
        }) // Filter by loggedInUser
        .distinct('group');

      const searchFilterObj = {
        ...searchObj,
      };

      // New Implementation
      const pipeline: any[] = [
        {
          $lookup: {
            from: 'users',
            localField: 'createdBy',
            foreignField: '_id',
            as: 'createdBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: '$createdBy',
        },
        {
          $lookup: {
            from: 'groupmembers',
            localField: '_id',
            foreignField: 'group',
            as: 'members',
            pipeline: [
              {
                $match: {
                  member: userId ? user._id : loggedInUser._id,
                },
              },
              {
                $project: {
                  isBlocked: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            isBlocked: {
              $cond: {
                if: { $gt: [{ $size: '$members' }, 0] },
                then: { $arrayElemAt: ['$members.isBlocked', 0] },
                else: false,
              },
            },
          },
        },
        {
          $project: {
            createdBy: {
              _id: 1,
              iAmMember: 1,
              firstName: 1,
              lastName: 1,
              businessOrganizationName: 1,
              userName: 1,
              hirerEmployerVerifiedStatus: 1,
              isMembershipVerified: 1,
              isFakeAccount: 1,
            },
            isBlocked: 1,
            // Keep all other group fields intact
            name: 1,
            _id: 1,
            privacy: 1,
            coverPhoto: 1,
            description: 1,
            visibility: 1,
            allowPosting: 1,
            groupRule: 1,
            shareableLink: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
      ];

      if (search) {
        pipeline.push({
          $match: {
            ...searchFilterObj,
          },
        });

        if (
          loggedInUser?.iAmMember === 'audienceMemberFan' ||
          (user && user.iAmMember === 'audienceMemberFan')
        ) {
          pipeline.push({
            $match: {
              $or: [
                {
                  'createdBy.iAmMember': 'audienceMemberFan',
                  privacy: 'public',
                }, // Include groups created by audienceMemberFan
                { _id: { $in: groupIds } }, // Include groups the loggedInUser is a member of (invited groups)
              ],
            },
          });
        } else {
          pipeline.push({
            $match: {
              $or: [
                {
                  privacy: 'public',
                }, // Include groups created by audienceMemberFan
                { _id: { $in: groupIds } }, // Include groups the loggedInUser is a member of (invited groups)
              ],
            },
          });
        }
      } else {
        pipeline.push({
          $match: {
            _id: { $in: groupIds },
          },
        });
      }

      // Sort, pagination, and additional stages
      pipeline.push(
        { $sort: sortObj },
        { $skip: skipData },
        { $limit: limitData },
      );

      // Execute aggregation pipeline
      const groupData = await this.groupModel.aggregate(pipeline);

      const groupCount = await this.groupModel.aggregate([
        ...pipeline.slice(0, pipeline.length - 3), // Remove $sort, $skip, and $limit for count
        { $count: 'totalCount' },
      ]);

      // Pagination parameters
      const totalResults = groupCount[0]?.totalCount || 0;
      const currentResults = groupData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      // Adding sign url for group attachment
      // const groups = await Promise.all(
      //   groupData.map(async (group) => {
      //     if (group.coverPhoto) {
      //       group.coverPhoto = await this.awsService.signedUrl(
      //         group.coverPhoto,
      //       );
      //     }
      //     return group;
      //   }),
      // );

      return successResponse(
        groupData,
        CONSTANT.FETCHED_SUCCESSFULLY('Group'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async getGroupById(req: any, groupId: string) {
    try {
      const { user: loggedInUser } = req;

      const isBlocked = req.query.isBlocked ?? false;

      const groupExist = await this.groupModel.findById(groupId);

      if (!groupExist)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      const isAdmin =
        groupExist.createdBy.toString() === loggedInUser._id.toString();

      const result = await this.groupModel.aggregate([
        // Match the specific group by ID
        {
          $match: { _id: new mongoose.Types.ObjectId(groupId) },
        },

        // Lookup for `createdBy` details
        {
          $lookup: {
            from: 'users', // Replace with the actual user collection name
            localField: 'createdBy',
            foreignField: '_id',
            as: 'createdBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true },
        },

        // Lookup for selected group rules
        {
          $lookup: {
            from: 'grouprules',
            let: { groupRuleItems: '$groupRule' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $in: ['$_id', '$$groupRuleItems.itemId'] },
                      !isAdmin && {
                        $in: [
                          true,
                          {
                            $map: {
                              input: '$$groupRuleItems',
                              as: 'item',
                              in: {
                                $cond: [
                                  { $eq: ['$$item.itemId', '$_id'] },
                                  '$$item.isSelected',
                                  false,
                                ],
                              },
                            },
                          },
                        ],
                      },
                    ],
                  },
                },
              },
              {
                $addFields: {
                  titleNumber: {
                    $toInt: {
                      $arrayElemAt: [{ $split: ['$title', '.'] }, 0],
                    },
                  },
                  titleSuffix: {
                    $trim: {
                      input: {
                        $arrayElemAt: [{ $split: ['$title', '.'] }, 1],
                      },
                    },
                  },
                },
              },
              {
                $sort: {
                  titleNumber: 1,
                },
              },
              {
                $setWindowFields: {
                  sortBy: { titleNumber: 1 },
                  output: {
                    newIndex: { $documentNumber: {} },
                  },
                },
              },
              {
                $addFields: {
                  title: {
                    $concat: [{ $toString: '$newIndex' }, '. ', '$titleSuffix'],
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  title: 1,
                  explanation: 1,
                },
              },
            ],
            as: 'selectGroupRule',
          },
        },
        {
          $project: {
            updatedAt: 0,
            __v: 0,
          },
        },
      ]);

      const group = result[0];

      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      const groupMembers: any = await this.groupMemberModel
        .find({
          group: groupId,
        })
        .select('member isBlocked')
        .populate({
          path: 'member',
          match: { isFakeAccount: false },
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        });

      const isCurrentUserBlocked = groupMembers.find(
        (member) =>
          member.member._id.toString() === loggedInUser._id.toString() &&
          member.isBlocked === true,
      )
        ? true
        : false;

      const membersList = groupMembers
        .map((groupMember: any) => ({
          ...groupMember.toObject().member,
          isBlocked: groupMember.toObject().isBlocked,
        }))
        .filter((member: any) => {
          return isBlocked === true || isBlocked === 'true'
            ? member.isBlocked === true
            : member.isBlocked === false;
        });

      // Adding signed URL for the group cover photo
      // if (group.coverPhoto) {
      //   group.coverPhoto = await this.awsService.signedUrl(group.coverPhoto);
      // }

      const groupWithMembers = {
        ...group,
        isCurrentUserBlocked,
        member: membersList,
      };

      return successResponse(
        groupWithMembers,
        CONSTANT.FETCHED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getBlockedMembers(groupId: string) {
    try {
      const group = await this.groupModel.findById(groupId);

      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      const groupMembers = await this.groupMemberModel
        .find({
          group: groupId,
          isBlocked: true,
        })
        .select('member isBlocked')
        .populate({
          path: 'member',
          match: { isFakeAccount: false },
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        });

      const membersList = groupMembers.map((groupMember) => ({
        ...groupMember.toObject().member,
        isBlocked: groupMember.toObject().isBlocked,
      }));

      return successResponse(
        membersList,
        CONSTANT.FETCHED_SUCCESSFULLY('Blocked Members'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  // Update Group
  async updateGroup(groupId: string, body: UpdateGroupDto) {
    try {
      const { coverPhoto } = body;

      const group = await this.groupModel.findOne({ _id: groupId }).lean();
      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      // Check if cover photo update or not
      if (coverPhoto) {
        body.coverPhoto = coverPhoto.split('amazonaws.com/')[1];
        await this.awsService.s3DeleteByKey(group.coverPhoto);
      }

      // Updating user details
      await this.groupModel.findOneAndUpdate({ _id: groupId }, body);

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Group'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  // Suggested users for invite
  async suggestedUsers(req: any, queryParam: GetSuggestedUsersDto) {
    try {
      const { user: loggedInUser } = req;
      const { groupId, type, search, sortBy, sort, page, perPage } = queryParam;

      // Default fields
      const searchFields = [
        'firstName',
        'lastName',
        'userName',
        'businessOrganizationName',
      ];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      // Create Search, Filter, Sort and Pagination
      const { searchObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      let searchFilterObj: any;

      //suggest users that are not in the group
      if (type === SuggestUsersEnum.SUGGESTED) {
        if (!groupId) {
          throw new HttpException(
            CONSTANT.REQUIRED('groupId'),
            HttpStatus.NOT_FOUND,
          );
        }

        // Fetch member IDs in the group
        const memberIds = await this.groupMemberModel
          .find({ group: groupId })
          .distinct('member');

        searchFilterObj = {
          ...searchObj,
          isFakeAccount: false,
          _id: { $nin: memberIds },
        };
      }

      //suggest users that are not in the group and follow eachother
      if (type === SuggestUsersEnum.MANAGE_GROUP) {
        if (!groupId) {
          throw new HttpException(
            CONSTANT.REQUIRED('groupId'),
            HttpStatus.NOT_FOUND,
          );
        }

        // Fetch member IDs in the group
        const memberIds = await this.groupMemberModel
          .find({ group: groupId })
          .distinct('member');

        searchFilterObj = {
          ...searchObj,
          _id: { $nin: memberIds },
        };
      }

      if (type === SuggestUsersEnum.LOCATION) {
        const searchCity = search
          ? { $regex: search, $options: 'i' }
          : loggedInUser.city;

        searchFilterObj = {
          $or: [
            { city: searchCity },
            { country: searchCity },
            { state: searchCity },
          ],
          _id: { $ne: loggedInUser._id },
        };
      }

      if (loggedInUser.iAmMember === iAmMemberEnum.AUDIENCE_MEMBER_FAN) {
        searchFilterObj = {
          ...searchFilterObj,
          iAmMember: iAmMemberEnum.AUDIENCE_MEMBER_FAN,
        };
      }

      const userCount = await this.userModel.countDocuments(searchFilterObj);

      const pipeline: PipelineStage[] = [
        { $match: searchFilterObj },
        { $sort: sortObj },
        { $skip: skipData },
        { $limit: limitData },
        {
          $project: {
            _id: 1,
            firstName: 1,
            lastName: 1,
            businessOrganizationName: 1,
            userName: 1,
            profileImage: 1,
            followers: 1,
            following: 1,
            connections: 1,
            accountVerified: 1,
            iAmMember: 1,
            hirerEmployerVerifiedStatus: 1,
            isMembershipVerified: 1,
            isFakeAccount: 1,
          },
        },
      ];

      if (
        (type === SuggestUsersEnum.SUGGESTED ||
          type === SuggestUsersEnum.MANAGE_GROUP ||
          type === SuggestUsersEnum.LOCATION) &&
        !search
      ) {
        pipeline.push(
          ...[
            {
              $lookup: {
                from: 'connectioninfos',
                let: {
                  userId: '$_id',
                  loggedInUserId: loggedInUser._id,
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $or: [
                          {
                            $and: [
                              {
                                $eq: ['$$userId', '$userId'],
                              },
                              {
                                $eq: ['$connectionWithId', '$$loggedInUserId'],
                              },
                              {
                                $eq: [
                                  '$status',
                                  connectionRequestStatus.ACCEPT,
                                ],
                              },
                            ],
                          },
                          {
                            $and: [
                              {
                                $eq: ['$userId', '$$loggedInUserId'],
                              },
                              {
                                $eq: [
                                  '$status',
                                  connectionRequestStatus.ACCEPT,
                                ],
                              },
                              {
                                $eq: ['$$userId', '$connectionWithId'],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  },
                ],
                as: 'connectionInfo',
              },
            },
            {
              $unwind: {
                path: '$connectionInfo',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'followerinfos',
                let: {
                  userId: '$_id',
                  loggedInUserId: loggedInUser._id,
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $or: [
                          {
                            $and: [
                              {
                                $eq: ['$followerId', '$$userId'],
                              },
                              {
                                $eq: ['$followingId', '$$loggedInUserId'],
                              },
                              {
                                $eq: ['$status', StatusEnum.ACCEPT],
                              },
                            ],
                          },
                          {
                            $and: [
                              {
                                $eq: ['$followerId', '$$loggedInUserId'],
                              },
                              {
                                $eq: ['$followingId', '$$userId'],
                              },
                              {
                                $eq: ['$status', StatusEnum.ACCEPT],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  },
                ],
                as: 'followerInfo',
              },
            },
            {
              $unwind: {
                path: '$followerInfo',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $match: {
                $or: [
                  { connectionInfo: { $ne: null } },
                  { followerInfo: { $ne: null } },
                ],
              },
            },
          ],
        );
      }

      const usersData = await this.userModel.aggregate(pipeline);

      const totalResults = userCount;
      const currentResults = usersData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      // Adding sign url for user profile
      const users = await Promise.all(
        usersData.map(async (user) => {
          const notificationExist = await this.notificationModel.findOne({
            group: queryParam.groupId,
            receiver: user._id,
            action: InvitationType.INITIAL,
          });
          if (notificationExist) {
            user['isInvited'] = true;
          } else {
            user['isInvited'] = false;
          }
          if (user.profileImage) {
            user.profileImage = await this.awsService.signedUrl(
              user.profileImage.split('amazonaws.com/')[1],
            );
          }
          return {
            ...user,
            isInvited: !!notificationExist,
          };
        }),
      );

      return successResponse(
        users,
        CONSTANT.FETCHED_SUCCESSFULLY('Suggested users'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  // Invite User for group
  async inviteUser(req: any, body: InviteUserDto) {
    try {
      const { user: loggedInUser } = req;
      const { userId, groupId } = body;

      const user = await this.userModel.findById(userId);
      if (!user)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));

      const group = await this.groupModel.findById(groupId);
      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      const isGroupMember = await this.groupMemberModel.findOne({
        group: group._id,
        member: loggedInUser._id,
      });

      if (!isGroupMember) {
        throw new HttpException(
          CONSTANT.INVITE_NOT_ALLOW,
          HttpStatus.FORBIDDEN,
        );
      }

      let notificationMessage;
      // if (loggedInUser.firstName && loggedInUser.lastName) {
      //   notificationMessage = `${loggedInUser.firstName} ${loggedInUser.lastName} invite you to join the group ${group.name}`;
      // } else {
      //   notificationMessage = `${loggedInUser.businessOrganizationName} invite you to join the group ${group.name}`;
      // }

      await this.notificationModel.deleteMany({
        notificationType: NotificationsType.GROUP_INVITE,
        redirectionType: RedirectionType.GROUP,
        sender: loggedInUser._id,
        receiver: userId,
        group: groupId,
      });

      //save notification
      const params = {
        title: NOTIFICATION_MESSAGES.JOIN_GROUP,
        notificationMessage: '',
        notificationType: NotificationsType.GROUP_INVITE,
        redirectionType: RedirectionType.GROUP,
        receiver: userId,
        sender: loggedInUser._id,
        group: groupId,
        action: InvitationType.INITIAL,
        isGroupMember: false,
      };
      await this.notificationService.sendToTopic(null, params);

      return successResponse(null, CONSTANT.INVITE_SENT, HttpStatus.OK);
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  // Invite User via mail for group
  async inviteUserViaEmail(req: any, body: InviteUserViaEmialDto) {
    try {
      const { user: loggedInUser } = req;
      const { email, groupId, description } = body;

      const group = await this.groupModel.findById(groupId);
      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      const isGroupMember = await this.groupMemberModel.findOne({
        group: group._id,
        member: loggedInUser._id,
      });

      if (!isGroupMember) {
        throw new HttpException(
          CONSTANT.INVITE_NOT_ALLOW,
          HttpStatus.FORBIDDEN,
        );
      }

      const template = templates.joinGroup({
        groupName: group.name,
        userName: loggedInUser.userName,
        groupLink: group.shareableLink,
        imageUrl: loggedInUser.profileImage
          ? loggedInUser.profileImage
          : 'https://pepli-beta.s3.us-east-1.amazonaws.com/Pepli/871296137847.jpg',
        description,
        logoUrl:
          'https://pepli-beta.s3.us-east-1.amazonaws.com/Pepli/logo_tm.png',
      });
      await this.mailService.SendMail(email, 'Join Group', template);

      return successResponse(null, CONSTANT.INVITE_SENT, HttpStatus.OK);
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async joinGroup(req: any, body: JoinGroupDTO) {
    try {
      const { user: loggedInUser } = req;
      const { groupId } = body;

      const group = await this.groupModel.findById(groupId);

      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      if (group.privacy === Privacy.PRIVATE) {
        throw new HttpException(
          'You can not join private group',
          HttpStatus.FORBIDDEN,
        );
      }

      const isGroupMember = await this.groupMemberModel.findOne({
        group: group._id,
        member: loggedInUser._id,
      });

      if (isGroupMember) {
        throw new HttpException(
          CONSTANT.ALREADY_MEMBER('Group'),
          HttpStatus.FORBIDDEN,
        );
      }

      await this.groupMemberModel.create({
        group: group._id,
        member: loggedInUser._id,
      });

      const params = {
        title: NOTIFICATION_MESSAGES.JOINED_GROUP,
        notificationMessage: '',
        notificationType: NotificationsType.GROUP_JOIN,
        redirectionType: RedirectionType.GROUP,
        receiver: group.createdBy,
        sender: loggedInUser._id,
        group: groupId,
      };

      await this.notificationService.sendToTopic(null, params);

      return successResponse(
        null,
        CONSTANT.JOINED_SUCCESSFULLY('Group'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  // block group member
  async blockUnblockGroupMember(req: any, body: blockUnblockMemberDto) {
    try {
      const { user: loggedInUser } = req;
      const { groupId, memberId } = body;

      const group = await this.groupModel.findById(groupId);

      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      if (group.createdBy.toString() !== loggedInUser._id.toString()) {
        throw new HttpException(
          CONSTANT.NOT_ALLOW('Member block or unblock '),
          HttpStatus.BAD_REQUEST,
        );
      }

      if (group.createdBy.toString() === memberId.toString()) {
        throw new HttpException(
          CONSTANT.NOT_ALLOW('You can not block or unblock group owner'),
          HttpStatus.BAD_REQUEST,
        );
      }

      const isGroupMember = await this.groupMemberModel.findOne({
        group: group._id,
        member: new mongoose.Types.ObjectId(memberId),
      });

      if (!isGroupMember) {
        throw new HttpException(
          CONSTANT.MEMBER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      await this.groupMemberModel.updateOne(
        {
          group: group._id,
          member: new mongoose.Types.ObjectId(memberId),
        },
        {
          $set: { isBlocked: !isGroupMember.isBlocked },
        },
      );

      return successResponse(
        null,
        CONSTANT.SUCCESSFULL(
          `Member ${!isGroupMember.isBlocked ? 'Blocked' : 'Unblocked'}`,
        ),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async inviteAcceptReject(req: any, body: InviteAcceptRejectDto) {
    try {
      const { user: loggedInUser } = req;
      const { notificationId, invitationType } = body;

      const notification = await this.notificationModel
        .findOne({
          _id: notificationId,
          receiver: loggedInUser._id,
        })
        .populate('group');

      if (!notification)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Notification'));

      if (invitationType === InvitationType.REMOVE) {
        await this.groupMemberModel.deleteOne({
          group: notification.group,
          member: loggedInUser._id,
        });
        await this.notificationModel.deleteOne(notification._id);
      }

      if (invitationType === InvitationType.REJECT) {
        if (notification?.postId) {
          await this.PostService.removeCollaboratorsOrTagPeopleFromPost(
            loggedInUser._id.toString(),
            notification?.postId.toString(),
            notification.notificationType,
          );
        }
      }
      if (notification?.group) {
        if (invitationType === InvitationType.ACCEPT) {
          // check if user is already a member
          const isGroupMember = await this.groupMemberModel.findOne({
            group: notification.group,
            member: loggedInUser._id,
          });
          if (!isGroupMember) {
            await this.groupMemberModel.create({
              group: notification.group,
              member: loggedInUser._id,
            });
          }
        }
      }

      if (invitationType === InvitationType.ACCEPT) {
        await this.notificationModel.findOneAndUpdate(
          {
            _id: notification._id,
          },
          {
            $set: {
              action: invitationType,
              title: notificationMessage(
                notification,
                invitationType,
                loggedInUser,
                notification.group,
              ),
              isGroupMember: true,
            },
          },
          { new: true },
        );
      }
      if (invitationType === InvitationType.REJECT) {
        // await this.notificationModel.deleteOne(notification._id);
        await this.notificationModel.findOneAndUpdate(
          {
            _id: notification._id,
          },
          {
            $set: {
              action: invitationType,
              title: notificationMessage(
                notification,
                invitationType,
                loggedInUser,
                notification.group,
              ),
            },
          },
          { new: true },
        );
      }

      return successResponse(
        null,
        invitationType === InvitationType.ACCEPT
          ? CONSTANT.INVITE_ACCEPT
          : CONSTANT.INVITE_REJECT,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async removeMemberFromGroup(req, body: removeGroupMemberDto) {
    try {
      const { groupId, memberIds } = body;

      const group = await this.groupModel.findById(groupId);

      if (!group)
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

      await this.groupMemberModel.deleteMany({
        group: group._id,
        member: {
          $in: memberIds
            .map((id) => new mongoose.Types.ObjectId(id))
            .filter((m) => m.toString() !== group.createdBy.toString()),
        },
      });

      return successResponse(null, CONSTANT.REMOVE('Member'), HttpStatus.OK);
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  public async addPostInGroup(req: any, postData: addGroupPostDto) {
    try {
      const { user: loggedInUser } = req;

      if (postData.caption == null) {
        const { caption, ...restOfPostData } = postData;
        postData = restOfPostData as addGroupPostDto; // Cast to addGroupPostDto after removing caption
      }

      if (postData.media == null) {
        const { media, ...restOfPostData } = postData;
        postData = restOfPostData as addGroupPostDto; // Cast to addGroupPostDto after removing caption
      }

      const data = {
        ...postData,
        group: postData.groupId,
        userId: loggedInUser._id,
      };

      const group = await this.groupModel.findById(postData.groupId);

      if (!group) {
        throw new HttpException(CONSTANT.NOT_FOUND_MESSAGE('Group'), 404);
      }

      if (
        group.allowPosting === AllowPosting.Admin &&
        group.createdBy?.toString() !== loggedInUser._id?.toString()
      ) {
        throw new HttpException(CONSTANT.ONLY_ADMIN_CAN_POST_IN_GROUP, 400);
      }

      const post = await new this.postModel(data).save();

      const deepLink = await this.branchDeeplinkService.generateDeepLink(
        post._id.toString(),
        {
          title: loggedInUser.userName,
          description: post.caption,
          image_url:
            post.media[0]?.mediaType === 'video'
              ? post.media[0]?.thumbUrl
              : post.media[0]?.url,
        },
      );

      await this.postModel.findByIdAndUpdate(
        post._id,
        {
          shareableLink: deepLink,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      await this.groupModel.findByIdAndUpdate(post.group, {
        $inc: { posts: 1 },
      });

      return successResponse(
        null,
        CONSTANT.ADDED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  // Get group posts
  // async getGroupPosts(req, queryParam: GetGroupPostsDto) {
  //   try {
  //     const { user: loggedInUser } = req;
  //     const { groupId, search, sortBy, sort, page, perPage } = queryParam;

  //     const group = await this.groupModel.findById(groupId);
  //     if (!group)
  //       throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));

  //     // Default fields
  //     const searchFields = ['caption'];
  //     let matchObj = {
  //       group: group._id,
  //     };

  //     const sortParam = { sortBy, sort };
  //     const paginationParam = { page, perPage };

  //     // Create Search, Filter, Sort and Pagination
  //     const { searchObj, sortObj, filterObj, skipData, limitData } =
  //       createSearchFilterSortPagination(
  //         search,
  //         searchFields,
  //         null,
  //         sortParam,
  //         paginationParam,
  //       );

  //     const searchFilterObj = {
  //       ...searchObj,
  //       ...filterObj,
  //     };

  //     matchObj = { ...matchObj, ...searchFilterObj };

  //     const groupPostsCount = await this.postModel.countDocuments(matchObj);

  //     const groupPosts = await this.postModel
  //       .find(matchObj)
  //       .populate({
  //         path: 'group',
  //         select: 'name',
  //       })
  //       .populate({
  //         path: 'userId',
  //         select:
  //           'firstName lastName businessOrganizationName userName profileImage',
  //       })
  //       .sort(sortObj)
  //       .skip(skipData)
  //       .limit(limitData);

  //     // Adding the `isBookmark`, `isLike`, and `currentUserReaction` flags
  //     const groupPostsWithFlags = await Promise.all(
  //       groupPosts.map(async (post) => {
  //         // Check if the logged-in user has bookmarked the post
  //         const isBookmarkPost = await this.bookmarkModel.findOne({
  //           postId: post._id,
  //           userId: loggedInUser._id,
  //         });

  //         // Check if the logged-in user has reacted to the post (isLike)
  //         const isLike = post.reactions.some(
  //           (reaction) =>
  //             reaction.userId.toString() === loggedInUser._id.toString(),
  //         );

  //         // Get the currentUserReaction based on the logged-in user's reaction type
  //         const userReaction = post.reactions.find(
  //           (reaction) =>
  //             reaction.userId.toString() === loggedInUser._id.toString(),
  //         );
  //         const currentUserReaction = userReaction ? userReaction.type : null;

  //         // Add isBookmark, isLike, and currentUserReaction flags to each post
  //         return {
  //           ...post.toObject(),
  //           isBookmark: !!isBookmarkPost,
  //           isLike: isLike,
  //           currentUserReaction: currentUserReaction,
  //         };
  //       }),
  //     );

  //     // Pagination parameters
  //     const totalResults = groupPostsCount;
  //     const currentResults = groupPosts?.length;
  //     const totalPages = Math.ceil(totalResults / limitData);
  //     const currentPage = Number(page) || 1;

  //     const paginationObj = {
  //       totalResults,
  //       currentResults,
  //       totalPages,
  //       currentPage,
  //     };

  //     return successResponse(
  //       groupPostsWithFlags,
  //       CONSTANT.FETCHED_SUCCESSFULLY('Group posts'),
  //       HttpStatus.OK,
  //       paginationObj,
  //     );
  //   } catch (error) {
  //     throw new HttpException(error, error?.status || 500);
  //   }
  // }
  async getGroupPosts(req, queryParam: GetGroupPostsDto) {
    try {
      const { user: loggedInUser } = req;
      const { groupId, search, sortBy, sort, page, perPage } = queryParam;

      const group = await this.groupModel.findById(groupId);
      if (!group) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('Group'));
      }

      // Default fields
      const searchFields = ['caption', 'repostCaption']; // Include fields for both original and reposted captions
      let matchObj = { group: group._id };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      // Create Search, Filter, Sort, and Pagination
      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      // Count total matching documents
      const groupPostsCount = await this.postModel.countDocuments(matchObj);

      // Fetch group posts with the necessary fields
      const pipeline: PipelineStage[] = [
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
            pipeline: [
              {
                $project: {
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$group',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$userId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            collaborators: {
              $filter: {
                input: '$collaborators',
                as: 'collab',
                cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'collaborators.id',
            foreignField: '_id',
            as: 'collaborators',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedPeople',
            foreignField: '_id',
            as: 'taggedPeople',
            pipeline: [
              {
                $match: { isFakeAccount: false },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $sort: sortObj,
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
      ];

      const groupPosts = await this.postModel.aggregate(pipeline);

      // Process posts and add required flags and `repostBy` field
      const groupPostsWithFlags = await Promise.all(
        groupPosts.map(async (post) => {
          // Check if the logged-in user has bookmarked the post
          const isBookmarkPost = await this.bookmarkModel.findOne({
            postId: post._id,
            userId: loggedInUser._id,
          });

          // Check if the logged-in user has reacted to the post (isLike)
          const isLike = post.reactions.some(
            (reaction) =>
              reaction.userId.toString() === loggedInUser._id.toString(),
          );

          // Get the currentUserReaction based on the logged-in user's reaction type
          const userReaction = post.reactions.find(
            (reaction) =>
              reaction.userId.toString() === loggedInUser._id.toString(),
          );
          const currentUserReaction = userReaction ? userReaction.type : null;

          // Add repostBy information if the post is a repost
          const repostByUser = post.repostBy
            ? await this.userModel
                .findById(post.repostBy)
                .select(
                  'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
                )
                .lean()
            : null;

          // Return the post object with additional flags and repostBy
          return {
            ...post,
            isBookmark: !!isBookmarkPost,
            isLike: isLike,
            currentUserReaction: currentUserReaction,
            repostBy: repostByUser, // Attach repostBy user data
          };
        }),
      );

      // Pagination parameters
      const totalResults = groupPostsCount;
      const currentResults = groupPosts?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        groupPostsWithFlags,
        CONSTANT.FETCHED_SUCCESSFULLY('Group posts'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async getGroupRules() {
    try {
      const groupRules = await this.groupRuleModel.aggregate([
        {
          $addFields: {
            titleNumber: {
              $toInt: {
                $arrayElemAt: [{ $split: ['$title', '.'] }, 0],
              },
            },
            titleSuffix: {
              $trim: {
                input: {
                  $arrayElemAt: [{ $split: ['$title', '.'] }, 1],
                },
              },
            },
          },
        },
        {
          $sort: {
            titleNumber: 1,
          },
        },
        {
          $setWindowFields: {
            sortBy: { titleNumber: 1 },
            output: {
              newIndex: { $documentNumber: {} },
            },
          },
        },
        {
          $addFields: {
            title: {
              $concat: [{ $toString: '$newIndex' }, '. ', '$titleSuffix'],
            },
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            explanation: 1,
          },
        },
      ]);

      return successResponse(
        groupRules,
        CONSTANT.FETCHED_SUCCESSFULLY('Group Rule'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async repostInGroup(req: any, postData: repostInGroupDto) {
    try {
      const { user: loggedInUser } = req;

      // Find the existing post in the group
      const postDetails = await this.postModel.findOne({
        _id: postData.postId,
        group: postData.groupId,
      }); // Use `.lean()` to convert the Mongoose document into a plain JavaScript object

      if (!postDetails) {
        throw new HttpException(
          { message: 'Group post not found' },
          HttpStatus.NOT_FOUND,
        );
      }

      // Increment repost count on the original post atomically
      const originalPostCount = await this.postModel.findByIdAndUpdate(
        postDetails._id,
        {
          $inc: { repostCount: 1 },
        },
        { new: true },
      );
      const repostCount = originalPostCount.repostCount;

      const repostObj = {
        caption: postDetails.caption,
        location: postDetails.location,
        media: postDetails.media,
        shareableLink: postDetails.shareableLink,
        collaborators: postDetails.collaborators,
        whoCanComment: postDetails.whoCanComment,
        isTurnOffComment: postDetails.isTurnOffComment,
        isHideLikeViews: postDetails.isHideLikeViews,
        isDisableRepostShare: postDetails.isDisableRepostShare,
        postLabel: postDetails.postLabel,
        taggedPeople: postDetails.taggedPeople,
        fundraisers: postDetails.fundraisers,
        taggedProduct: postDetails.taggedProduct,
        aiLabels: postDetails.aiLabels,
        userId: postDetails.userId,
        repostBy: loggedInUser._id,
        post: postDetails._id,
        repostCount: repostCount,
        group: postDetails.group,
      };
      if (postData.repostCaption) {
        repostObj['repostCaption'] = postData.repostCaption;
      }

      // Increment repost count to all re-posts atomically
      await this.postModel.updateMany(
        { post: postDetails._id },
        {
          $set: { repostCount: repostCount },
        },
      );

      const repost = await this.postModel.create(repostObj);

      const createdRepost = await this.postModel
        .findOne({ _id: repost._id })
        .populate({
          path: 'userId',
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
          match: { isFakeAccount: false },
        })
        .populate({
          path: 'repostBy',
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
          match: { isFakeAccount: false },
        });

      return successResponse(
        createdRepost,
        CONSTANT.ADDED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
