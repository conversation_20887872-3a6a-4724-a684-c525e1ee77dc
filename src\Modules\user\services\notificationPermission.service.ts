import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { NotificationPermission, NotificationPermissionDocument, UserTypeEnum } from 'src/Models/notificationPermission.schema';
import { UserNotificationPermission, UserNotificationPermissionDocument } from 'src/Models/userNotificationPermission.schema';
import { userDocument } from 'src/Models/user.schema';
import CONSTANT from '../../../common/constant/common.constant';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import { SaveUserNotificationPermissionsDto, GetDefaultPermissionsDto } from '../dtos/notificationPermission.dto';
import { defaultNotificationPermissions } from '../../../seeders/notificationPermissions.seeder';

@Injectable()
export class NotificationPermissionService {
  constructor(
    @InjectModel('NotificationPermission') 
    private readonly notificationPermissionModel: Model<NotificationPermissionDocument>,
    @InjectModel('UserNotificationPermission') 
    private readonly userNotificationPermissionModel: Model<UserNotificationPermissionDocument>,
    @InjectModel('User') 
    private readonly userModel: Model<userDocument>,
  ) {}

  /**
   * Save user notification permissions
   */
  async saveUserNotificationPermissions(req: any, data: SaveUserNotificationPermissionsDto) {
    try {
      const { user: loggedInUser } = req;
      const { permissions } = data;
      const permissionsArray = permissions as Array<{ permissionId: string; isEnabled: boolean }>;

      // Validate that all permission IDs exist
      const permissionIds = permissionsArray.map(p => new mongoose.Types.ObjectId(p.permissionId));
      const existingPermissions = await this.notificationPermissionModel.find({
        _id: { $in: permissionIds }
      });

      if (existingPermissions.length !== permissionIds.length) {
        throw new HttpException(
          'Some permission IDs are invalid or inactive',
          HttpStatus.BAD_REQUEST
        );
      }

      // Convert to the format expected by the schema
      const permissionSettings = permissionsArray.map(p => ({
        permissionId: new mongoose.Types.ObjectId(p.permissionId),
        isEnabled: p.isEnabled
      }));

      // Upsert user notification permissions
      const result = await this.userNotificationPermissionModel.findOneAndUpdate(
        { userId: loggedInUser._id },
        { 
          permissions: permissionSettings,
          isActive: true
        },
        { 
          upsert: true, 
          new: true,
          runValidators: true 
        }
      );

      return successResponse(
        result,
        CONSTANT.UPDATED_SUCCESSFULLY('Notification permissions'),
        HttpStatus.OK
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  /**
   * Get user's notification permissions
   */
  async getUserNotificationPermissions(req: any) {
    try {
      const { user: loggedInUser } = req;

      const userPermissions = await this.userNotificationPermissionModel.findOne({
        userId: loggedInUser._id
      });

      if (!userPermissions) {
        return successResponse(
          { permissions: [] },
          CONSTANT.FETCHED_SUCCESSFULLY('User notification permissions'),
          HttpStatus.OK
        );
      }

      // Filter only enabled permissions and populate permission details
      const enabledPermissionIds = userPermissions.permissions
        .filter(p => p.isEnabled)
        .map(p => p.permissionId);

      if (enabledPermissionIds.length === 0) {
        return successResponse(
          { permissions: [] },
          CONSTANT.FETCHED_SUCCESSFULLY('User notification permissions'),
          HttpStatus.OK
        );
      }

      // Get permission details for enabled permissions
      const permissionDetails = await this.notificationPermissionModel.find({
        _id: { $in: enabledPermissionIds }
      });

      // Map enabled permissions with their details
      const enabledPermissions = userPermissions.permissions
        .filter(p => p.isEnabled)
        .map(p => {
          const details = permissionDetails.find(d => d._id.toString() === p.permissionId.toString());
          return {
            permissionId: p.permissionId,
            isEnabled: p.isEnabled,
            permissionName: details ? details.permissionName : ''
          };
        });

      const result = {
        _id: userPermissions._id,
        userId: userPermissions.userId,
        permissions: enabledPermissions
      };

      return successResponse(
        result,
        CONSTANT.FETCHED_SUCCESSFULLY('User notification permissions'),
        HttpStatus.OK
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  /**
   * Get default permissions based on user type
   */
  async getDefaultPermissions(data: GetDefaultPermissionsDto) {
    try {
      const { userType } = data;

      const permissions = await this.notificationPermissionModel.find({
        userType: userType
      }).sort({ createdAt: 1 });

      return successResponse(
        permissions,
        CONSTANT.FETCHED_SUCCESSFULLY('Default notification permissions'),
        HttpStatus.OK
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  /**
   * Create default notification permissions for new user signup
   */
  async createDefaultPermissionsForNewUser(userId: string, userType: string) {
    try {
      // Get all permissions for the user type
      const defaultPermissions = await this.notificationPermissionModel.find({
        userType: userType
      });

      if (defaultPermissions.length === 0) {
        return; // No permissions found for this user type
      }

      // Create permission settings with all permissions disabled by default
      const permissionSettings = defaultPermissions.map(permission => ({
        permissionId: permission._id,
        isEnabled: false
      }));

      // Create user notification permissions document
      await this.userNotificationPermissionModel.create({
        userId: new mongoose.Types.ObjectId(userId),
        permissions: permissionSettings
      });

    } catch (error) {
      // Log error but don't throw - don't block user signup
      console.error('Error creating default notification permissions:', error);
    }
  }


} 