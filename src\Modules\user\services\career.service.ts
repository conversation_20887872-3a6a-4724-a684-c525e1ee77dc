import { HttpException, HttpStatus, Injectable } from '@nestjs/common';

import { successResponse } from 'src/Custom/helpers/responseHandler';
import {
  educationDto,
  volunteerExperienceDto,
  HonorAndRewardDto,
  licenseCertificationDto,
  experienceDto,
} from 'src/Modules/user/dtos/career.dto';
import CONSTANT from '../../../common/constant/common.constant';
import mongoose, { Model } from 'mongoose';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { EducationRepository } from 'src/Repositories/education.repository';
import { LicenseCertificationRepository } from 'src/Repositories/licenceCertification.repository';
import { VolunteerExperienceRepository } from 'src/Repositories/volunteer-experience.repository';
import { HonorAndAwardRepository } from 'src/Repositories/honorAndAward.repository';
import { ExperienceRepository } from 'src/Repositories/experience.repository';
import { UserRepository } from 'src/Repositories/user.repository';
import { PeopleDocument } from 'src/Models/peoples.schema';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class CareerService {
  constructor(
    private readonly educationRepository: EducationRepository,

    private readonly licenseCertificationRepository: LicenseCertificationRepository,

    private readonly volunteerExperienceRepository: VolunteerExperienceRepository,

    private readonly userRepository: UserRepository,

    private readonly experienceRepository: ExperienceRepository,

    private readonly honorAndAwardRepository: HonorAndAwardRepository,

    @InjectModel('People')
    private readonly peopleModel: Model<PeopleDocument>,
  ) {}

  // Add/Update Education
  public async addEditEducation(req, educationData: educationDto) {
    try {
      const { user: loggedInUser } = req;

      const { _id, school, ...restEducationData } = educationData;
      let project;

      if (
        Array.isArray(educationData.skills) &&
        educationData.skills.length > 0
      ) {
        const updatedSkills = await this.userRepository.updateSkills(
          loggedInUser._id,
          educationData.skills,
        );
        educationData.skills = Array.isArray(updatedSkills)
          ? updatedSkills
          : [];
      }

      if (educationData.isAlum) {
        const isExist = await this.peopleModel.findOne({
          parentUserId: new mongoose.Types.ObjectId(educationData.school),
          childUserId: loggedInUser._id,
        });

        if (!isExist) {
          await this.peopleModel.create({
            parentUserId: new mongoose.Types.ObjectId(educationData.school),
            childUserId: loggedInUser._id,
            isAlumni: true,
            isStudent: false,
          });
        } else {
          await this.peopleModel.findByIdAndUpdate(isExist._id, {
            isAlumni: true,
            isStudent: false,
          });
        }
      } else {
        const isExist = await this.peopleModel.findOne({
          parentUserId: new mongoose.Types.ObjectId(educationData.school),
          childUserId: loggedInUser._id,
        });
        if (!isExist) {
          await this.peopleModel.create({
            parentUserId: new mongoose.Types.ObjectId(educationData.school),
            childUserId: loggedInUser._id,
            isStudent: true,
            isAlumni: false,
          });
        } else {
          await this.peopleModel.findByIdAndUpdate(isExist._id, {
            isStudent: true,
            isAlumni: false,
          });
        }
      }

      if (educationData.fieldOfStudy) {
        await this.userRepository.updateFieldOfStudy(loggedInUser._id, [
          educationData.fieldOfStudy,
        ]);
      }

      if (_id) {
        const projectExist = await this.educationRepository.findById(_id);
        if (!projectExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Education'),
            HttpStatus.BAD_REQUEST,
          );
        }

        project = await this.educationRepository.findByFilterAndUpdate(
          { _id: _id, userId: loggedInUser._id },
          { ...restEducationData, school: new mongoose.Types.ObjectId(school) },
        );

        return successResponse(
          project,
          CONSTANT.UPDATED_SUCCESSFULLY('Education'),
          HttpStatus.OK,
        );
      }
      const data = {
        ...restEducationData,
        school: new mongoose.Types.ObjectId(school),
        userId: loggedInUser._id,
      };

      project = await this.educationRepository.create(data);

      return successResponse(
        project,
        CONSTANT.ADDED_SUCCESSFULLY('Education'),
        HttpStatus.OK,
      );
    } catch (error) {
      console.log(error);
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeEducation(req: any, id: string) {
    try {
      const { user: loggedInUser } = req;

      const isExist = await this.educationRepository.findById(id);

      if (
        !isExist ||
        isExist.userId.toString() !== loggedInUser._id.toString()
      ) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Education'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.educationRepository.deleteById(id);

      return successResponse(
        {},
        CONSTANT.DELETED_SUCCESSFULLY('Education'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  // Add/Update License Certification
  public async addEditLicenseCertification(
    req,
    licenseData: licenseCertificationDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      const { id, ...restLicenseData } = licenseData;

      let license;

      if (Array.isArray(licenseData.skills) && licenseData.skills.length > 0) {
        const updatedSkills = await this.userRepository.updateSkills(
          loggedInUser._id,
          licenseData.skills,
        );
        licenseData.skills = Array.isArray(updatedSkills) ? updatedSkills : [];
      }

      if (id) {
        const projectExist = await this.licenseCertificationRepository.findById(
          id,
        );

        if (!projectExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('License & Certification'),
            HttpStatus.BAD_REQUEST,
          );
        }

        license =
          await this.licenseCertificationRepository.findByFilterAndUpdate(
            { _id: id, userId: loggedInUser._id },
            { ...restLicenseData },
          );

        return successResponse(
          license,
          CONSTANT.UPDATED_SUCCESSFULLY('License & Certification'),
          HttpStatus.OK,
        );
      }
      const data = {
        ...restLicenseData,
        userId: loggedInUser._id,
      };

      license = await this.licenseCertificationRepository.create(data);

      return successResponse(
        license,
        CONSTANT.ADDED_SUCCESSFULLY('License & Certification'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeLicenseCertification(req, id: string) {
    try {
      const { user: loggedInUser } = req;

      const isExist = await this.licenseCertificationRepository.findById(id);

      if (
        !isExist ||
        isExist.userId.toString() !== loggedInUser._id.toString()
      ) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('License & Certification'),
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!isExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('License & Certification'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.licenseCertificationRepository.deleteById(id);

      return successResponse(
        {},
        CONSTANT.DELETED_SUCCESSFULLY('License & Certification'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllLicenseCertification(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['name'];
      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = { ...searchObj, ...filterObj };
      matchObj = { ...matchObj, ...searchFilterObj };

      const licenseCertifications =
        await this.licenseCertificationRepository.findLicenseCertifications(
          matchObj,
          sortObj,
          skipData,
          limitData,
        );

      const jobsCount =
        await this.licenseCertificationRepository.countDocuments(matchObj);

      const paginationObj = {
        totalResults: jobsCount,
        currentResults: licenseCertifications.length,
        totalPages: Math.ceil(jobsCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        licenseCertifications,
        CONSTANT.FETCHED_SUCCESSFULLY('License & Certifications'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  public async addEditHonorAndAward(
    req,
    honorAndRewardData: HonorAndRewardDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      const { _id, ...restHonorAndRewardData } = honorAndRewardData;

      let entry;

      if (_id) {
        const isExist = await this.honorAndAwardRepository.findById(_id);
        if (!isExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Honor & Reward'),
            HttpStatus.BAD_REQUEST,
          );
        }

        entry = await this.honorAndAwardRepository.findByFilterAndUpdate(
          { _id, userId: loggedInUser._id },
          { ...restHonorAndRewardData },
        );

        return successResponse(
          entry,
          CONSTANT.UPDATED_SUCCESSFULLY('Honor & Award'),
          HttpStatus.OK,
        );
      }
      const data = {
        ...restHonorAndRewardData,
        userId: loggedInUser._id,
      };

      entry = await this.honorAndAwardRepository.create(data);

      return successResponse(
        entry,
        CONSTANT.ADDED_SUCCESSFULLY('Honor & Award'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeHonorAndAward(id: string) {
    try {
      const isExist = await this.honorAndAwardRepository.findById(id);

      if (!isExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Honor & Award'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.honorAndAwardRepository.deleteById(id);

      return successResponse(
        {},
        CONSTANT.DELETED_SUCCESSFULLY('Honor & Award'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  // Add/Update Experience Certification
  public async addEditVolunteerExperience(
    req,
    experienceData: volunteerExperienceDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      const { _id: id, organization, ...restExperienceData } = experienceData;

      let experience;

      const organizationObjectIds = organization.map(
        (org) => new mongoose.Types.ObjectId(org),
      );

      if (id) {
        const experienceExist =
          await this.volunteerExperienceRepository.findById(id);

        if (!experienceExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Experience'),
            HttpStatus.BAD_REQUEST,
          );
        }

        experience =
          await this.volunteerExperienceRepository.findByFilterAndUpdate(
            { _id: id, userId: loggedInUser._id },
            { ...restExperienceData, organization: organizationObjectIds },
          );

        return successResponse(
          experience,
          CONSTANT.UPDATED_SUCCESSFULLY('Volunteer Experience'),
          HttpStatus.OK,
        );
      }
      const data = {
        ...restExperienceData,
        organization: organizationObjectIds,
        userId: loggedInUser._id,
      };

      experience = await this.volunteerExperienceRepository.create(data);

      return successResponse(
        experience,
        CONSTANT.ADDED_SUCCESSFULLY('Volunteer Experience'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addEditExperience(req, experienceData: experienceDto) {
    try {
      const { user: loggedInUser } = req;

      const { id, ...restExperienceData } = experienceData;
      let experience;

      if (
        Array.isArray(experienceData.skills) &&
        experienceData.skills.length > 0
      ) {
        const updatedSkills = await this.userRepository.updateSkills(
          loggedInUser._id,
          experienceData.skills,
        );
        experienceData.skills = Array.isArray(updatedSkills)
          ? updatedSkills
          : [];
      }
      if (id) {
        const experienceExist = await this.experienceRepository.findById(id);

        if (!experienceExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Experience'),
            HttpStatus.BAD_REQUEST,
          );
        }

        experience = await this.experienceRepository.findByFilterAndUpdate(
          { _id: id, userId: loggedInUser._id },
          { ...restExperienceData },
        );

        if (experienceData.skills.length > 0) {
          const updatedSkills = await this.userRepository.updateSkills(
            loggedInUser._id,
            experienceData.skills,
          );
          experienceData.skills = Array.isArray(updatedSkills)
            ? updatedSkills
            : [];
        }

        return successResponse(
          experience,
          CONSTANT.UPDATED_SUCCESSFULLY('Experience'),
          HttpStatus.OK,
        );
      }
      const data = {
        ...restExperienceData,
        userId: loggedInUser._id,
      };

      experience = await this.experienceRepository.create(data);

      if (experienceData.skills.length > 0) {
        const updatedSkills = await this.userRepository.updateSkills(
          loggedInUser._id,
          experienceData.skills,
        );
        experienceData.skills = Array.isArray(updatedSkills)
          ? updatedSkills
          : [];
      }

      return successResponse(
        experience,
        CONSTANT.ADDED_SUCCESSFULLY('Experience'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllExperiences(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['title'];
      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = { ...searchObj, ...filterObj };
      matchObj = { ...matchObj, ...searchFilterObj };

      const experiences = await this.experienceRepository.findExperiences(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      const jobsCount = await this.experienceRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults: jobsCount,
        currentResults: experiences.length,
        totalPages: Math.ceil(jobsCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        experiences,
        CONSTANT.FETCHED_SUCCESSFULLY('Experiences'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  public async removeExperience(req: any, id: string) {
    try {
      const { user: loggedInUser } = req;

      const isExist = await this.experienceRepository.findById(id);

      if (
        !isExist ||
        isExist.userId.toString() !== loggedInUser._id.toString()
      ) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Job Post'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.experienceRepository.deleteById(id);

      return successResponse(
        {},
        CONSTANT.DELETED_SUCCESSFULLY('Experience'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllVolunteerExperiences(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['organization'];
      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = { ...searchObj, ...filterObj };
      matchObj = { ...matchObj, ...searchFilterObj };

      const experiences =
        await this.volunteerExperienceRepository.findExperiences(
          matchObj,
          sortObj,
          skipData,
          limitData,
        );

      const jobsCount = await this.volunteerExperienceRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults: jobsCount,
        currentResults: experiences.length,
        totalPages: Math.ceil(jobsCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        experiences,
        CONSTANT.FETCHED_SUCCESSFULLY('Volunteer Experiences'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  public async removeVolunteerExperience(req: any, id: string) {
    try {
      const { user: loggedInUser } = req;

      const isExist = await this.volunteerExperienceRepository.findById(id);

      if (
        !isExist ||
        isExist.userId.toString() !== loggedInUser._id.toString()
      ) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Job Post'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.volunteerExperienceRepository.deleteById(id);

      return successResponse(
        {},
        CONSTANT.DELETED_SUCCESSFULLY('Volunteer Experience'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllEducations(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['school'];
      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = { ...searchObj, ...filterObj };
      matchObj = { ...matchObj, ...searchFilterObj };

      const educations = await this.educationRepository.findEducations(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      const jobsCount = await this.educationRepository.countDocuments(matchObj);

      const paginationObj = {
        totalResults: jobsCount,
        currentResults: educations.length,
        totalPages: Math.ceil(jobsCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        educations,
        CONSTANT.FETCHED_SUCCESSFULLY('Educations'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  public async getAllHonorAndAward(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['title'];
      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = { ...searchObj, ...filterObj };
      matchObj = { ...matchObj, ...searchFilterObj };

      const honorAndawards =
        await this.honorAndAwardRepository.findHonorsAndAWards(
          matchObj,
          sortObj,
          skipData,
          limitData,
        );

      const jobsCount = await this.honorAndAwardRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults: jobsCount,
        currentResults: honorAndawards.length,
        totalPages: Math.ceil(jobsCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        honorAndawards,
        CONSTANT.FETCHED_SUCCESSFULLY('Honor & Awards'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
