# Organization API Documentation

This document provides detailed documentation for the organization-related API endpoints in the Pepli Node application.

## Table of Contents

1. [Overview](#overview)
2. [Data Models](#data-models)
3. [API Endpoints](#api-endpoints)
   - [Get Organization Categories](#get-organization-categories)
   - [Get Organization Subcategories](#get-organization-subcategories)
   - [Get Organizations by Subcategory](#get-organizations-by-subcategory)
4. [Database Schema](#database-schema)
5. [Examples](#examples)
6. [Error Handling](#error-handling)

## Overview

The Organization API provides endpoints to manage and retrieve organization-related data in the Pepli Node application. The system uses a **3-level hierarchical structure** with main categories, subcategories, and organizations to organize different types of organizations.

**Important Note:** The organization endpoints return comprehensive user profile data similar to the `/api/v1/user/account/:id` endpoint, including detailed profile information, analytics, social media data, contact information, and other profile-related fields.

### Organization Hierarchy (3-Level Structure)

```
Level 1: Main Categories
├── Union (an Organization / Not an individual member)
├── Affiliate (Organization)
├── Affiliate (Business)
└── School / Training Facility

Level 2: Subcategories
├── Union Types
│   ├── Screen Actors Guild (SAG)
│   ├── American Federation of Television and Radio Artists (AFTRA)
│   ├── Directors Guild of America (DGA)
│   ├── Writers Guild of America (WGA)
│   ├── International Alliance of Theatrical Stage Employees (IATSE)
│   └── Actors' Guild of Nigeria (AGN)
├── Affiliate Organization Types
│   ├── Professional Association
│   ├── Industry Group
│   ├── Trade Organization
│   └── Non-Profit Arts Organization
├── Affiliate Business Types
│   ├── Production Company
│   ├── Talent Agency
│   ├── Casting Agency
│   ├── Entertainment Business
│   └── Media Company
└── School/Training Facility Types
    ├── Acting School
    ├── Film School
    ├── Music School
    └── Dance School

Level 3: Organizations
├── SAG Organizations
│   ├── Screen Actors Guild-American Federation of Television and Radio Artists (SAG-AFTRA)
│   ├── SAG Foundation
│   └── SAG-AFTRA Conservatory
├── AGN Organizations
│   ├── Actors' Guild of Nigeria (AGN) - National
│   ├── AGN Lagos Chapter
│   └── AGN Abuja Chapter
├── Production Company Organizations
│   ├── Warner Bros. Pictures
│   ├── Paramount Pictures
│   └── Universal Pictures
└── Acting School Organizations
    ├── Juilliard School
    ├── American Academy of Dramatic Arts
    └── Stella Adler Studio of Acting
```

## Data Models

### UserSignupData Schema

```typescript
interface UserSignupData {
  _id: ObjectId;
  title: string;                    // e.g., "who_are_you", "union_subcategory"
  parentSlug: string[];             // e.g., ["4"], ["union_1"]
  itemText: string;                 // e.g., "Union (an Organization / Not an individual member)"
  slug: string;                     // e.g., "union_1", "sag_union"
  selectionType: SelectionType;     // "all", "only", "none", "multiple"
  subCategory: SubCategoryType;     // "main_category", "union_type", etc.
  createdAt: Date;
  updatedAt: Date;
}

enum SelectionType {
  ALL = 'all',
  ONLY = 'only',
  NONE = 'none',
  MULTIPLE = 'multiple'
}

enum SubCategoryType {
  MAIN_CATEGORY = 'main_category',
  UNION_TYPE = 'union_type',
  AFFILIATE_ORG_TYPE = 'affiliate_org_type',
  AFFILIATE_BUSINESS_TYPE = 'affiliate_business_type',
  SCHOOL_TRAINING_TYPE = 'school_training_type'
}
```

### User Schema (Organization-related fields)

```typescript
interface User {
  _id: ObjectId;
  iAmMember: iAmMemberEnum;
  businessOrganizationName: string;
  userName: string;
  isFakeAccount: boolean;
  signUpData: Signupdata[];
  organizationMemberships: OrganizationMembership[];
  // ... other fields
}

interface Signupdata {
  itemId: ObjectId;           // Reference to UserSignupData
  isSelected: boolean;
  subCategory?: string;
  title?: string;
  slug?: string;
  itemText?: string;
}

interface OrganizationMembership {
  mainCategory: string;       // e.g., "union_1", "affiliate_organization_1"
  subCategory: string;        // e.g., "sag_union", "production_company"
  organizationName: string;   // e.g., "Screen Actors Guild"
  memberId: string;           // User's member ID in this organization
  memberSince: string;        // Date joined
  isActive: boolean;          // Current membership status
  membershipLevel: string;    // e.g., "Regular", "Premium", "Student"
}

enum iAmMemberEnum {
  UNION_AFFILIATE_MEMBER = 'unionAffiliateMember',
  NONUNION_INDIVIDUAL_HOBBYIST = 'nonUnionIndividualHobbyist',
  AUDIENCE_MEMBER_FAN = 'audienceMemberFan',
  UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY = 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility'
}
```

## API Endpoints

### 1. Get Organization Categories

**Endpoint:** `GET /api/v1/user/organization-categories`

**Description:** Retrieves all main organization categories (Level 1).

**Authentication:** Not required

**Query Parameters:** None

**Response Schema:**
```typescript
interface OrganizationCategoriesResponse {
  success: boolean;
  message: string;
  data: UserSignupData[];
  statusCode: number;
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Organization Categories fetched successfully",
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "title": "who_are_you",
      "parentSlug": ["4"],
      "itemText": "Union (an Organization / Not an individual member)",
      "slug": "union_1",
      "selectionType": "only",
      "subCategory": "main_category",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439012",
      "title": "who_are_you",
      "parentSlug": ["4"],
      "itemText": "Affiliate (Organization)",
      "slug": "affiliate_organization_1",
      "selectionType": "only",
      "subCategory": "main_category",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439013",
      "title": "who_are_you",
      "parentSlug": ["4"],
      "itemText": "Affiliate (Business)",
      "slug": "affiliate_business_1",
      "selectionType": "only",
      "subCategory": "main_category",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439014",
      "title": "who_are_you",
      "parentSlug": ["4"],
      "itemText": "School / Training Facility",
      "slug": "school_training_facility_1",
      "selectionType": "only",
      "subCategory": "main_category",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "statusCode": 200
}
```

### 2. Get Organization Subcategories

**Endpoint:** `GET /api/v1/user/organization-subcategories/{mainCategorySlug}`

**Description:** Retrieves subcategories for a specific main organization category.

**Authentication:** Not required

**Path Parameters:**
- `mainCategorySlug` (string, required): The slug of the main category (e.g., "union_1", "affiliate_organization_1")

**Query Parameters:** None

**Response Schema:**
```typescript
interface OrganizationSubcategoriesResponse {
  success: boolean;
  message: string;
  data: UserSignupData[];
  statusCode: number;
}
```

**Example Request:**
```
GET /api/v1/user/organization-subcategories/union_1
```

**Example Response:**
```json
{
  "success": true,
  "message": "Organization Subcategories fetched successfully",
  "data": [
    {
      "_id": "507f1f77bcf86cd799439021",
      "title": "union_subcategory",
      "parentSlug": ["union_1"],
      "itemText": "Screen Actors Guild (SAG)",
      "slug": "sag_union",
      "selectionType": "multiple",
      "subCategory": "sub_type",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439022",
      "title": "union_subcategory",
      "parentSlug": ["union_1"],
      "itemText": "American Federation of Television and Radio Artists (AFTRA)",
      "slug": "aftra_union",
      "selectionType": "multiple",
      "subCategory": "sub_type",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439023",
      "title": "union_subcategory",
      "parentSlug": ["union_1"],
      "itemText": "Directors Guild of America (DGA)",
      "slug": "dga_union",
      "selectionType": "multiple",
      "subCategory": "sub_type",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "statusCode": 200
}
```

### 3. Get Organizations by Subcategory

**Endpoint:** `GET /api/v1/user/organizations-by-subcategory/{level2SubcategorySlug}`

**Description:** Retrieves all Level 3 organizations under a specific Level 2 subcategory.

**Authentication:** Not required

**Path Parameters:**
- `level2SubcategorySlug` (string, required): The slug of the Level 2 subcategory (e.g., "sag_union", "agn_union", "production_company")

**Query Parameters:** None

**Response Schema:**
```typescript
interface OrganizationsBySubcategoryResponse {
  success: boolean;
  message: string;
  data: UserSignupData[];
  statusCode: number;
}
```

**Example Request:**
```
GET /api/v1/user/organizations-by-subcategory/agn_union
```

**Example Response:**
```json
{
  "success": true,
  "message": "Level 3 Organizations fetched successfully",
  "data": [
    {
      "_id": "507f1f77bcf86cd799439051",
      "title": "agn_organization",
      "parentSlug": ["agn_union"],
      "itemText": "Actors' Guild of Nigeria (AGN) - National",
      "slug": "agn_national",
      "selectionType": "multiple",
      "subCategory": "organization",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439052",
      "title": "agn_organization",
      "parentSlug": ["agn_union"],
      "itemText": "AGN Lagos Chapter",
      "slug": "agn_lagos_chapter",
      "selectionType": "multiple",
      "subCategory": "organization",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "_id": "507f1f77bcf86cd799439053",
      "title": "agn_organization",
      "parentSlug": ["agn_union"],
      "itemText": "AGN Abuja Chapter",
      "slug": "agn_abuja_chapter",
      "selectionType": "multiple",
      "subCategory": "organization",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "statusCode": 200
}
```

## Database Schema

### Collections

#### 1. usersignupdata
Stores the hierarchical organization category and subcategory data.

**Indexes:**
- `parentSlug` - For efficient category lookup
- `slug` - For unique subcategory identification
- `subCategory` - For filtering by category type

**Sample Document:**
```json
{
  "_id": ObjectId("507f1f77bcf86cd799439021"),
  "title": "union_subcategory",
  "parentSlug": ["union_1"],
  "itemText": "Screen Actors Guild (SAG)",
  "slug": "sag_union",
  "selectionType": "multiple",
  "subCategory": "sub_type",
  "createdAt": ISODate("2024-01-01T00:00:00.000Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00.000Z")
}
```

#### 2. users
Stores user/organization profiles with comprehensive profile information.

**Indexes:**
- `iAmMember` - For filtering organization accounts
- `isFakeAccount` - For excluding fake accounts
- `businessOrganizationName` - For search functionality
- `userName` - For search functionality
- `signUpData.slug` - For filtering by subcategory

**Sample Document:**
```json
{
  "_id": ObjectId("507f1f77bcf86cd799439031"),
  "iAmMember": "unionAffiliateOrganizationBusinessSchoolsTrainingFacility",
  "businessOrganizationName": "Screen Actors Guild",
  "userName": "sag_official",
  "isFakeAccount": false,
  "signUpData": [
    {
      "itemId": ObjectId("507f1f77bcf86cd799439021"),
      "isSelected": true,
      "subCategory": "sub_type",
      "title": "union_subcategory",
      "slug": "sag_union",
      "itemText": "Screen Actors Guild (SAG)"
    }
  ],
  "organizationMemberships": [
    {
      "mainCategory": "union_1",
      "subCategory": "sag_union",
      "organizationName": "Screen Actors Guild",
      "memberId": "SAG123456",
      "memberSince": "2020-01-15",
      "isActive": true,
      "membershipLevel": "Regular"
    }
  ],
  "followers": 15000,
  "following": 500,
  "connections": 1200,
  "accountVerified": true,
  "professions": ["Acting Union", "Entertainment"],
  "analytics": {
    "viewCount": 25000,
    "viewByMeCount": 5000,
    "visibility": "public"
  },
  "socialMedia": {
    "website": "https://www.sagaftra.org",
    "facebook": "https://facebook.com/sagaftra",
    "instagram": "https://instagram.com/sagaftra",
    "linkedin": "https://linkedin.com/company/sagaftra",
    "twitter": "https://twitter.com/sagaftra",
    "youtube": "https://youtube.com/sagaftra"
  },
  "userProfileAboutYou": {
    "website": "https://www.sagaftra.org",
    "description": "Screen Actors Guild-American Federation of Television and Radio Artists (SAG-AFTRA) is the premier labor union representing performers.",
    "industry": "Entertainment",
    "founded": "2012",
    "skills": ["Acting", "Performance", "Union Representation", "Labor Rights"]
  },
  "contactInfo": [
    {
      "slug": "main_office",
      "name": "SAG-AFTRA National Office",
      "address": "5757 Wilshire Blvd, Los Angeles, CA 90036",
      "phoneNumber": "******-549-6644",
      "email": "<EMAIL>",
      "isSelected": true
    }
  ]
}
```

## Database Schema & API Relationships

### How the 3 API Endpoints Are Linked

The three organization API endpoints work together in a hierarchical chain, each building upon the previous level's data:

```
Level 1 → Level 2 → Level 3
   ↓        ↓        ↓
API 1    API 2    API 3
```

### **Complete Data Flow:**

#### **Step 1: Get Main Categories (Level 1)**
```bash
GET /api/v1/user/organization-categories
```

**Response Schema:**
```typescript
interface OrganizationCategoriesResponse {
  success: boolean;
  message: string;
  data: UserSignupData[]; // Level 1 categories
  statusCode: number;
}
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "title": "who_are_you",
      "parentSlug": ["4"],
      "itemText": "Union (an Organization / Not an individual member)",
      "slug": "union_1",           // ← This slug links to Level 2
      "selectionType": "only",
      "subCategory": "main_category"
    },
    {
      "_id": "507f1f77bcf86cd799439012", 
      "title": "who_are_you",
      "parentSlug": ["4"],
      "itemText": "Affiliate (Organization)",
      "slug": "affiliate_organization_1", // ← This slug links to Level 2
      "selectionType": "only",
      "subCategory": "main_category"
    }
  ]
}
```

#### **Step 2: Get Subcategories (Level 2)**
```bash
GET /api/v1/user/organization-subcategories/{mainCategorySlug}
# Example: GET /api/v1/user/organization-subcategories/union_1
```

**Response Schema:**
```typescript
interface OrganizationSubcategoriesResponse {
  success: boolean;
  message: string;
  data: UserSignupData[]; // Level 2 subcategories
  statusCode: number;
}
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "688718fe6daa29ee37d5bf16",
      "title": "union_subcategory",
      "parentSlug": ["union_1"],    // ← Links back to Level 1
      "itemText": "Screen Actors Guild (SAG)",
      "slug": "sag_union",          // ← This slug links to Level 3
      "selectionType": "multiple",
      "subCategory": "union_type"
    },
    {
      "_id": "6887919a6daa29ee37d5bf38",
      "title": "union_subcategory", 
      "parentSlug": ["union_1"],    // ← Links back to Level 1
      "itemText": "Actors' Guild of Nigeria (AGN)",
      "slug": "agn_union",          // ← This slug links to Level 3
      "selectionType": "multiple",
      "subCategory": "union_type"
    }
  ]
}
```

#### **Step 3: Get Organizations (Level 3)**
```bash
GET /api/v1/user/organizations-by-subcategory/{level2SubcategorySlug}
# Example: GET /api/v1/user/organizations-by-subcategory/agn_union
```

**Response Schema:**
```typescript
interface OrganizationsBySubcategoryResponse {
  success: boolean;
  message: string;
  data: UserSignupData[]; // Level 3 organizations
  statusCode: number;
}
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "507f1f77bcf86cd799439051",
      "title": "agn_organization",
      "parentSlug": ["agn_union"],  // ← Links back to Level 2
      "itemText": "Actors' Guild of Nigeria (AGN) - National",
      "slug": "agn_national",       // ← This is the final organization
      "selectionType": "multiple",
      "subCategory": "agn_organization"
    },
    {
      "_id": "507f1f77bcf86cd799439052",
      "title": "agn_organization",
      "parentSlug": ["agn_union"],  // ← Links back to Level 2
      "itemText": "AGN Lagos Chapter", 
      "slug": "agn_lagos_chapter",  // ← This is the final organization
      "selectionType": "multiple",
      "subCategory": "agn_organization"
    }
  ]
}
```

### **Schema Relationships in Database:**

#### **1. Level 1 → Level 2 Relationship:**
```typescript
// Level 1: Main Category
{
  "slug": "union_1",           // ← Primary key for Level 1
  "itemText": "Union (an Organization / Not an individual member)"
}

// Level 2: Subcategory (references Level 1)
{
  "parentSlug": ["union_1"],   // ← Foreign key to Level 1
  "slug": "agn_union",         // ← Primary key for Level 2
  "itemText": "Actors' Guild of Nigeria (AGN)"
}
```

#### **2. Level 2 → Level 3 Relationship:**
```typescript
// Level 2: Subcategory
{
  "slug": "agn_union",         // ← Primary key for Level 2
  "itemText": "Actors' Guild of Nigeria (AGN)"
}

// Level 3: Organization (references Level 2)
{
  "parentSlug": ["agn_union"], // ← Foreign key to Level 2
  "slug": "agn_national",      // ← Primary key for Level 3
  "itemText": "Actors' Guild of Nigeria (AGN) - National"
}
```

### **Complete Hierarchy Example:**

```
Level 1: union_1 (Union)
    ↓
Level 2: agn_union (Actors' Guild of Nigeria)
    ↓
Level 3: agn_national (AGN National)
Level 3: agn_lagos_chapter (AGN Lagos Chapter)
Level 3: agn_abuja_chapter (AGN Abuja Chapter)
```

### **API Call Chain Example:**

```bash
# 1. Get all main categories
GET /api/v1/user/organization-categories
# Returns: ["union_1", "affiliate_organization_1", ...]

# 2. Get subcategories for "union_1"
GET /api/v1/user/organization-subcategories/union_1  
# Returns: ["sag_union", "agn_union", "dga_union", ...]

# 3. Get organizations for "agn_union"
GET /api/v1/user/organizations-by-subcategory/agn_union
# Returns: ["agn_national", "agn_lagos_chapter", "agn_abuja_chapter"]
```

### **Key Schema Fields:**

| Field | Purpose | Example |
|-------|---------|---------|
| `slug` | Primary identifier | `"agn_union"` |
| `parentSlug` | Foreign key to parent | `["union_1"]` |
| `itemText` | Display name | `"Actors' Guild of Nigeria (AGN)"` |
| `title` | Category type | `"union_subcategory"` |
| `subCategory` | Classification | `"union_type"` |
| `selectionType` | Selection behavior | `"multiple"` |

### **Database Collection Structure:**

All levels are stored in the same `userSignupData` collection with different `title` and `subCategory` values:

```typescript
// Level 1
{ 
  title: "who_are_you", 
  subCategory: "main_category",
  selectionType: "only"
}

// Level 2  
{ 
  title: "union_subcategory", 
  subCategory: "union_type",
  selectionType: "multiple"
}
{ 
  title: "affiliate_org_subcategory", 
  subCategory: "affiliate_org_type",
  selectionType: "multiple"
}

// Level 3
{ 
  title: "agn_organization", 
  subCategory: "agn_organization",
  selectionType: "multiple"
}
{ 
  title: "sag_organization", 
  subCategory: "sag_organization",
  selectionType: "multiple"
}
```

### **Actual API Response Structure:**

Based on the real API responses, here's the actual schema:

```typescript
interface UserSignupData {
  _id: string;
  title: string;
  parentSlug: string[];
  itemText: string;
  slug: string;
  selectionType: string;
  subCategory: string;  // ← This field IS present!
  createdAt?: string;
  updatedAt?: string;
}
```

### **Real Response Examples:**

From your terminal output:

```json
// Level 2 Response (organization-subcategories/union_1)
{
  "data": [
    {
      "_id": "688718fe6daa29ee37d5bf16",
      "title": "union_subcategory",
      "parentSlug": ["union_1"],
      "itemText": "Screen Actors Guild (SAG)",
      "slug": "sag_union",
      "selectionType": "multiple",
      "subCategory": "union_type"  // ← Present!
    },
    {
      "_id": "6887919a6daa29ee37d5bf38", 
      "title": "union_subcategory",
      "parentSlug": ["union_1"],
      "itemText": "Actors' Guild of Nigeria (AGN)",
      "slug": "agn_union",
      "selectionType": "multiple", 
      "subCategory": "union_type"  // ← Present!
    }
  ]
}
```

## Examples

### Complete Workflow Example

1. **Get all organization categories:**
```bash
curl -X GET "https://api.pepli.com/api/v1/user/organization-categories"
```

2. **Get subcategories for Unions:**
```bash
curl -X GET "https://api.pepli.com/api/v1/user/organization-subcategories/union_1"
```

3. **Get organizations in SAG Union with detailed profile data:**
```bash
curl -X GET "https://api.pepli.com/api/v1/user/organizations-by-subcategory/sag_union?page=1&perPage=10&search=Screen"
```

### JavaScript/TypeScript Example

```typescript
// Get organization categories
const getCategories = async () => {
  const response = await fetch('/api/v1/user/organization-categories');
  const data = await response.json();
  return data.data;
};

// Get subcategories for a main category
const getSubcategories = async (mainCategorySlug: string) => {
  const response = await fetch(`/api/v1/user/organization-subcategories/${mainCategorySlug}`);
  const data = await response.json();
  return data.data;
};

// Get organizations by subcategory with detailed profile data
const getOrganizations = async (subcategorySlug: string, options: {
  page?: number;
  perPage?: number;
  search?: string;
}) => {
  const params = new URLSearchParams();
  if (options.page) params.append('page', options.page.toString());
  if (options.perPage) params.append('perPage', options.perPage.toString());
  if (options.search) params.append('search', options.search);
  
  const response = await fetch(`/api/v1/user/organizations-by-subcategory/${subcategorySlug}?${params}`);
  const data = await response.json();
  return data;
};

// Usage example
const main = async () => {
  // Get all categories
  const categories = await getCategories();
  console.log('Categories:', categories);
  
  // Get union subcategories
  const unionSubcategories = await getSubcategories('union_1');
  console.log('Union subcategories:', unionSubcategories);
  
  // Get SAG organizations with detailed profile data
  const sagOrganizations = await getOrganizations('sag_union', {
    page: 1,
    perPage: 5,
    search: 'Screen'
  });
  console.log('SAG organizations with detailed profiles:', sagOrganizations);
  
  // Access detailed profile information
  sagOrganizations.data.organizations.forEach(org => {
    console.log('Organization:', org.businessOrganizationName);
    console.log('Followers:', org.followers);
    console.log('Website:', org.socialMedia.website);
    console.log('Description:', org.userProfileAboutYou.description);
    console.log('Contact Info:', org.contactInfo);
  });
};
```

## Error Handling

### Common Error Responses

#### 404 - Subcategory Not Found
```json
{
  "success": false,
  "message": "Subcategory not found",
  "statusCode": 404
}
```

#### 500 - Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "statusCode": 500
}
```

### Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request |
| 404 | Not Found |
| 500 | Internal Server Error |

### Best Practices

1. **Pagination**: Always use pagination for large datasets to improve performance
2. **Search**: Use the search parameter to filter organizations by name
3. **Authentication**: While not required, authentication provides additional features like connection status
4. **Error Handling**: Always handle potential errors in your client code
5. **Caching**: Consider caching category and subcategory data as they don't change frequently
6. **Profile Data**: The organization endpoints return comprehensive profile data similar to `/api/v1/user/account/:id`, including analytics, social media, contact info, and other profile details

## Rate Limiting

- **Organization Categories**: 100 requests per minute
- **Organization Subcategories**: 100 requests per minute  
- **Organizations by Subcategory**: 50 requests per minute

## Versioning

This API is part of version 1 (`/api/v1/`). Future versions will maintain backward compatibility where possible.

## Support

For technical support or questions about these endpoints, please contact the development team or refer to the internal documentation. 

### **Visual Schema Relationship Diagram:**

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           DATABASE: userSignupData Collection               │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │   LEVEL 1       │    │   LEVEL 2       │    │   LEVEL 3       │         │
│  │ Main Categories │    │ Subcategories   │    │ Organizations   │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│           │                       │                       │                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │ slug: "union_1" │◄───│ parentSlug:     │◄───│ parentSlug:     │         │
│  │ itemText:       │    │ ["union_1"]     │    │ ["agn_union"]   │         │
│  │ "Union"         │    │ slug: "agn_union"│    │ slug: "agn_national"      │
│  │ title:          │    │ itemText:       │    │ itemText:       │         │
│  │ "who_are_you"   │    │ "AGN"           │    │ "AGN National"  │         │
│  └─────────────────┘    │ title:          │    │ title:          │         │
│                         │ "union_subcategory"│  │ "agn_organization"       │
│  ┌─────────────────┐    └─────────────────┘    └─────────────────┘         │
│  │ slug: "affiliate_│    ┌─────────────────┐    ┌─────────────────┐         │
│  │ _organization_1"│◄───│ parentSlug:     │◄───│ parentSlug:     │         │
│  │ itemText:       │    │ ["affiliate_    │    │ ["production_   │         │
│  │ "Affiliate Org" │    │  organization_1"]│    │  company"]      │         │
│  └─────────────────┘    │ slug: "production_│   │ slug: "warner_bros"       │
│                         │  company"        │    │ itemText:       │         │
│                         │ itemText:        │    │ "Warner Bros"   │         │
│                         │ "Production Co"  │    └─────────────────┘         │
│                         └─────────────────┘                                │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                              API ENDPOINTS                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │ 1. GET /api/v1/user/organization-categories                            │ │
│  │    Returns: All Level 1 items (union_1, affiliate_organization_1, ...) │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                                    │                                        │
│                                    ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │ 2. GET /api/v1/user/organization-subcategories/{mainCategorySlug}      │ │
│  │    Example: /organization-subcategories/union_1                        │ │
│  │    Returns: All Level 2 items under union_1 (agn_union, sag_union, ...)│ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│                                    │                                        │
│                                    ▼                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │ 3. GET /api/v1/user/organizations-by-subcategory/{level2SubcategorySlug}│ │
│  │    Example: /organizations-by-subcategory/agn_union                    │ │
│  │    Returns: All Level 3 items under agn_union (agn_national, ...)      │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Complete Data Flow Example:**

```bash
# Step 1: Get all main categories
GET /api/v1/user/organization-categories
Response: ["union_1", "affiliate_organization_1", "affiliate_business_1", "school_training_facility_1"]

# Step 2: Get subcategories for "union_1" 
GET /api/v1/user/organization-subcategories/union_1
Response: ["sag_union", "agn_union", "dga_union", "wga_union", "iatse_union"]

# Step 3: Get organizations for "agn_union"
GET /api/v1/user/organizations-by-subcategory/agn_union  
Response: ["agn_national", "agn_lagos_chapter", "agn_abuja_chapter"]
```

### **Schema Linking Logic:**

1. **Level 1 → Level 2**: `parentSlug` field in Level 2 references `slug` field in Level 1
2. **Level 2 → Level 3**: `parentSlug` field in Level 3 references `slug` field in Level 2
3. **API Chain**: Each API endpoint uses the `slug` from the previous level as a parameter

### **Database Query Relationships:**

```typescript
// Level 1 to Level 2
db.userSignupData.find({
  parentSlug: "union_1",           // References Level 1 slug
  title: "union_subcategory"       // Identifies Level 2 items
})

// Level 2 to Level 3  
db.userSignupData.find({
  parentSlug: "agn_union",         // References Level 2 slug
  title: { $regex: /_organization$|_org$/ } // Identifies Level 3 items
})
``` 

## How Each API Endpoint Works

### **API 1: Get Organization Categories (Level 1)**

**Endpoint:** `GET /api/v1/user/organization-categories`

**How it works:**
```typescript
// Database Query Logic
const data = await this.userSignupDataModel.find({
  title: "who_are_you",           // ← Identifies Level 1 items
  subCategory: "main_category",   // ← Confirms it's a main category
  parentSlug: ["4"]              // ← All Level 1 items have parentSlug ["4"]
}).sort({ _id: 1 });
```

**What it finds:**
- **Level 1 items** with `title: "who_are_you"`
- **Main categories** like Union, Affiliate Organization, etc.
- **Parent items** that have other items under them

**Example Response:**
```json
{
  "data": [
    {
      "title": "who_are_you",
      "slug": "union_1",           // ← This slug is used in API 2
      "itemText": "Union (an Organization / Not an individual member)",
      "subCategory": "main_category"
    }
  ]
}
```

---

### **API 2: Get Organization Subcategories (Level 2)**

**Endpoint:** `GET /api/v1/user/organization-subcategories/{mainCategorySlug}`

**How it works:**
```typescript
// Database Query Logic
const data = await this.userSignupDataModel.find({
  parentSlug: [mainCategorySlug],  // ← Uses slug from Level 1 as parent
  title: { $in: [                  // ← Identifies Level 2 items by title
    "union_subcategory",
    "affiliate_org_subcategory", 
    "affiliate_business_subcategory",
    "school_training_subcategory"
  ]}
}).sort({ _id: 1 });
```

**What it finds:**
- **Level 2 items** that have the specified Level 1 slug as their `parentSlug`
- **Subcategories** under the main category
- **Items with specific titles** that indicate Level 2

**Example Query:**
```bash
GET /api/v1/user/organization-subcategories/union_1
```

**Database Query:**
```typescript
// Finds all items where:
// - parentSlug contains "union_1" (from Level 1)
// - title is one of the Level 2 subcategory types
{
  parentSlug: ["union_1"],
  title: { $in: ["union_subcategory", "affiliate_org_subcategory", ...] }
}
```

**Example Response:**
```json
{
  "data": [
    {
      "title": "union_subcategory",
      "slug": "sag_union",         // ← This slug is used in API 3
      "parentSlug": ["union_1"],   // ← References Level 1
      "itemText": "Screen Actors Guild (SAG)",
      "subCategory": "union_type"
    }
  ]
}
```

---

### **API 3: Get Organizations by Subcategory (Level 3)**

**Endpoint:** `GET /api/v1/user/organizations-by-subcategory/{level2SubcategorySlug}`

**How it works:**
```typescript
// Database Query Logic
const data = await this.userSignupDataModel.find({
  parentSlug: [level2SubcategorySlug],  // ← Uses slug from Level 2 as parent
  title: { $regex: /_organization$|_org$/ }  // ← Identifies Level 3 items
}).sort({ _id: 1 });
```

**What it finds:**
- **Level 3 items** that have the specified Level 2 slug as their `parentSlug`
- **Organizations** under the subcategory
- **Items with titles ending in `_organization` or `_org`**

**Example Query:**
```bash
GET /api/v1/user/organizations-by-subcategory/sag_union
```

**Database Query:**
```typescript
// Finds all items where:
// - parentSlug contains "sag_union" (from Level 2)
// - title matches pattern ending with "_organization" or "_org"
{
  parentSlug: ["sag_union"],
  title: { $regex: /_organization$|_org$/ }
}
```

**Example Response:**
```json
{
  "data": [
    {
      "title": "sag_organization",
      "slug": "sag_aftra_main",
      "parentSlug": ["sag_union"],  // ← References Level 2
      "itemText": "Screen Actors Guild-American Federation of Television and Radio Artists (SAG-AFTRA)",
      "subCategory": "sag_organization"
    }
  ]
}
```

---

### **Complete Flow Example:**

```bash
# Step 1: Get Level 1 categories
GET /api/v1/user/organization-categories
Query: { title: "who_are_you", subCategory: "main_category", parentSlug: ["4"] }
Result: ["union_1", "affiliate_organization_1", ...]

# Step 2: Get Level 2 subcategories for "union_1"
GET /api/v1/user/organization-subcategories/union_1
Query: { parentSlug: ["union_1"], title: { $in: ["union_subcategory", ...] } }
Result: ["sag_union", "agn_union", "dga_union", ...]

# Step 3: Get Level 3 organizations for "sag_union"
GET /api/v1/user/organizations-by-subcategory/sag_union
Query: { parentSlug: ["sag_union"], title: { $regex: /_organization$|_org$/ } }
Result: ["sag_aftra_main", "sag_foundation", "sag_conservatory"]
```

### **Key Identification Patterns:**

| Level | Identified By | Example |
|-------|---------------|---------|
| **Level 1** | `title: "who_are_you"` + `subCategory: "main_category"` | `union_1` |
| **Level 2** | `title: "*_subcategory"` + `parentSlug: [level1_slug]` | `sag_union` |
| **Level 3** | `title: "*_organization"` + `parentSlug: [level2_slug]` | `sag_aftra_main` |

### **Why Your Query Returns Empty:**

When you call `/api/v1/user/organizations-by-subcategory/sag_union`, the API looks for:

```typescript
{
  parentSlug: ["sag_union"],           // ← Items that have sag_union as parent
  title: { $regex: /_organization$|_org$/ }  // ← Titles ending with _organization
}
```

But your data has:
```json
{
  "title": "union_subcategory",        // ← Doesn't end with _organization
  "parentSlug": ["sag_union"],         // ← This part is correct
  "slug": "agn_union"
}
```

**Solution:** You need Level 3 items with titles like `"sag_organization"` under `sag_union`, not Level 2 items with `"union_subcategory"`. 

### **Why Use `subCategory` Instead of `title`?**

You're absolutely right! Using the `subCategory` field would be much better than the `title` field for several reasons:

#### **Problems with Using `title` Field:**

1. **Inconsistent Naming**: Titles like `"union_subcategory"` vs `"sag_organization"` are inconsistent
2. **Hard to Maintain**: Need to remember specific title patterns
3. **Error-Prone**: Easy to make typos in title matching
4. **Not Semantic**: Title is meant for display, not for categorization

#### **Benefits of Using `subCategory` Field:**

1. **Consistent Naming**: All levels use the same pattern
2. **Semantic Meaning**: `subCategory` is specifically designed for categorization
3. **Easier to Maintain**: Clear, predictable values
4. **Less Error-Prone**: Standardized field values

### **Proposed Better Approach Using `subCategory`:**

#### **Current Approach (Using `title`):**
```typescript
// Level 1
{ title: "who_are_you", subCategory: "main_category" }

// Level 2  
{ title: "union_subcategory", subCategory: "union_type" }

// Level 3
{ title: "sag_organization", subCategory: "sag_organization" }
```

#### **Better Approach (Using `subCategory`):**
```typescript
// Level 1
{ title: "who_are_you", subCategory: "main_category" }

// Level 2  
{ title: "who_are_you", subCategory: "union_type" }

// Level 3
{ title: "who_are_you", subCategory: "sag_organization" }
```

### **Modified API Logic Using `subCategory`:**

#### **API 1: Get Organization Categories (Level 1)**
```typescript
// Current Logic
const data = await this.userSignupDataModel.find({
  title: "who_are_you",
  subCategory: "main_category",
  parentSlug: ["4"]
});

// Better Logic (no change needed)
const data = await this.userSignupDataModel.find({
  title: "who_are_you", 
  subCategory: "main_category",
  parentSlug: ["4"]
});
```

#### **API 2: Get Organization Subcategories (Level 2)**
```typescript
// Current Logic
const data = await this.userSignupDataModel.find({
  parentSlug: [mainCategorySlug],
  title: { $in: ["union_subcategory", "affiliate_org_subcategory", ...] }
});

// Better Logic (IMPLEMENTED)
const data = await this.userSignupDataModel.find({
  parentSlug: [mainCategorySlug],
  subCategory: "sub_type"  // All Level 2 items use "sub_type"
});
```

#### **API 3: Get Organizations by Subcategory (Level 3)**
```typescript
// Current Logic
const data = await this.userSignupDataModel.find({
  parentSlug: [level2SubcategorySlug],
  title: { $regex: /_organization$|_org$/ }
});

// Better Logic (IMPLEMENTED)
const data = await this.userSignupDataModel.find({
  parentSlug: [level2SubcategorySlug],
  subCategory: "organization"  // All Level 3 items use "organization"
});
```

### **Improved Data Structure:**

```typescript
// Level 1: Main Categories
{
  title: "who_are_you",
  subCategory: "main_category",
  slug: "union_1",
  parentSlug: ["4"]
}

// Level 2: Subcategories  
{
  title: "who_are_you",
  subCategory: "sub_type",             // ← Standardized naming
  slug: "sag_union",
  parentSlug: ["union_1"]
}

// Level 3: Organizations
{
  title: "who_are_you", 
  subCategory: "organization",         // ← Standardized naming
  slug: "sag_aftra_main",
  parentSlug: ["sag_union"]
}
```

### **Benefits of This Approach:**

1. **Consistent `title`**: All items use `"who_are_you"` as title
2. **Semantic `subCategory`**: Clear categorization by purpose
3. **Predictable Patterns**: 
   - Level 1: `"main_category"`
   - Level 2: `"sub_type"` 
   - Level 3: `"organization"`
4. **Easier Queries**: No complex regex patterns needed
5. **Better Maintainability**: Clear, logical field values

### **Implementation Recommendation:**

You should modify your API endpoints to use `subCategory` instead of `title` for level identification. This would make the code more maintainable and less error-prone. 