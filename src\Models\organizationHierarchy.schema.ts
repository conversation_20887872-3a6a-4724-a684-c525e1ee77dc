import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, ObjectId } from 'mongoose';

export type OrganizationHierarchyDocument = OrganizationHierarchy & Document;

@Schema({ 
  timestamps: true,
  collection: 'organization_hierarchy'
})
export class OrganizationHierarchy {
  @Prop({ type: Object, required: true, ref: 'User' })
  organizationId: ObjectId;

  @Prop({ type: String, required: true, unique: true })
  organizationName: string;

  @Prop({ type: String, required: true })
  mainCategory: string;

  @Prop({ type: String, required: true })
  subcategory: string;

  @Prop({ type: String, required: true })
  mainCategoryName: string;

  @Prop({ type: String, required: true })
  subcategoryName: string;

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({ type: Date, default: Date.now })
  lastUpdated: Date;
}

export const OrganizationHierarchySchema = SchemaFactory.createForClass(OrganizationHierarchy);

// Create indexes for performance
OrganizationHierarchySchema.index({ organizationId: 1 });
OrganizationHierarchySchema.index({ mainCategory: 1 });
OrganizationHierarchySchema.index({ subcategory: 1 });
OrganizationHierarchySchema.index({ organizationName: 1 }, { unique: true });
OrganizationHierarchySchema.index({ mainCategory: 1, subcategory: 1 });
OrganizationHierarchySchema.index({ isActive: 1 });
