import { IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { IsStringValidation } from 'src/Custom/helpers/dto.helper';
import CONSTANT from '../../../common/constant/common.constant';

export class MediaDto {
  @IsString()
  mediaType: string;

  @IsString()
  url: string;

  @IsString()
  thumbUrl: string | null;
}

export class addGroupPostDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;

  @IsOptional()
  @IsStringValidation('caption', 500, false)
  readonly caption: string;

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}

export class repostInGroupDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;

  @IsOptional()
  @IsStringValidation('caption', 500, false)
  readonly repostCaption: string;

  @IsMongoId({ message: CONSTANT.INVALID('postId') })
  @IsStringValidation('postId', 24, false)
  readonly postId: string;
}
