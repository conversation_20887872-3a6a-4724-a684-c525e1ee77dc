import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from './user.schema';

export type PeopleDocument = People & Document;

export enum StatusEnum {
  PENDING = 'pending',
  ACCEPT = 'accept',
  REJECT = 'reject',
}

@Schema({ timestamps: true })
export class People {
  @Prop({ default: null, enum: StatusEnum })
  status: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  parentUserId: User;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  childUserId: User;

  @Prop({ type: String, default: null })
  email: string;

  @Prop({ type: String, default: null })
  idNumber: string;

  @Prop({ type: String, default: null })
  document: string;

  @Prop({ type: Boolean, default: false })
  isAlumni: boolean;

  @Prop({ type: Boolean, default: false })
  isStudent: boolean;
}

export const PeopleSchema = SchemaFactory.createForClass(People);
