// Test file for Project API with new fields: role, category, location
// This demonstrates how to use the updated v1/user/project API

const BASE_URL = 'http://localhost:3000';
const USER_ID = '688706a5304c5a2526f6056c'; // Example user ID
const JWT_TOKEN = 'your_jwt_token_here'; // Replace with actual token

// Example 1: Add a new project with role, category, and location
const addProjectExample = {
  name: "Music Production Studio",
  description: "A professional music production studio for artists and bands",
  role: "Lead Producer", // NEW FIELD
  category: "Music Production", // NEW FIELD
  location: "Los Angeles, CA", // NEW FIELD
  skills: ["Audio Engineering", "Music Production", "Sound Design"],
  currentlyWorking: true,
  startDate: "2024-01-01",
  endDate: "2024-12-31",
  link: "https://example.com/project",
  media: [
    {
      mediaType: "image",
      url: "https://example.com/project-image.jpg",
      thumbUrl: "https://example.com/project-thumb.jpg"
    }
  ],
  collaborators: [], // Array of user IDs
  affiliateOrganizations: [] // Array of organization IDs
};

// Example 2: Update an existing project
const updateProjectExample = {
  _id: "project_id_here", // Existing project ID
  name: "Updated Music Production Studio",
  description: "Updated description",
  role: "Senior Producer", // Updated role
  category: "Professional Audio", // Updated category
  location: "New York, NY", // Updated location
  skills: ["Advanced Audio Engineering", "Music Production", "Sound Design", "Mixing"],
  currentlyWorking: false,
  startDate: "2024-01-01",
  endDate: "2024-06-30",
  link: "https://example.com/updated-project",
  media: [
    {
      mediaType: "image",
      url: "https://example.com/updated-project-image.jpg",
      thumbUrl: "https://example.com/updated-project-thumb.jpg"
    }
  ],
  collaborators: [],
  affiliateOrganizations: []
};

// cURL Commands for testing:

console.log('=== Project API Test Commands ===\n');

// 1. Add new project
console.log('1. Add new project with role, category, location:');
console.log(`curl -X POST "${BASE_URL}/user/project" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(addProjectExample, null, 2)}'`);
console.log('\n');

// 2. Get user's projects (will include new fields)
console.log('2. Get user projects (includes role, category, location):');
console.log(`curl -X GET "${BASE_URL}/user/projects/${USER_ID}" \\
  -H "Authorization: Bearer ${JWT_TOKEN}"`);
console.log('\n');

// 3. Get user profile (projects object will include new fields)
console.log('3. Get user profile (projects object includes new fields):');
console.log(`curl -X GET "${BASE_URL}/user/${USER_ID}" \\
  -H "Authorization: Bearer ${JWT_TOKEN}"`);
console.log('\n');

// 4. Update existing project
console.log('4. Update existing project:');
console.log(`curl -X POST "${BASE_URL}/user/project" \\
  -H "Authorization: Bearer ${JWT_TOKEN}" \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(updateProjectExample, null, 2)}'`);
console.log('\n');

// 5. Delete project
console.log('5. Delete project:');
console.log(`curl -X DELETE "${BASE_URL}/user/project/project_id_here" \\
  -H "Authorization: Bearer ${JWT_TOKEN}"`);
console.log('\n');

// Expected Response Structure:
console.log('=== Expected Response Structure ===\n');

const expectedProjectResponse = {
  status: true,
  message: "Project added successfully",
  data: {
    _id: "project_id_here",
    name: "Music Production Studio",
    description: "A professional music production studio for artists and bands",
    role: "Lead Producer", // NEW FIELD
    category: "Music Production", // NEW FIELD
    location: "Los Angeles, CA", // NEW FIELD
    skills: ["Audio Engineering", "Music Production", "Sound Design"],
    currentlyWorking: true,
    startDate: "2024-01-01T00:00:00.000Z",
    endDate: "2024-12-31T00:00:00.000Z",
    link: "https://example.com/project",
    media: [
      {
        mediaType: "image",
        url: "https://example.com/project-image.jpg",
        thumbUrl: "https://example.com/project-thumb.jpg"
      }
    ],
    collaborators: [],
    affiliateOrganizations: [],
    userId: USER_ID,
    createdAt: "2024-01-15T10:30:00.000Z",
    updatedAt: "2024-01-15T10:30:00.000Z"
  }
};

console.log('Project Response:');
console.log(JSON.stringify(expectedProjectResponse, null, 2));
console.log('\n');

// User Profile Response (projects object):
const expectedUserProfileResponse = {
  status: true,
  message: "User fetched successfully",
  data: {
    _id: USER_ID,
    firstName: "John",
    lastName: "Doe",
    // ... other user fields
    projects: {
      _id: "project_id_here",
      name: "Music Production Studio",
      description: "A professional music production studio for artists and bands",
      role: "Lead Producer", // NEW FIELD - now included in user profile
      category: "Music Production", // NEW FIELD - now included in user profile
      location: "Los Angeles, CA", // NEW FIELD - now included in user profile
      skills: ["Audio Engineering", "Music Production", "Sound Design"],
      currentlyWorking: true,
      startDate: "2024-01-01T00:00:00.000Z",
      endDate: "2024-12-31T00:00:00.000Z",
      link: "https://example.com/project",
      media: [...],
      collaborators: [...],
      affiliateOrganizations: [...],
      userId: USER_ID,
      createdAt: "2024-01-15T10:30:00.000Z",
      updatedAt: "2024-01-15T10:30:00.000Z"
    }
  }
};

console.log('User Profile Response (projects object):');
console.log(JSON.stringify(expectedUserProfileResponse, null, 2));

console.log('\n=== Summary ===');
console.log('✅ Added role, category, and location fields to Project model');
console.log('✅ Updated project DTO with validation for new fields');
console.log('✅ New fields automatically included in all project responses');
console.log('✅ New fields included in user profile projects object');
console.log('✅ Backward compatible - existing projects will have null/undefined for new fields');
