import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Request } from 'express';
import { Model } from 'mongoose';
import { Reflector } from '@nestjs/core';
import { User } from '../../Models/user.schema';
import { IS_PUBLIC_KEY } from '../../common/decorators/auth.decorator';
import CONSTANT from '../../common/constant/common.constant';
import { JwtAuthService } from '../../common/services/jwt.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    @InjectModel(User.name)
    private userModel: Model<User>,
    private jwtIService: JwtAuthService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token)
      throw new UnauthorizedException({ message: CONSTANT.UNAUTHORIZED });

    try {
      const payload = await this.jwtIService.verifyToken(token);
      // console.log('payload ->>', payload);
      const user = await this.userModel
        .findOne({ _id: payload.id }, { password: 0 })
        .lean();

      if (!user)
        throw new UnauthorizedException({
          message: CONSTANT.NOT_FOUND_MESSAGE('User'),
        });

      // 💡 We're assigning the payload to the request object here
      request['user'] = user;
    } catch (error) {
      if (error.message === 'jwt expired') {
        throw new HttpException(
          { message: error.message },
          HttpStatus.UNAUTHORIZED,
        );
      } else {
        throw new HttpException(
          { message: error.message },
          error?.status || 500,
        );
      }
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
