import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Exclude } from 'class-transformer';
import crypto from 'crypto';
import { UserSignupData } from './userSignupData.schema';
import mongoose, { Document, Types } from 'mongoose';
import { StatusEnum } from './connectionInfo.schema';

export type userDocument = User & Document;

export enum iAmMemberEnum {
  UNION_AFFILIATE_MEMBER = 'unionAffiliateMember',
  NONUNION_INDIVIDUAL_HOBBYIST = 'nonUnionIndividualHobbyist',
  AUDIENCE_MEMBER_FAN = 'audienceMemberFan',
  UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY = 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility',
}

export enum collaborationEnum {
  PRIVATE_ONLY = 'privateOnly',
  FRIENDS_IN_MY_NETWORK = 'friendsInMyNetwork',
  ALL_UNION_MEMBERS = 'allUnionMembers',
  ONLY_MEMBERS_IN_MY_UNION = 'onlyMembersInMyUnion',
  ALL_UNION_MEMBERS_AFFILIATES = 'allUnionMembersAffiliates',
  ALL_UNION_MEMBERS_AFFILIATES_TEACHERS_COACHES = 'allUnionMembersAffiliatesTeachersCoaches',
  ALL_UNION_MEMBERS_AFFILIATES_TEACHERS_COACHES_STUDENTS = 'allUnionMembersAffiliatesTeachersCoachesStudents',
  AFFILIATE_MEMBERS_IN_MY_AFFILIATE_ORGANIZATION_ONLY = 'affiliateMembersInMyAffiliateOrganizationsOnly',
}

export enum analyticsView {
  PRIVATE = 'private',
  PUBLIC = 'public',
}

export enum GenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  NON_BINARY = 'nonBinary',
  PREFER_NOT_SAY = 'preferNotSay',
  PREFER_SELF_DESCRIBE = 'preferSelfDescribe',
}

interface ContactInfoData {
  slug: string;
  name: string | null;
  address: string | null;
  phoneNumber: string | null;
  email: string | null;
  isSelected: boolean;
}

@Schema({ _id: false, versionKey: false })
export class Signupdata {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'UserSignupData',
  })
  itemId: UserSignupData;

  @Prop({ type: Boolean, trim: true, required: true })
  isSelected: boolean;
}

@Schema({ _id: false, versionKey: false })
export class SubData {
  @Prop({ type: String })
  itemText: string;

  @Prop({ type: String })
  idNumber: string;

  @Prop({ type: String })
  memberIdCard: string;

  @Prop({ type: String })
  memberDoc: string;

  @Prop({ type: [Signupdata] })
  iAm: Signupdata[];
}

@Schema({ _id: true, versionKey: false })
export class AffiliatePagesType {
  @Prop({ type: String })
  name: string;

  @Prop({ type: Boolean, default: false })
  isVerified: boolean;
}

@Schema({ _id: false, versionKey: false })
export class AboutYouData {
  @Prop({
    type: [SubData],
  })
  union: SubData[];

  @Prop({
    type: [SubData],
  })
  affiliateOrganization: SubData[];

  @Prop({
    type: [SubData],
  })
  affiliateBusiness: SubData[];

  @Prop({
    type: [SubData],
  })
  schoolTrainingFacility: SubData[];

  @Prop({ type: [String] })
  visualArts: string[];

  @Prop({ type: [String] })
  performingArts: string[];

  @Prop({ type: [String] })
  dance: string[];

  @Prop({ type: [String] })
  acting: string[];

  @Prop({ type: [String] })
  music: string[];

  @Prop({ type: [String] })
  filmMedia: string[];

  @Prop({ type: [String] })
  design: string[];

  @Prop({ type: [String] })
  literaryArts: string[];

  @Prop({ type: [String] })
  crafts: string[];

  @Prop({ type: [String] })
  appliedArts: string[];

  @Prop({ type: [String] })
  other: string[];
}

@Schema({ _id: false, versionKey: false })
export class SocialMediaData {
  @Prop({ type: String })
  website: string;

  @Prop({ type: String })
  imdb: string;

  @Prop({ type: String })
  facebook: string;

  @Prop({ type: String })
  instagram: string;

  @Prop({ type: String })
  tiktok: string;

  @Prop({ type: String })
  linkedin: string;

  @Prop({ type: String })
  twitter: string;

  @Prop({ type: String })
  youtube: string;

  @Prop({ type: String })
  patreon: string;
}

@Schema({ _id: false, versionKey: false })
export class UserProfileAboutYou {
  @Prop({ type: String })
  website: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  industry: string;

  @Prop({ type: String })
  founded: string;

  @Prop({ type: String })
  organizationType: string;

  @Prop({ type: String })
  organizationSize: string;

  @Prop({ type: String, default: null })
  phone: string;

  @Prop({ type: String })
  headquarter: string;

  @Prop({ type: [String] })
  skills: string[];
  @Prop({ type: [String] })
  specialization: string[];
  @Prop({ type: String })
  members: string;
}

@Schema({ _id: false, versionKey: false })
export class Publication {
  @Prop({ type: Types.ObjectId, default: () => new Types.ObjectId() })
  _id: Types.ObjectId;

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  website: string;
}

@Schema({ _id: false, versionKey: false })
export class Analytics {
  @Prop({ type: Number, default: 0 })
  viewCount: number;

  @Prop({ type: Number, default: 0 })
  viewByMeCount: number;

  @Prop({ type: String, enum: analyticsView, default: analyticsView.PRIVATE })
  visibility: string;
}

@Schema({ timestamps: true, versionKey: false })
export class User {
  @Prop({ type: String, enum: iAmMemberEnum })
  iAmMember: string;

  @Prop({ type: String })
  firstName: string;

  @Prop({ type: String })
  middleName: string;

  @Prop({ type: String })
  lastName: string;

  @Prop({ type: String })
  additionalName: string;

  @Prop({ type: Boolean, default: false })
  isFakeAccount: boolean;

  @Prop({ type: String })
  businessOrganizationName: string;

  @Prop({ type: String, required: true, unique: true })
  userName: string;

  @Prop({ type: String })
  city: string;

  @Prop({ type: String })
  state: string;

  @Prop({ type: String})
  country: string;

  @Prop({ type: String, required: true, unique: true })
  email: string;

  @Prop({ type: Boolean, default: false })
  isEmailVerified: boolean;

  @Prop({ type: Boolean, default: false })
  isMembershipVerified: boolean;

  @Prop({ enum: StatusEnum, default: null })
  hirerEmployerVerifiedStatus: StatusEnum;

  @Prop({ type: String, required: true })
  countryCode: string;

  @Prop({
    type: String,
    // required: true
    default: null,
  })
  phone: string;

  @Prop({ type: String, required: true })
  @Exclude()
  password: string;

  @Prop({ type: [Signupdata] })
  signUpData: Signupdata[];

  @Prop({
    type: AboutYouData,
    default: { AboutYouData },
  })
  aboutYou: AboutYouData;

  @Prop({ type: String, default: null })
  profileImage: string;

  @Prop({ type: String })
  position: string;

  @Prop({ type: String })
  industry: string;

  @Prop({ type: String })
  pronouns: string;

  @Prop({ type: String })
  headline: string;

  // @Prop({ type: String, default: null })
  // bio: string;

  // @Prop({
  //   default: GenderEnum.PREFER_NOT_SAY,
  //   enum: GenderEnum,
  // })
  // gender: string;

  // @Prop({
  //   default: null,
  // })
  // customGenderValue: string;

  // @Prop({ type: [String], default: null })
  // education: string[];

  // @Prop({
  //   type: [
  //     {
  //       self_identify: String,
  //       addTxt: { type: String, default: null },
  //       _id: false,
  //     },
  //   ],
  //   default: null,
  // })
  // degree: DemographicsData[];

  // @Prop({
  //   type: [
  //     {
  //       self_identify: String,
  //       addTxt: { type: String, default: null },
  //       _id: false,
  //     },
  //   ],
  //   default: null,
  // })
  // profession: DemographicsData[];

  // @Prop({
  //   type: {
  //     availableToWork: { type: Boolean, default: false },
  //     currentlyWorking: { type: String, default: null },
  //     hiring: { type: String, default: null },
  //     investingInProjects: { type: String, default: null },
  //     availabilityDates: { type: String, default: null },
  //     basedIn: { type: String, default: null },
  //     willingToTravelForWorkNationwideOrRelocate: {
  //       type: Boolean,
  //       default: false,
  //     },
  //     willingToTravelForWorkWorldwideOrRelocate: {
  //       type: Boolean,
  //       default: false,
  //     },
  //     willingToWorkFromHome: {
  //       type: Boolean,
  //       default: false,
  //     },
  //     willingToWorkOnSite: {
  //       type: Boolean,
  //       default: false,
  //     },
  //     willingToWorkHybrid: {
  //       type: Boolean,
  //       default: false,
  //     },
  //     validPassport: { type: Boolean, default: false },
  //     validDriverLicense: { type: Boolean, default: false },
  //   },
  //   _id: false,
  // })
  // availabilityForWork: {
  //   availableToWork: boolean;
  //   currentlyWorking: boolean;
  //   hiring: string;
  //   investingInProjects: string;
  //   availabilityDates: string;
  //   basedIn: string;
  //   willingToTravelForWorkNationwideOrRelocate: boolean;
  //   willingToTravelForWorkWorldwideOrRelocate: boolean;
  //   willingToWorkFromHome: string;
  //   willingToWorkOnSite: boolean;
  //   willingToWorkHybrid: boolean;
  //   validPassport: boolean;
  //   validDriverLicense: boolean;
  // };

  // @Prop({
  //   type: [
  //     {
  //       self_identify: String,
  //       addTxt: { type: String, default: null },
  //       _id: false,
  //     },
  //   ],
  //   default: null,
  // })
  // passportCountries: DemographicsData[];

  // @Prop({
  //   type: [{ memberAs: String, isSelected: Boolean, _id: false }],
  //   default: null,
  // })
  // contactYou: AreYouData[];

  // @Prop({
  //   type: [{ memberAs: String, isSelected: Boolean }],
  //   default: null,
  //   _id: false,
  // })
  // collaborationOptions: AreYouData[];

  // @Prop({
  //   type: [{ memberAs: String, isSelected: Boolean }],
  //   default: null,
  //   _id: false,
  // })
  // notify: AreYouData[];

  // @Prop({ default: false })
  // isOnline: boolean;

  // @Prop({
  //   default: collaborationEnum.ONLY_MEMBERS_IN_MY_UNION,
  //   enum: collaborationEnum,
  // })
  // collaboration: string;

  @Prop({
    type: SocialMediaData,
    default: { SocialMediaData },
  })
  socialMedia: SocialMediaData;

  @Prop({
    type: UserProfileAboutYou,
  })
  userProfileAboutYou: UserProfileAboutYou;

  @Prop({ type: Object, default: null })
  businessAddress: object;

  @Prop({
    type: [
      {
        slug: String,
        name: String,
        address: String,
        phoneNumber: String,
        email: String,
        isSelected: { type: Boolean, default: false },
      },
    ],
    // _id: false,
  })
  contactInfo: ContactInfoData[];

  // @Prop({
  //   type: [{ txt: String, memberId: { type: String, default: null } }],
  //   default: null,
  //   _id: false,
  // })
  // union: AboutYouData[];

  // @Prop({
  //   type: [{ txt: String, memberId: { type: String, default: null } }],
  //   default: null,
  //   _id: false,
  // })
  // organization: AboutYouData[];

  // @Prop({
  //   type: [{ txt: String, memberId: { type: String, default: null } }],
  //   default: null,
  //   _id: false,
  // })
  // school: AboutYouData[];

  // @Prop({
  //   type: [{ txt: String, memberId: { type: String, default: null } }],
  //   default: null,
  //   _id: false,
  // })
  // affiliateBusinesses: AboutYouData[];

  // @Prop({ default: false })
  // nonUnion: boolean;

  @Prop({
    type: Number,
    default: 0,
    set: (v: number) => (v < 0 ? 0 : v),
  })
  followers: number;

  @Prop({
    type: Number,
    default: 0,
    set: (v: number) => (v < 0 ? 0 : v),
  })
  following: number;

  @Prop({
    type: Number,
    default: 0,
    set: (v: number) => (v < 0 ? 0 : v),
  })
  connections: number;

  @Prop({ type: Boolean, default: false })
  showClients: boolean;

  @Prop({ type: Boolean, default: false })
  isFunding: boolean;

  @Prop({ default: true })
  isNotificationOn: boolean;

  @Prop({ default: true })
  isEmailOn: boolean;

  @Prop({
    type: Analytics,
  })
  analytics: Analytics;
  // @Prop({ type: Number, default: 0 })
  // friends: number;

  @Prop({ type: Number, default: 0 })
  posts: number;

  @Prop({
    type: [
      {
        _id: Types.ObjectId,
        description: String,
        website: String,
      },
    ],
  })
  publications: Publication[];

  @Prop({ type: [String], default: [] })
  professions: string[];

  @Prop({ type: [AffiliatePagesType], default: [] })
  affiliatePages: AffiliatePagesType[];

  @Prop({ type: [String], default: [] })
  fieldOfStudy: string[];

  @Prop({ type: [String], default: [] })
  socialActivities: string[];

  @Prop({ type: [String], default: [] })
  offeredServices: string[];

  @Prop({ type: [String], default: [] })
  causes: string[];

  @Prop({ type: Boolean, default: false })
  accountVerified: boolean;
}

export const UserSchema = SchemaFactory.createForClass(User);

//generating password reset token
UserSchema.methods.generateResetPasswordToken = function () {
  //generate token
  const resetToken = crypto.randomBytes(20).toString('hex');
  //hashing and adding resetPasswordToken to UserSchema
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');
  this.resetPasswordExpire = Date.now() + 3600000; // expires in an hour
  return resetToken;
};
