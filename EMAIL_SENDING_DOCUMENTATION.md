# Email Sending Functionality Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Setup & Configuration](#setup--configuration)
4. [Email Service Implementation](#email-service-implementation)
5. [Email Templates](#email-templates)
6. [API Endpoints Using Email](#api-endpoints-using-email)
7. [Usage Examples](#usage-examples)
8. [Error Handling](#error-handling)
9. [Troubleshooting](#troubleshooting)
10. [Security Considerations](#security-considerations)

## Overview

The Pepli Node application uses Microsoft 365 OAuth 2.0 authentication with Microsoft Graph API for sending emails. This implementation provides:

- **Secure OAuth 2.0 Authentication**: No password-based authentication
- **Template-Based Emails**: HTML templates with Handlebars templating engine
- **File Attachment Support**: Ability to attach files to emails
- **Automatic Token Refresh**: Handles token expiration automatically
- **Multiple Fallback Strategies**: Tries different approaches for sending emails
- **Comprehensive Error Handling**: Detailed error messages and retry logic

## Architecture

### Core Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Email Service │    │  Microsoft Graph │    │  Handlebars     │
│   (MailService) │◄──►│      API         │    │   Templates     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   OAuth 2.0     │    │   Token Manager  │    │   Template      │
│   Authentication │    │   (Auto Refresh) │    │   Compilation   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Service Flow

1. **Initialization**: Service acquires OAuth 2.0 access token
2. **Template Compilation**: Handlebars templates are compiled with data
3. **Email Sending**: Uses Microsoft Graph API to send emails
4. **Token Management**: Automatically refreshes expired tokens
5. **Error Handling**: Implements retry logic and fallback strategies

## Setup & Configuration

### 1. Environment Variables

Add these variables to your `.env` file:

```env
# Microsoft 365 OAuth 2.0 Configuration
MICROSOFT_CLIENT_ID=your_client_id_here
MICROSOFT_CLIENT_SECRET=your_client_secret_here
MICROSOFT_TENANT_ID=your_tenant_id_here

# Email Configuration
SMTP_MAIL=<EMAIL>
CONTACT_US_EMAIL=<EMAIL>

# Optional: Contact email for system notifications
CONTACT_US_EMAIL=<EMAIL>
```

### 2. Azure AD Application Setup

Follow the detailed setup guide in [MICROSOFT_365_OAUTH_SETUP.md](./MICROSOFT_365_OAUTH_SETUP.md) to:

1. Register your application in Azure AD
2. Configure API permissions (`Mail.Send`, `Mail.ReadWrite`)
3. Create client secrets
4. Grant admin consent

### 3. Module Registration

The `MailService` is automatically registered in the `CommonModule`:

```typescript
// src/common/common.module.ts
@Module({
  providers: [
    MailService,
    // ... other services
  ],
  exports: [
    MailService,
    // ... other services
  ],
})
export class CommonModule {}
```

## Email Service Implementation

### Core Service: `MailService`

**Location**: `src/common/services/mail.service.ts`

#### Key Features

1. **OAuth 2.0 Token Management**
   - Automatic token acquisition and refresh
   - Token expiration handling
   - Secure token storage in memory

2. **Multiple Sending Strategies**
   - User context (delegated permissions)
   - Application context (application permissions)
   - Automatic fallback between strategies

3. **File Attachment Support**
   - Base64 encoding for attachments
   - Multiple file types supported
   - Proper MIME type handling

#### Main Methods

```typescript
@Injectable()
export class MailService {
  // Send email with template and optional attachments
  public async SendMail(
    to: string,
    subject: string,
    templateHtml: string,
    attachments?: Express.Multer.File[]
  ): Promise<{ messageId: string; response: any }>

  // Get OAuth 2.0 access token
  private async getAccessToken(): Promise<any>

  // Refresh expired access token
  private async refreshAccessToken(): Promise<void>
}
```

### Template System

**Location**: `src/common/services/mail.service.ts` (templates object)

```typescript
export const templates = {
  feedbackTemplate: template('src/Templates/feedback.html'),
  sendOtp: template('src/Templates/sendOtp.html'),
  forgotPassword: template('src/Templates/forgotPassword.html'),
  joinGroup: template('src/Templates/joinGroup.html'),
  betaProgramTemplate: template('src/Templates/betaProgram.html'),
  hirerVerificationTemplate: template('src/Templates/hirerVerification.html'),
  contactUsTemplate: template('src/Templates/contact.html'),
};
```

## Email Templates

### Available Templates

| Template | File | Purpose | Variables |
|----------|------|---------|-----------|
| **OTP Verification** | `sendOtp.html` | Email verification with OTP | `otp` |
| **Forgot Password** | `forgotPassword.html` | Password reset emails | `resetLink`, `userName` |
| **Join Group** | `joinGroup.html` | Group invitation emails | `groupName`, `userName`, `groupLink`, `imageUrl`, `description`, `logoUrl` |
| **Beta Program** | `betaProgram.html` | Beta program welcome emails | `user_name`, `surveyLink` |
| **Hirer Verification** | `hirerVerification.html` | Employer verification emails | `message`, `email`, `position`, `profession`, `fullName`, `website`, `actionUrl`, `verificationId` |
| **Contact Us** | `contact.html` | Contact form submissions | Form data fields |
| **Feedback** | `feedback.html` | User feedback notifications | `user_name`, `category`, `subject`, `description` |

### Template Structure

All templates follow a consistent structure:

```html
<!DOCTYPE html>
<html lang="en" class="miro">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width" />
  <title>Email Title</title>
</head>
<body style="background-color: #f3f4f8;">
  <table class="miro__container" align="center" width="600">
    <!-- Email content with responsive design -->
  </table>
</body>
</html>
```

### Handlebars Integration

Templates use Handlebars for dynamic content:

```typescript
// Template compilation
const template = templates.sendOtp({ otp: '123456' });

// Template with multiple variables
const template = templates.joinGroup({
  groupName: 'Pepli Developers',
  userName: 'John Doe',
  groupLink: 'https://pepli.com/group/123',
  imageUrl: 'https://example.com/avatar.jpg',
  description: 'Join our developer community',
  logoUrl: 'https://pepli.com/logo.png'
});
```

## API Endpoints Using Email

### 1. User Authentication & Verification

#### Send OTP via Email
```typescript
// Endpoint: POST /api/v1/user/send-otp
// Service: UserService.sendOtp()
// Template: sendOtp.html

public async sendOtp(sendOtpData) {
  if (sendOtpData.type === 'email') {
    const template = templates.sendOtp({ otp });
    await this.mailService.SendMail(
      sendOtpData.email,
      'Verification mail',
      template
    );
  }
}
```

### 2. Group Management

#### Invite User via Email
```typescript
// Endpoint: POST /api/v1/group/invite-user-email
// Service: GroupService.inviteUserViaEmail()
// Template: joinGroup.html

async inviteUserViaEmail(req: any, body: InviteUserViaEmialDto) {
  const template = templates.joinGroup({
    groupName: group.name,
    userName: loggedInUser.userName,
    groupLink: group.shareableLink,
    imageUrl: loggedInUser.profileImage,
    description,
    logoUrl: 'https://pepli-beta.s3.us-east-1.amazonaws.com/Pepli/logo_tm.png'
  });
  
  await this.mailService.SendMail(email, 'Join Group', template);
}
```

### 3. Web Services

#### Beta Program Registration
```typescript
// Endpoint: POST /api/v1/web/join-beta-program
// Service: WebService.joinBetaProgram()
// Template: betaProgram.html

await this.mailService.SendMail(
  data.email,
  'Beta Test Waitlist',
  templates.betaProgramTemplate({
    user_name: data.firstName?.trim(),
    surveyLink: surveyLink.link,
  })
);
```

#### Contact Us Form
```typescript
// Endpoint: POST /api/v1/web/contact-us
// Service: WebService.contactUs()
// Template: contact.html

await this.mailService.SendMail(
  email,
  'Verification mail',
  templates.contactUsTemplate(data)
);
```

### 4. User Feedback

#### Submit Feedback
```typescript
// Endpoint: POST /api/v1/user/feedback
// Service: UserService.feedback()
// Template: feedback.html

await this.mailService.SendMail(
  process.env.CONTACT_US_EMAIL,
  feedbackData.title,
  templates.feedbackTemplate({
    user_name: loggedInUser.firstName + ' ' + loggedInUser.lastName,
    category: feedbackData.category,
    subject: feedbackData.title,
    description: feedbackData.description,
  })
);
```

### 5. Hirer Verification

#### Send Verification Email
```typescript
// Endpoint: POST /api/v1/user/sent-hirer-verification-email
// Service: UserService.sentHirerVerificationEmail()
// Template: hirerVerification.html

await this.mailService.SendMail(
  process.env.CONTACT_US_EMAIL || '<EMAIL>',
  'New Hirer Verification Request - Pepli',
  templates.hirerVerificationTemplate({
    message: body.message,
    email: body.email,
    position: body.position,
    profession: body.profession,
    fullName: body.fullName,
    website: body.website,
    actionUrl,
    verificationId,
  }),
  attachments
);
```

## Usage Examples

### 1. Basic Email Sending

```typescript
import { MailService, templates } from 'src/common/services/mail.service';

@Injectable()
export class MyService {
  constructor(private readonly mailService: MailService) {}

  async sendWelcomeEmail(userEmail: string, userName: string) {
    const template = templates.sendOtp({ 
      otp: 'Welcome to Pepli!' 
    });
    
    await this.mailService.SendMail(
      userEmail,
      'Welcome to Pepli',
      template
    );
  }
}
```

### 2. Email with Attachments

```typescript
async sendEmailWithAttachment(
  userEmail: string, 
  file: Express.Multer.File
) {
  const template = templates.contactUsTemplate({
    message: 'Please find the attached document'
  });
  
  await this.mailService.SendMail(
    userEmail,
    'Document Attached',
    template,
    [file] // Array of files
  );
}
```

### 3. Custom Template Usage

```typescript
// Using Handlebars directly
import Handlebars from 'handlebars';

const customTemplate = Handlebars.compile(`
  <h1>Hello {{name}}!</h1>
  <p>Your verification code is: <strong>{{code}}</strong></p>
`);

const html = customTemplate({ name: 'John', code: '123456' });

await this.mailService.SendMail(
  '<EMAIL>',
  'Custom Email',
  html
);
```

## Error Handling

### 1. Authentication Errors (401)

```typescript
// Automatic token refresh and retry
if (error.response?.status === 401 || error.message.includes('access token')) {
  console.log('Authentication error detected, refreshing token and retrying...');
  await this.refreshAccessToken();
  return await this.SendMail(to, subject, templateHtml, attachments);
}
```

### 2. Permission Errors (403)

```typescript
// Detailed permission error message
if (error.response?.status === 403) {
  throw new HttpException({ 
    message: 'Permission denied. Please check your Azure AD application permissions and ensure Mail.Send permission is granted with admin consent.',
    details: error.response?.data 
  }, 403);
}
```

### 3. General Error Handling

```typescript
try {
  await this.mailService.SendMail(to, subject, template, attachments);
} catch (error) {
  console.error('Email sending failed:', error);
  
  // Handle specific error types
  if (error.response?.status === 403) {
    // Permission denied
  } else if (error.response?.status === 401) {
    // Authentication failed
  } else {
    // General error
  }
}
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "Missing required OAuth 2.0 configuration"
**Cause**: Environment variables not set
**Solution**: Verify all required environment variables are set:
```env
MICROSOFT_CLIENT_ID=your_client_id
MICROSOFT_CLIENT_SECRET=your_client_secret
MICROSOFT_TENANT_ID=your_tenant_id
SMTP_MAIL=<EMAIL>
```

#### 2. "403 Forbidden - Permission denied"
**Cause**: Missing API permissions or admin consent
**Solution**: 
1. Go to Azure Portal > App registrations > Your app > API permissions
2. Add `Mail.Send` permission under Microsoft Graph
3. Click "Grant admin consent" (requires admin privileges)

#### 3. "Invalid client secret"
**Cause**: Client secret expired or incorrect
**Solution**: Create a new client secret in Azure AD

#### 4. "User account from a foreign tenant"
**Cause**: Tenant ID mismatch
**Solution**: Verify the tenant ID matches your organization

#### 5. Template Compilation Errors
**Cause**: Invalid Handlebars syntax or missing variables
**Solution**: Check template syntax and ensure all required variables are provided

### Testing Email Functionality

#### 1. Test OAuth 2.0 Configuration
```bash
node test-oauth.js
```

#### 2. Test Email Sending via API
```bash
# Test OTP sending
curl -X POST http://localhost:3000/api/v1/user/send-otp \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "type": "email"
  }'

# Test contact form
curl -X POST http://localhost:3000/api/v1/web/contact-us \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "message": "Test message"
  }'
```

#### 3. Programmatic Testing
```typescript
// Test token acquisition
const tokenTest = await this.mailService.testOAuth2Token();

// Test email sending
const emailTest = await this.mailService.SendMail(
  '<EMAIL>',
  'Test Email',
  '<h1>Test</h1>'
);
```

## Security Considerations

### 1. OAuth 2.0 Security
- **Client Secret Protection**: Never commit secrets to version control
- **Token Storage**: Tokens stored in memory, not persistent storage
- **Scope Limitation**: Only request minimum required permissions
- **Token Refresh**: Automatic refresh prevents token exposure

### 2. Email Security
- **Input Validation**: Validate email addresses and content
- **Rate Limiting**: Implement rate limiting for email endpoints
- **Attachment Scanning**: Scan attachments for malware
- **Content Filtering**: Filter potentially harmful content

### 3. Environment Security
- **Environment Variables**: Use secure environment variable management
- **Production Secrets**: Use Azure Key Vault or similar for production
- **Access Control**: Limit access to email service configuration

### 4. Monitoring and Logging
- **Email Logging**: Log email sending attempts and failures
- **Authentication Monitoring**: Monitor OAuth 2.0 authentication attempts
- **Error Tracking**: Track and alert on email sending failures
- **Usage Analytics**: Monitor email usage patterns

## Best Practices

### 1. Email Template Design
- Use responsive design for mobile compatibility
- Include plain text alternatives
- Optimize for email client compatibility
- Use consistent branding and styling

### 2. Error Handling
- Implement comprehensive error handling
- Provide meaningful error messages
- Log errors for debugging
- Implement retry logic for transient failures

### 3. Performance Optimization
- Compile templates once at startup
- Use connection pooling for API calls
- Implement caching for frequently used data
- Monitor and optimize email sending performance

### 4. Testing
- Test email templates across different email clients
- Verify OAuth 2.0 configuration in different environments
- Test error scenarios and edge cases
- Implement automated email testing

## API Reference

### MailService Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `SendMail` | `to`, `subject`, `templateHtml`, `attachments?` | `Promise<{messageId, response}>` | Send email with template |
| `getAccessToken` | None | `Promise<any>` | Get OAuth 2.0 access token |
| `refreshAccessToken` | None | `Promise<void>` | Refresh expired access token |

### Template Variables

| Template | Variables | Type | Description |
|----------|-----------|------|-------------|
| `sendOtp` | `otp` | string | OTP code |
| `joinGroup` | `groupName`, `userName`, `groupLink`, `imageUrl`, `description`, `logoUrl` | string | Group invitation data |
| `betaProgram` | `user_name`, `surveyLink` | string | Beta program data |
| `hirerVerification` | `message`, `email`, `position`, `profession`, `fullName`, `website`, `actionUrl`, `verificationId` | string | Verification data |
| `feedback` | `user_name`, `category`, `subject`, `description` | string | Feedback data |
| `contact` | Form fields | object | Contact form data |

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `MICROSOFT_CLIENT_ID` | Yes | Azure AD application client ID |
| `MICROSOFT_CLIENT_SECRET` | Yes | Azure AD application client secret |
| `MICROSOFT_TENANT_ID` | Yes | Azure AD tenant ID |
| `SMTP_MAIL` | Yes | Sender email address |
| `CONTACT_US_EMAIL` | No | Contact email for system notifications |

## Conclusion

The email sending functionality in Pepli Node provides a robust, secure, and scalable solution for sending emails using Microsoft 365 OAuth 2.0 authentication. The implementation includes comprehensive error handling, template management, and security considerations to ensure reliable email delivery across all application features.

For additional setup instructions, refer to [MICROSOFT_365_OAUTH_SETUP.md](./MICROSOFT_365_OAUTH_SETUP.md) and [403_ERROR_TROUBLESHOOTING.md](./403_ERROR_TROUBLESHOOTING.md).