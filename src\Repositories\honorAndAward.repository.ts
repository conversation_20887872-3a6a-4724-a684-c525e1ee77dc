import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { HonorAndAwardDocument } from 'src/Models/honorsAndAwards.schema';

@Injectable()
export class HonorAndAwardRepository extends AbstractRepository<HonorAndAwardDocument> {
  constructor(
    @InjectModel('HonorAndAward')
    honorAndAwardModel: Model<HonorAndAwardDocument>,
  ) {
    super(honorAndAwardModel);
  }

  async findHonorsAndAWards(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    return await this.model
      .find(filter)
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();
  }
}
