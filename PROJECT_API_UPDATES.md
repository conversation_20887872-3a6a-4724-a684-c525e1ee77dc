# Project API Updates - Role, Category, and Location Fields

## Overview

The Project API has been enhanced to include three new fields: `role`, `category`, and `location`. These fields provide additional context and categorization for projects, making them more informative and searchable.

## New Fields Added

### 1. Role
- **Type**: String
- **Description**: The role/position the user had in the project
- **Examples**: "Lead Producer", "Senior Developer", "Project Manager", "Creative Director"

### 2. Category
- **Type**: String
- **Description**: The category or type of project
- **Examples**: "Music Production", "Web Development", "Film Production", "Art Installation"

### 3. Location
- **Type**: String
- **Description**: The location where the project was completed
- **Examples**: "Los Angeles, CA", "New York, NY", "Remote", "London, UK"

## API Endpoints

### 1. Add/Update Project
```
POST /user/project
```

**Request Body:**
```json
{
  "name": "Music Production Studio",
  "description": "A professional music production studio for artists and bands",
  "role": "Lead Producer",
  "category": "Music Production",
  "location": "Los Angeles, CA",
  "skills": ["Audio Engineering", "Music Production", "Sound Design"],
  "currentlyWorking": true,
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "link": "https://example.com/project",
  "media": [
    {
      "mediaType": "image",
      "url": "https://example.com/project-image.jpg",
      "thumbUrl": "https://example.com/project-thumb.jpg"
    }
  ],
  "collaborators": [],
  "affiliateOrganizations": []
}
```

**Response:**
```json
{
  "status": true,
  "message": "Project added successfully",
  "data": {
    "_id": "project_id_here",
    "name": "Music Production Studio",
    "description": "A professional music production studio for artists and bands",
    "role": "Lead Producer",
    "category": "Music Production",
    "location": "Los Angeles, CA",
    "skills": ["Audio Engineering", "Music Production", "Sound Design"],
    "currentlyWorking": true,
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T00:00:00.000Z",
    "link": "https://example.com/project",
    "media": [...],
    "collaborators": [],
    "affiliateOrganizations": [],
    "userId": "user_id_here",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 2. Get User Projects
```
GET /user/projects/{userId}
```

**Response:**
```json
{
  "status": true,
  "message": "Projects fetched successfully",
  "data": [
    {
      "_id": "project_id_here",
      "name": "Music Production Studio",
      "description": "A professional music production studio for artists and bands",
      "role": "Lead Producer",
      "category": "Music Production",
      "location": "Los Angeles, CA",
      "skills": ["Audio Engineering", "Music Production", "Sound Design"],
      "currentlyWorking": true,
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T00:00:00.000Z",
      "link": "https://example.com/project",
      "media": [...],
      "collaborators": [...],
      "affiliateOrganizations": [...],
      "userId": "user_id_here",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "pagination": {
    "totalResults": 1,
    "currentResults": 1,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

### 3. Get User Profile (includes projects)
```
GET /user/{userId}
```

**Response:**
```json
{
  "status": true,
  "message": "User fetched successfully",
  "data": {
    "_id": "user_id_here",
    "firstName": "John",
    "lastName": "Doe",
    "userName": "johndoe",
    // ... other user fields
    "projects": {
      "_id": "project_id_here",
      "name": "Music Production Studio",
      "description": "A professional music production studio for artists and bands",
      "role": "Lead Producer",
      "category": "Music Production",
      "location": "Los Angeles, CA",
      "skills": ["Audio Engineering", "Music Production", "Sound Design"],
      "currentlyWorking": true,
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T00:00:00.000Z",
      "link": "https://example.com/project",
      "media": [...],
      "collaborators": [...],
      "affiliateOrganizations": [...],
      "userId": "user_id_here",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  }
}
```

## Validation Rules

### Role Field
- **Required**: No (optional)
- **Type**: String
- **Validation**: Must be a valid string if provided

### Category Field
- **Required**: No (optional)
- **Type**: String
- **Validation**: Must be a valid string if provided

### Location Field
- **Required**: No (optional)
- **Type**: String
- **Validation**: Must be a valid string if provided

## Backward Compatibility

- **Existing Projects**: Projects created before this update will have `null` or `undefined` values for the new fields
- **API Compatibility**: All existing API calls will continue to work without modification
- **Database**: No migration required - new fields are optional

## Example Usage Scenarios

### 1. Music Industry Project
```json
{
  "name": "Album Production",
  "description": "Produced a full-length album for indie artist",
  "role": "Lead Producer",
  "category": "Music Production",
  "location": "Nashville, TN",
  "skills": ["Music Production", "Audio Engineering", "Mixing"]
}
```

### 2. Technology Project
```json
{
  "name": "E-commerce Platform",
  "description": "Built a full-stack e-commerce platform",
  "role": "Senior Full-Stack Developer",
  "category": "Web Development",
  "location": "Remote",
  "skills": ["React", "Node.js", "MongoDB", "AWS"]
}
```

### 3. Creative Project
```json
{
  "name": "Art Installation",
  "description": "Interactive art installation for museum",
  "role": "Creative Director",
  "category": "Art Installation",
  "location": "New York, NY",
  "skills": ["Conceptual Art", "Interactive Design", "Project Management"]
}
```

## Frontend Integration

### React Example
```javascript
const addProject = async (projectData) => {
  try {
    const response = await fetch('/user/project', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: projectData.name,
        description: projectData.description,
        role: projectData.role, // NEW FIELD
        category: projectData.category, // NEW FIELD
        location: projectData.location, // NEW FIELD
        skills: projectData.skills,
        currentlyWorking: projectData.currentlyWorking,
        startDate: projectData.startDate,
        endDate: projectData.endDate,
        link: projectData.link,
        media: projectData.media,
        collaborators: projectData.collaborators,
        affiliateOrganizations: projectData.affiliateOrganizations
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error adding project:', error);
    throw error;
  }
};
```

### Form Fields Example
```jsx
const ProjectForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    role: '', // NEW FIELD
    category: '', // NEW FIELD
    location: '', // NEW FIELD
    skills: [],
    currentlyWorking: false,
    startDate: '',
    endDate: '',
    link: '',
    media: [],
    collaborators: [],
    affiliateOrganizations: []
  });

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Project Name"
        value={formData.name}
        onChange={(e) => setFormData({...formData, name: e.target.value})}
      />
      
      <input
        type="text"
        placeholder="Your Role (e.g., Lead Producer)"
        value={formData.role}
        onChange={(e) => setFormData({...formData, role: e.target.value})}
      />
      
      <input
        type="text"
        placeholder="Category (e.g., Music Production)"
        value={formData.category}
        onChange={(e) => setFormData({...formData, category: e.target.value})}
      />
      
      <input
        type="text"
        placeholder="Location (e.g., Los Angeles, CA)"
        value={formData.location}
        onChange={(e) => setFormData({...formData, location: e.target.value})}
      />
      
      {/* Other form fields */}
    </form>
  );
};
```

## Benefits

1. **Enhanced Project Information**: More detailed project descriptions with role, category, and location
2. **Better Searchability**: Projects can be filtered and searched by role, category, and location
3. **Professional Context**: Clear indication of the user's role and project type
4. **Geographic Information**: Location data for networking and opportunities
5. **Improved User Experience**: More comprehensive project profiles

## Migration Notes

- No database migration required
- Existing projects will have `null` values for new fields
- Frontend can gradually adopt new fields
- API remains backward compatible
