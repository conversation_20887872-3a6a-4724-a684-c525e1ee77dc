import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { DeviceType } from 'src/common/constant/enum';

export type BetaLeadDocument = BetaLead & Document;

@Schema({ timestamps: true })
export class BetaLead {
  @Prop({ type: String })
  firstName: string;

  @Prop({ type: String })
  lastName: string;

  @Prop({ type: String })
  email: string;

  @Prop({ type: String })
  phone: string;

  @Prop({ type: String })
  profession: string;

  @Prop({ type: String })
  industry: string;

  @Prop({ type: String })
  organizationName: string;

  @Prop({ type: String })
  location: string;

  @Prop({ type: Object })
  type: object;

  @Prop({ type: Boolean })
  isMessagingEnable: boolean;

  @Prop({ enum: DeviceType, default: null })
  deviceType: string;
}

export const BetaLeadSchema = SchemaFactory.createForClass(BetaLead);
