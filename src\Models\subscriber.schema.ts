import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';

export type SubscriberDocument = Subscriber & Document;

export enum StatusEnum {
  PENDING = 'pending',
  ACCEPT = 'accept',
  REJECT = 'reject',
}

@Schema({ timestamps: true })
export class Subscriber {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  userId: User;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  subscriberId: User;
}

export const SubscriberSchema = SchemaFactory.createForClass(Subscriber);
