import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Transform } from 'class-transformer';
import mongoose, { Document } from 'mongoose';
import { WhoCanFindEventEnum } from 'src/common/constant/enum';

export type EventDocument = Event & Document;

@Schema({ timestamps: false, versionKey: false })
export class Demographic {
  @Prop({ type: String })
  title: string;

  @Prop({ type: String })
  description: string;
}

@Schema({ timestamps: false, versionKey: false })
export class PaymentMethod {
  @Prop({ type: String })
  type: string;

  @Prop({ type: String })
  info: string;
}

@Schema({ timestamps: false, versionKey: false })
export class PersonDetail {
  @Prop({ type: String })
  firstName: string;

  @Prop({ type: String })
  middleName: string;

  @Prop({ type: String })
  lastName: string;

  @Prop({ type: String })
  organizationName: string;

  @Prop({ type: String })
  website: string;

  @Prop({ type: String })
  jobTitle: string;

  @Prop({ type: String })
  country: string;

  @Prop({ type: String })
  postalCode: string;

  @Prop({ type: Boolean })
  isHidden: boolean;
}

@Schema({ timestamps: true, versionKey: false })
export class Event {
  @Prop({ type: String })
  title: string;

  @Prop({ type: String })
  organizationName: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: [Demographic] })
  demographics: Demographic[];

  @Prop({ type: Boolean })
  isAccomodationForDisability: boolean;

  @Prop({ type: Date })
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @Prop({ type: Date })
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @Prop({ type: Date })
  @Transform(({ value }) => new Date(value))
  schedule: Date;

  @Prop({ type: Boolean })
  isRecurring: boolean;

  @Prop({ type: String })
  recurringType: string;

  @Prop({ type: String })
  reminder: string;

  @Prop({ type: Boolean })
  isInPerson: boolean;

  @Prop({ type: Boolean })
  isVertualOrRemote: boolean;

  @Prop({ type: String })
  location: string;

  @Prop({ type: String })
  meetingLink: string;

  @Prop({ type: Boolean })
  isThereFee: boolean;

  @Prop({ type: Number })
  feeAmount: number;

  @Prop({ type: String })
  currency: string;

  @Prop({ type: [PaymentMethod] })
  paymentMethods: PaymentMethod[];

  @Prop({ type: String })
  bioOfOrganizer: string;

  @Prop({ type: Date })
  expirationDate: Date;

  @Prop({ type: PersonDetail })
  organizer: PersonDetail;

  @Prop({ enum: WhoCanFindEventEnum, type: [String] })
  whoCanFind: string[];

  @Prop({ type: String })
  poster: string;

  @Prop({ type: String })
  sharableLink: string;

  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: 'User' })
  collaborators: mongoose.Types.ObjectId[];

  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: 'User' })
  participants: mongoose.Types.ObjectId[];
}

export const EventSchema = SchemaFactory.createForClass(Event);
