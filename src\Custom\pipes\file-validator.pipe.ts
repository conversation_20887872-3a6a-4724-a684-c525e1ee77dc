import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  HttpStatus,
  HttpException,
} from '@nestjs/common';

@Injectable()
export class FileSizeValidationPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    // "value" is an object containing the file's attributes and metadata
    const oneKb = 50 * 1024 * 1024; // 50 mb
    const file = Array.isArray(value) ? value : [value];
    if (file[0]?.size > oneKb) {
      throw new HttpException(
        { message: 'File size is too large. Maximum allowed size is 50 MB.' },
        HttpStatus.BAD_REQUEST,
      );
    }
    return value;
  }
}
