import { Is<PERSON>mail, IsMongoId, <PERSON><PERSON><PERSON>Empty, IsString } from 'class-validator';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';
import CONSTANT from '../../../common/constant/common.constant';

export class InviteUserViaEmialDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;

  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  description: string;
}

export class JoinGroupDTO {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;
}
