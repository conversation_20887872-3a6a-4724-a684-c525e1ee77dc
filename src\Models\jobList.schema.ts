import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';

export type jobListDocument = JobList & Document;

interface WhoCanSeeData {
  memberAs: string;
  isSelected: boolean;
}

export enum IsJobForEnum {
  WORK = 'work',
  PLAY_WITH_PEERS = 'playWithPeers',
  WORKSHOPS_CLASSES_PROFESSIONAL_DEVELOPMENT = 'workshopsClassesProfessionalDevelopment',
  INDUSTRY_EVENT = 'industryEvent',
  GENERAL_PUBLIC_EVENT = 'generalPublicEvent',
}

export enum JobTypeEnum {
  CONTRACT = 'contract',
  FULLTIME = 'fullTime',
  PARTTIME = 'partTime',
  INTERNSHIP = 'internship',
  TEMPORARY = 'temporary',
  VOLUNTEER = 'volunteer',
  OTHER = 'other',
}

export enum RateTypeEnum {
  FLATE_RATE = 'flateRate',
  HOURLY = 'hourly',
}

export enum RecurringEnum {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  SEMI_ANNUALLY = 'semiAnnually',
  YEARLY = 'yearly',
}

@Schema({ timestamps: true })
export class JobList {
  @Prop({
    enum: IsJobForEnum,
  })
  isJobFor: string;

  @Prop({
    type: {
      isCastingCallAudition: { type: Boolean, default: null },
      roleType: { type: String, default: null },
      breakDownOfRoleOrPosition: { type: String, default: null },
      lookingForProfessions: { type: String, default: null },
      demographics: { type: String, default: null },
      projectType: { type: String, default: null },
      genre: { type: String, default: null },
      synopsisOfProjectOrDescriptionOfJobOpportunity: {
        type: String,
        default: null,
      },
      companyOrNetwork: { type: String, default: null },
      typeOfOrganizationYouAreWorkingWithForThisProject: {
        type: String,
        default: null,
      },
      ageRange: { type: String, default: null },
      selfIdentifiableQualities: { type: [String], default: null },
      dateNeededToStart: { type: String, default: null },
      dateOfWorkCompleted: { type: String, default: null },
      ongoingWork: { type: Boolean, default: null },
      inPerson: { type: Boolean, default: null },
      virtual: { type: Boolean, default: null },
      locationOrLink: { type: String, default: null },
      localHireOnly: { type: Boolean, default: null },
      openNationWide: { type: Boolean, default: null },
      openWorldWide: { type: Boolean, default: null },
      isPay: { type: Boolean, default: null },
      jobType: {
        type: String,
        enum: Object.values(JobTypeEnum).concat([null]),
        default: null,
      },
      rateType: {
        type: String,
        enum: Object.values(RateTypeEnum).concat([null]),
        default: null,
      },
      hourlyExpectedHours: { type: Number, default: null },
      currency: { type: String, default: null },
      pay: { type: Number, default: null },
      payRange: { type: String, default: null },
      contractDetailsOrCompensation: { type: String, default: null },
      noPayDeferredPay: { type: Boolean, default: null },
      isUnion: { type: Boolean, default: null },
      isNonUnion: { type: Boolean, default: null },
      mediaRequired: { type: [String], default: null },
      titleOfEvent: { type: String, default: null },
      forWhatDescriptionOfEvent: { type: String, default: null },
      whenDatesIncludeRecurring: { type: [String], default: null },
      time: { type: String, default: null },
      isThisRecurring: { type: Boolean, default: null },
      ifRecurring: {
        type: String,
        enum: Object.values(RecurringEnum).concat([null]),
        default: null,
      },
      isThereFee: { type: Boolean, default: null },
      whatIsTheFeeCost: { type: String, default: null },
      sendPaymentTo: { type: String, default: null },
      isItUnionMembersOnly: { type: Boolean, default: null },
      bioOfPersonOrOrganizationHosting: { type: String, default: null },
      whenShouldThisEventExpire: { type: String, default: null },
      _id: false,
    },
  })
  jobDetail: {
    isCastingCallAudition: boolean;
    roleType: string;
    breakDownOfRoleOrPosition: string;
    lookingForProfessions: string;
    demographics: string;
    projectType: string;
    genre: string;
    synopsisOfProjectOrDescriptionOfJobOpportunity: string;
    companyOrNetwork: string;
    typeOfOrganizationYouAreWorkingWithForThisProject: string;
    ageRange: string;
    selfIdentifiableQualities: string[];
    dateNeededToStart: string;
    dateOfWorkCompleted: string;
    ongoingWork: boolean;
    inPerson: boolean;
    virtual: boolean;
    locationOrLink: string;
    localHireOnly: boolean;
    openNationwide: boolean;
    openWorldwide: boolean;
    isPay: boolean;
    jobType: string;
    rateType: string;
    hourlyExpectedHours: number;
    currency: string;
    pay: number;
    payRange: string;
    contractDetailsOrCompensation: string;
    noPayDeferredPay: boolean;
    isUnion: boolean;
    isNonUnion: boolean;
    mediaRequired: string[];
    titleOfEvent: string;
    forWhatDescriptionOfEvent: string;
    whenDatesIncludeRecurring: string[];
    time: string;
    isThisRecurring: boolean;
    ifRecurring: string;
    isThereFee: boolean;
    whatIsTheFeeCost: string;
    sendPaymentTo: string;
    isItUnionMembersOnly: boolean;
    bioOfPersonOrOrganizationHosting: string;
    whenShouldThisEventExpire: string;
  };

  @Prop({
    type: [{ memberAs: String, isSelected: Boolean, _id: false }],
    default: null,
  })
  whoCanSeeRespondToListing: WhoCanSeeData[];

  @Prop({
    type: {
      doesThisProjectInvolveBodyScanning: { type: Boolean, default: null },
      doesThisProjectInvolveAI: { type: Boolean, default: null },
      doesThisProjectInvolveGenerativeAI: { type: Boolean, default: null },
      doesThisProjectInvolveSyntheticPerformers: {
        type: Boolean,
        default: null,
      },
      anyOfTheBodyScansAIquestionsAboveHowSo: { type: String, default: null },
      doesThisProjectInvolveAnyNudity: { type: Boolean, default: null },
      willThereBeAnIntimacyCoordinator: { type: Boolean, default: null },
      doesThisProjectInvolveAnythingElseThatNeedsMentioningUpFront: {
        type: Boolean,
        default: null,
      },
      pleaseTypeWhatItInvolves: { type: String, default: null },
      anyNotes: { type: String, default: null },
      ifInterestedSubmitMaterialsOrResponseHere: {
        type: String,
        default: null,
      },
      forwardJobListingTo: { type: String, default: null },
      whenShouldThisJobListingExpire: { type: String, default: null },
      _id: false,
    },
  })
  whatDoesThisProjectInvolve: {
    doesThisProjectInvolveBodyScanning: boolean;
    doesThisProjectInvolveAI: boolean;
    doesThisProjectInvolveGenerativeAI: boolean;
    doesThisProjectInvolveSyntheticPerformers: boolean;
    anyOfTheBodyScansAIquestionsAboveHowSo: string;
    doesThisProjectInvolveAnyNudity: boolean;
    willThereBeAnIntimacyCoordinator: boolean;
    doesThisProjectInvolveAnythingElseThatNeedsMentioningUpFront: boolean;
    pleaseTypeWhatItInvolves: string;
    anyNotes: string;
    ifInterestedSubmitMaterialsOrResponseHere: string;
    forwardJobListingTo: string;
    whenShouldThisJobListingExpire: string;
  };

  @Prop({
    type: {
      firstName: { type: String, default: null },
      lastName: { type: String, default: null },
      companyName: { type: String, default: null },
      companyWebsite: { type: String, default: null },
      yourJobTitle: { type: String, default: null },
      country: { type: String, default: null },
      postalCode: { type: String, default: null },
      forwardEventTo: { type: String, default: null },
      _id: false,
    },
  })
  yourInfo: {
    firstName: string;
    lastName: string;
    companyName: string;
    companyWebsite: string;
    yourJobTitle: string;
    country: string;
    postalCode: string;
    forwardEventTo: string;
  };

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: User;
}

export const JobListSchema = SchemaFactory.createForClass(JobList);
