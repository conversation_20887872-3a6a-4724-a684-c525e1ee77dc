export enum Title {
  WHO_ARE_YOU = 'who_are_you',
  MORE_ABOUT_YOU = 'more_about_you',
  ABOUT_YOU = 'about_you',
  PROFILE_OPEN_TO = 'profile_open_to',
  WHO_CAN_MESSAGE = 'who_can_message',
}

export enum Privacy {
  PUBLIC = 'public',
  PRIVATE = 'private',
}

export enum Visibility {
  VISIBLE = 'visible',
  HIDDEN = 'hidden',
}

export enum NotificationsType {
  POST = 'post',
  GROUP_INVITE = 'groupinvite',
  TAGGED_COMMENT = 'taggedComment',
  TAGGED_PEOPLE = 'taggedPeople',
  COLLABORATOR = 'collaborators',
  LIKE_POST = 'likePost',
  RE_POST = 'repost',
  CONNECTION = 'connection',
  CLIENT = 'client',
  MEMBER = 'member',
  FOLLOWING = 'following',
  SUBSCRIBER = 'subscriber',

  COMMENT_REACTION = 'commentReaction',
  STORY_REACTION = 'storyReaction',
  GROUP_JOIN = 'groupJoin',
  HIRER_VERIFICATION = 'hirerVerification',
}
export const WhoCanMessageSlugEnum = {
  ANYONE: 'anyone_1',
  FOLLOWER: 'follower_1',
  ALL_UNION_MEMBER: 'all_union_member_1',
  AFFILIATE_MEMBER: 'affiliate_member_2',
  STUDENT: 'student_3',
  AUDIENCE_FANS: 'audience_fans_2',
  UNION_MEMBER: 'union_member_2',
  MY_CONNECTION: 'my_connection_1',
  CONTACT_ME_ONLY_THROUGH_REPRESENTATIVE:
    'contact_me_only_through_representative',
  ONLY_EMPLOYERS_HIRERS_CAN_MESSAGE: 'only_employers_hirers_can_message',
  MEMBERS_IN_MY_UNION: 'members_in_my_union',
  NON_UNION_INDIVIDUALS: 'non-union_individuals',
  MEMBER_AFFILIATE_ORGANIZATION_BUSINESS:
    'member_affiliate_organization_business',
  ALUMNI: 'alumni_4',
  PROFESSOR_TEACHER_INSTRUCTOR_COACHE: 'professor_teacher_instructor_coache_5',
} as const;

export enum WhoAreYouSlug {
  UNION_MEMBER_1 = 'union_member_1',
  AFFILIATE_MEMBER_1 = 'affiliate_member_1',
  PRO_TEA_INT_COACH_1 = 'pro_tea_int_coach_1',
  STUDENT_1 = 'student_1',
  NON_UNION_HOBBYISTS_1 = 'non_union_hobbyists_1',
  AUDIENCE_FANS_1 = 'audience_fans_1',
  UNION_1 = 'union_1',
  AFFILIATE_ORGANIZATION_1 = 'affiliate_organization_1',
  AFFILIATE_BUSINESS_1 = 'affiliate_business_1',
  SCHOOL_TRAINING_FACILITY_1 = 'school_training_facility_1',
}

export const WebBetaProgramSurveyEnum = {
  artist: {
    name: 'Artist / Performer / Creative',
    link: 'https://forms.gle/vwote7z7AdVzbq5d7',
  },
  organization: {
    name: 'Entertainment & Arts Organizations / Businesses',
    link: 'https://forms.gle/ud5uY61cpqGcu6Ny8',
  },
  fan: {
    name: 'Audience Members / Fans',
    link: 'https://forms.gle/Fg9XUtEbDqdwsT1w6',
  },
};

export enum DeviceType {
  ANDROID = 'android',
  IOS = 'ios',
}

export enum RedirectionType {
  USER = 'user',
  POST = 'post',
  GROUP = 'group',
}

export enum InvitationType {
  INITIAL = 'initial',
  ACCEPT = 'accept',
  REJECT = 'reject',
  REMOVE = 'remove',
}

export enum SuggestUsersEnum {
  SUGGESTED = 'suggested',
  MANAGE_GROUP = 'managegroup',
  LOCATION = 'location',
}

export enum PostCollaboratorTagPeopleEnum {
  TAGGED_PEOPLE = 'taggedPeople',
  COLLABORATOR = 'collaborators',
  REPOST_BY = 'repostBy',
  REPOST = 'rePost',
  CONNECTION = 'connection',
  FOLLOWING = 'following',
  CLIENT = 'client',
}

export enum JobApplicationStatus {
  APPLIED = 'Applied',
  ACCEPTED = 'Accepted',
  REJECTED = 'Rejected',
}

export enum JobPostWorkplaceType {
  ONSITE = 'onsite',
  REMOTE = 'remote',
  HYBRID = 'hybrid',
  OTHER = 'other',
}

export enum JobPostWorkType {
  CONTRACT = 'contract',
  FULL_TIME = 'fullTime',
  PART_TIME = 'partTime',
  INTERNSHIP = 'internship',
  TEMPORARY = 'temporary',
  VOLUNTEER = 'volunteer',
  OTHER = 'other',
}

export enum WhoCanFindEventEnum {
  EVERYONE_INCLUDING_AUDIENCE_MEMBER_OR_FANS = 'EveryoneIncludingAudienceMembersOrFans',
  AUDIENCE_MEMBER_OR_FANS = 'AudienceMembersOrFans',
  MY_CONNECTIONS = 'MyConnections',
  MY_FOLLOWERS = 'MyFollowers',
}

export enum WhoAreYou {
  UNION = 'Union (an Organization / Not an individual member)',
  AFFILIATE_BUSINESS = 'Affiliate (Business)',
  SCHOOL_TRAINING_FACILITY = 'School / Training Facility',
  AFFILIATE_ORGANIZATION = 'Affiliate (Organization)',
}
