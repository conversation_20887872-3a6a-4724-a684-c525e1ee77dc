# Unified Profile Visibility API

## Overview

This document describes the unified profile visibility API that consolidates user profile visibility settings and default settings into a single, efficient endpoint to handle all profile visibility options.

## Problem Solved

### Previous Issues:
1. **Two API calls required**: Frontend needed to call both `/user` and `/default` endpoints
2. **Incomplete data**: User endpoint only returned saved settings
3. **Hardcoded frontend**: Frontend had to manage merging user preferences with defaults
4. **First-time user confusion**: No clear way to show all available options for new users

### Solution:
- **Single unified endpoint**: `/api/v1/profile-visibility`
- **Complete data**: Returns all available profile visibility options with current state
- **Smart defaults**: New users get default values for all options
- **No frontend hardcoding**: All logic handled on backend

## API Endpoints

### 1. Unified Profile Visibility (Primary)
```
GET /api/v1/profile-visibility
```

**Purpose**: Get all available profile visibility options with their current state.

**Authentication**: Required (AuthGuard)

**Response**:
```json
{
  "success": true,
  "message": "Profile Visibility Settings fetched successfully",
  "data": {
    "userId": "507f1f77bcf86cd799439011",
    "totalCategories": 3,
    "mainMenu": [
      "Profile viewing options",
      "Profile discovery with contact", 
      "Show active status"
    ],
    "subMenuDetails": [
      {
        "settingName": "Profile viewing options",
        "settingDescription": "Control who can see your profile information",
        "settings": [
          {
            "Name": "Your name headline",
            "isEnabled": true
          },
          {
            "Name": "Private profile character",
            "isEnabled": false
          }
        ],
        "isActive": true,
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      },
      {
        "settingName": "Profile discovery with contact",
        "settingDescription": "Control who can find your profile",
        "settings": [
          {
            "Name": "Everyone",
            "isEnabled": false
          },
          {
            "Name": "My Connections",
            "isEnabled": false
          },
          {
            "Name": "People I Follow",
            "isEnabled": false
          },
          {
            "Name": "No One",
            "isEnabled": true
          }
        ],
        "isActive": true,
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      },
      {
        "settingName": "Show active status",
        "settingDescription": "Control who can see when you're online",
        "settings": [
          {
            "Name": "Everyone",
            "isEnabled": false
          },
          {
            "Name": "My Connections",
            "isEnabled": false
          },
          {
            "Name": "People I Follow",
            "isEnabled": false
          },
          {
            "Name": "No One",
            "isEnabled": true
          }
        ],
        "isActive": true,
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ]
  }
}
```

**Key Features**:
- Returns ALL available profile visibility options (not just saved ones)
- Shows current state (enabled/disabled) for each option
- New users get default values for all options
- Includes metadata like total categories and main menu structure

### 2. Save Profile Visibility Settings
```
POST /api/v1/profile-visibility/save
```

**Purpose**: Save user's profile visibility preferences (supports partial updates).

**Authentication**: Required (AuthGuard)

**Request Body** (supports partial updates):
```json
{
  "profileViewingOptions": [
    {
      "Name": "Your name headline",
      "isEnabled": true
    },
    {
      "Name": "Private profile character",
      "isEnabled": false
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "message": "Profile Visibility Settings updated successfully",
  "data": {
    "profileVisibility": {
      // Updated profile visibility object
    }
  }
}
```

### 3. Legacy Endpoints (Deprecated but maintained)

#### Get User Profile Visibility (Legacy)
```
GET /api/v1/profile-visibility/user
```
Returns only user's saved settings (legacy behavior).

#### Get Default Profile Visibility (Legacy)
```
GET /api/v1/profile-visibility/default
```
Returns all default profile visibility settings (legacy behavior).

## Frontend Implementation Guide

### 1. First Time User Experience
```javascript
// When user opens profile visibility settings for the first time
const response = await fetch('/api/v1/profile-visibility', {
  headers: { 'Authorization': `Bearer ${token}` }
});

const { data } = await response.json();

// data.subMenuDetails contains ALL available options with current state
// For new users, options will have default values
```

### 2. Display All Profile Visibility Options
```javascript
// Show all categories and their options
data.subMenuDetails.forEach(category => {
  console.log(`Category: ${category.settingName}`);
  console.log(`Description: ${category.settingDescription}`);
  
  category.settings.forEach(option => {
    console.log(`  ${option.Name}: ${option.isEnabled ? 'ON' : 'OFF'}`);
  });
});
```

### 3. Save User Preferences
```javascript
// Save all settings
const profileVisibilityToSave = {
  profileViewingOptions: data.subMenuDetails[0].settings,
  profileDiscoveryOptions: data.subMenuDetails[1].settings,
  activeStatusOptions: data.subMenuDetails[2].settings
};

await fetch('/api/v1/profile-visibility/save', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(profileVisibilityToSave)
});
```

### 4. Save Partial Settings
```javascript
// Save only specific category
const partialSettings = {
  profileViewingOptions: [
    { Name: "Your name headline", isEnabled: true },
    { Name: "Private profile character", isEnabled: false }
  ]
};

await fetch('/api/v1/profile-visibility/save', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(partialSettings)
});
```

## Database Schema

### User Settings Collection
```javascript
{
  _id: ObjectId,
  userId: ObjectId,
  profileVisibility: {
    profileViewingOptions: [
      {
        Name: String,
        isEnabled: Boolean
      }
    ],
    profileDiscoveryOptions: [
      {
        Name: String,
        isEnabled: Boolean
      }
    ],
    activeStatusOptions: [
      {
        Name: String,
        isEnabled: Boolean
      }
    ]
  }
}
```

### Default Settings Collection
```javascript
{
  _id: ObjectId,
  category: "profileVisibility",
  settingName: "Profile viewing options",
  settingDescription: "Control who can see your profile information",
  settings: [
    {
      Name: "Your name headline",
      isEnabled: true
    },
    {
      Name: "Private profile character", 
      isEnabled: false
    }
  ],
  isActive: Boolean
}
```

## Profile Visibility Categories

Based on the default settings:
- **Profile viewing options**: 2 options
- **Profile discovery with contact**: 4 options  
- **Show active status**: 4 options

## Migration Strategy

### For Existing Users:
1. Existing profile visibility settings are preserved
2. New unified endpoint returns their current preferences
3. Missing options are added with default values

### For New Users:
1. All available profile visibility options are returned
2. All options default to their predefined values
3. User can modify as needed

## Benefits

1. **Single API Call**: Frontend only needs one call to get complete data
2. **No Hardcoding**: All profile visibility logic handled on backend
3. **Scalable**: Handles multiple categories and options efficiently
4. **User-Friendly**: Clear first-time experience with all options visible
5. **Backward Compatible**: Legacy endpoints still work
6. **Type Safe**: Proper validation and DTOs

## Error Handling

- **Invalid Option Names**: Returns 400 with validation error
- **Missing Required Fields**: Returns 400 with validation error
- **Authentication**: 401 for unauthenticated requests
- **User Not Found**: Returns 404 if user doesn't exist

## Performance Considerations

- **Indexing**: Ensure indexes on `userId`, `category`, `isActive`
- **Caching**: Consider caching default profile visibility settings
- **Pagination**: Not needed as options are typically < 20 per category 