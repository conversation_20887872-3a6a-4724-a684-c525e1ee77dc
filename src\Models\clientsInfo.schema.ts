import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from './user.schema';
import { StatusEnum } from './followerInfo.schema';

export type clientInfoDocument = ClientInfo & Document;

@Schema({ timestamps: true })
export class ClientInfo {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  userId: User;

  @Prop({ default: StatusEnum.PENDING, enum: StatusEnum })
  status: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  clientId: User;
}

export const ClientInfoSchema = SchemaFactory.createForClass(ClientInfo);
