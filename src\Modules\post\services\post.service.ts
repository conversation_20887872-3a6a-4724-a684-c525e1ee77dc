import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, PipelineStage } from 'mongoose';
import { ParsedQs } from 'qs';
import {
  InvitationType,
  NotificationsType,
  PostCollaboratorTagPeopleEnum,
  Privacy,
  RedirectionType,
} from 'src/common/constant/enum';
import { BranchDeeplinkService } from 'src/common/services/branchDeeplink.service';
import { NotificationService } from 'src/common/services/notification.service';
import {
  NOTIFICATION_MESSAGES,
  notificationMessage,
} from 'src/Custom/helpers/message.helper';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import { bookmarkDocument } from 'src/Models/bookmark.schema';
import { commentDocument } from 'src/Models/comment.schema';
import { likeDocument } from 'src/Models/like.schema';
import { postDocument, PostLabelEnum } from 'src/Models/post.schema';
import { storyDocument } from 'src/Models/story.schema';
import { iAmMemberEnum, userDocument } from 'src/Models/user.schema';
import CONSTANT from '../../../common/constant/common.constant';
import { CommentRepository } from 'src/Repositories/comment.repository';
import { UserRepository } from 'src/Repositories/user.repository';
import { PostRepository } from 'src/Repositories/post.repository';
import { StatusEnum, ConnectionInfoDocument } from 'src/Models/connectionInfo.schema';
import { connectionRequestStatus } from 'src/Modules/user/dtos/user.dtos';
import { SubscriberDocument } from 'src/Models/subscriber.schema';
import {
  NotificationDocument,
  Notifications,
} from 'src/Models/notification.schema';
import { acceptRejectCollabRequestDTO } from '../dtos/post.dtos';
import { WhoCanMessageValidatorService } from 'src/Modules/user/helper/whoCanMessageValidator';
import { followerInfoDocument } from 'src/Models/followerInfo.schema';

@Injectable()
export class PostService {
  constructor(
    private readonly commentRepository: CommentRepository,

    private readonly postRepository: PostRepository,
    private readonly userRepository: UserRepository,
    @InjectModel('User') private readonly userModel: Model<userDocument>,
    @InjectModel('Post') private readonly postModel: Model<postDocument>,
    @InjectModel('Like') private readonly likeModel: Model<likeDocument>,
    @InjectModel(Notifications.name)
    private readonly notificationModel: Model<NotificationDocument>,
    @InjectModel('Subscriber')
    private readonly subscriberModel: Model<SubscriberDocument>,
    @InjectModel('Comment')
    private readonly commentModel: Model<commentDocument>,
    @InjectModel('ConnectionInfo') private readonly connectionInfoModel: Model<ConnectionInfoDocument>,
    @InjectModel('FollowerInfo') private readonly followerInfoModel: Model<followerInfoDocument>,
    private readonly whoCanMessageValidatorService: WhoCanMessageValidatorService,

    @InjectModel('Bookmark')
    private readonly bookmarkModel: Model<bookmarkDocument>,
    @InjectModel('Story') private readonly storyModel: Model<storyDocument>,
    private readonly branchDeeplinkService: BranchDeeplinkService,
    private notificationService: NotificationService,
  ) {}

  public async addPost(req, postData) {
    try {
      const { user: loggedInUser } = req;

      if (loggedInUser.iAmMember == iAmMemberEnum.AUDIENCE_MEMBER_FAN) {
        throw new HttpException(CONSTANT.POST_NOT_ALLOW, HttpStatus.FORBIDDEN);
      }

      const data = {
        ...postData,
        location: postData.location !== null ? postData.location : null,
        collaborators: postData.collaborators.map((col) => ({
          id: new mongoose.Types.ObjectId(col),
          status: StatusEnum.PENDING,
        })),
        userId: loggedInUser._id,
      };
      const post = await new this.postModel(data).save();

      const notificationTasks = [];

      if (
        Array.isArray(postData?.collaborators) &&
        postData.collaborators.length > 0
      ) {
        notificationTasks.push(
          ...postData.collaborators.map((userId) => {
            const params = {
              title: NOTIFICATION_MESSAGES.HAS_MADE_COLLABORATION_POST,
              notificationMessage: '',
              notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
              redirectionType: RedirectionType.POST,
              receiver: userId,
              sender: loggedInUser._id,
              postId: post._id,
              action: InvitationType.INITIAL,
            };
            return this.notificationService.sendToTopic(null, params);
          }),
        );
      }

      if (
        Array.isArray(postData?.taggedPeople) &&
        postData.taggedPeople.length > 0
      ) {
        notificationTasks.push(
          ...postData.taggedPeople.map((userId) => {
            const params = {
              title: NOTIFICATION_MESSAGES.TAGGED_YOU_IN_POST,
              notificationMessage: '',
              notificationType: PostCollaboratorTagPeopleEnum.TAGGED_PEOPLE,
              redirectionType: RedirectionType.POST,
              receiver: userId,
              sender: loggedInUser._id,
              postId: post._id,
            };
            return this.notificationService.sendToTopic(null, params);
          }),
        );
      }

      const subscribers = await this.subscriberModel.find({
        userId: loggedInUser._id,
      });

      if (subscribers.length > 0) {
        subscribers.map((sub) => {
          const params = {
            title: NOTIFICATION_MESSAGES.HAS_MADE_POST,
            notificationMessage: '',
            notificationType: NotificationsType.POST,
            redirectionType: RedirectionType.POST,
            receiver: sub.subscriberId,
            sender: loggedInUser._id,
            postId: post._id,
          };

          return this.notificationService.sendToTopic(null, params);
        });
      }

      await Promise.all(notificationTasks);

      const deepLink = await this.branchDeeplinkService.generateDeepLink(
        post._id.toString(),
        {
          title: loggedInUser.userName,
          description: post.caption,
          image_url:
            post.media[0].mediaType === 'video'
              ? post.media[0].thumbUrl
              : post.media[0].url,
        },
      );

      await this.postModel.findByIdAndUpdate(
        post._id,
        {
          shareableLink: deepLink,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      // const post_data: any = await this.postModel
      //   .findOne(post._id)
      //   .select('-updatedAt -__v')
      //   .populate({
      //     path: 'userId',
      //     select:
      //       '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      //   })
      //   .populate({
      //     path: 'fundraisers',
      //     select:
      //       '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      //   })
      //   .populate({
      //     path: 'collaborators',
      //     select:
      //       '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      //   });

      // post_data.user = post_data.userId;
      // delete post_data.userId;

      // post_data.collaborators = post_data.collaborators.map((col) => {
      //   return col.id;
      // });

      await this.userModel.findByIdAndUpdate(loggedInUser._id, {
        $inc: { posts: 1 },
      });

      return successResponse(
        null,
        CONSTANT.ADDED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async acceptRejectCollaborationRequest(
    req,
    postData: acceptRejectCollabRequestDTO,
  ) {
    try {
      const { user: loggedInUser } = req;

      const post: any = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postData.postId),
        collaborators: {
          $elemMatch: {
            id: new mongoose.Types.ObjectId(String(loggedInUser._id)),
            status: StatusEnum.PENDING,
          },
        },
      });

      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const updatePost = await this.postModel.findOneAndUpdate(
        {
          _id: new mongoose.Types.ObjectId(postData.postId),
          collaborators: {
            $elemMatch: {
              id: new mongoose.Types.ObjectId(loggedInUser._id),
              status: StatusEnum.PENDING,
            },
          },
        },
        {
          $set: {
            'collaborators.$[collaborator].status': postData.status,
          },
        },
        {
          new: true,
          arrayFilters: [
            {
              'collaborator.id': new mongoose.Types.ObjectId(
                String(loggedInUser._id),
              ),
            },
          ],
        },
      );
      if (!updatePost) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      // if reject then remove from the list
      if (postData.status === StatusEnum.REJECT) {
        await this.postModel.updateOne(
          {
            _id: new mongoose.Types.ObjectId(postData.postId),
          },
          {
            $pull: {
              collaborators: {
                id: loggedInUser._id,
              },
            },
          },
        );
      }

      // delete notification
      await this.notificationModel.deleteMany({
        notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
        redirectionType: RedirectionType.POST,
        sender: updatePost.userId,
        receiver: loggedInUser._id,
        postId: post._id,
      });

      const action =
        postData.status === StatusEnum.ACCEPT ? 'accepted' : 'rejected';

      const params = {
        title: NOTIFICATION_MESSAGES.COLLABORATION_REQUEST_STATUS(action),
        notificationMessage: '',
        notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
        redirectionType: RedirectionType.POST,
        receiver: post.userId,
        sender: loggedInUser._id,
        postId: post._id,
      };

      await this.notificationService.sendToTopic(null, params);
      // Send notification to the post owner

      const params2 = {
        title: NOTIFICATION_MESSAGES.COLLABORATION_REQUEST_STATUS(action),
        notificationMessage: '',
        notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
        redirectionType: RedirectionType.POST,
        receiver: loggedInUser._id,
        sender: post.userId,
        postId: post._id,
      };

      // Send notification to the post owner

      await this.notificationService.sendToTopic(null, params2);

      return successResponse(
        updatePost,
        CONSTANT.ADDED_SUCCESSFULLY('Collaboration Request'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async repost(req, postData) {
    try {
      const { user: loggedInUser } = req;

      const post: any = await this.postModel
        .findOne({
          _id: new mongoose.Types.ObjectId(postData.postId),
        })
        .populate('userId', 'userName _id')
        .exec();

      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      // Save the repost

      // Increment repost count on the original post atomically
      const originalPostCount = await this.postModel.findByIdAndUpdate(
        post._id,
        {
          $inc: { repostCount: 1 },
        },
        { new: true },
      );

      const repostCount = originalPostCount.repostCount;

      const repostObj = {
        caption: post.caption,
        taggedCaptionUsers: post.taggedCaptionUsers,
        location: post.location,
        media: post.media,
        shareableLink: post.shareableLink,
        collaborators: post.collaborators,
        whoCanComment: post.whoCanComment,
        isTurnOffComment: post.isTurnOffComment,
        isHideLikeViews: post.isHideLikeViews,
        isDisableRepostShare: post.isDisableRepostShare,
        postLabel: post.postLabel,
        taggedPeople: post.taggedPeople,
        fundraisers: post.fundraisers,
        taggedProduct: post.taggedProduct,
        aiLabels: post.aiLabels,
        userId: post.userId?._id,
        repostBy: loggedInUser._id,
        repostCount: repostCount,
        post: post._id,
      };
      if (postData.repostCaption) {
        repostObj['repostCaption'] = postData.repostCaption;
      }
      if (postData.repostTaggedCaptionUsers) {
        repostObj['repostTaggedCaptionUsers'] =
          postData.repostTaggedCaptionUsers;
      }

      // Increment repost count to all re-posts atomically
      await this.postModel.updateMany(
        { post: post._id },
        {
          $set: { repostCount: repostCount },
        },
      );

      // Create a new post for the repost
      const repost = await this.postModel.create(repostObj);

      if (post.userId?._id.toString() !== loggedInUser._id?.toString()) {
        const params = {
          title: NOTIFICATION_MESSAGES.REPOST_BY(loggedInUser?.usernName ?? ''),
          notificationMessage: '',
          notificationType: NotificationsType.RE_POST, // Updated type for tagging
          redirectionType: RedirectionType.POST,
          receiver: post.userId?._id,
          sender: loggedInUser._id,
          postId: post._id,
        };
        await this.notificationService.sendToTopic(null, params);
      }

      const createdRepost = await this.postModel
        .findOne({ _id: repost._id })
        .populate({
          path: 'userId',
          match: { isFakeAccount: false },
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        })
        .populate({
          path: 'repostBy',
          match: { isFakeAccount: false },
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        });

      return successResponse(
        createdRepost,
        CONSTANT.ADDED_SUCCESSFULLY('Re-Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async editPost(req, postData) {
    try {
      const { user: loggedInUser } = req;
      const { caption = '', repostCaption = '' } = postData;

      let post: any = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postData.postId),
        $or: [
          { userId: loggedInUser._id }, // Match userId
          { repostBy: loggedInUser._id }, // Match repostBy
        ],
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const payload = {
        ...postData,
        isCaptionEdited:
          post.isCaptionEdited || (!post.repostBy && post.caption !== caption),
        isRepostCaptionEdited:
          post.isRepostCaptionEdited ||
          (post.repostBy && post.repostCaption !== repostCaption),
      };

      if (postData.collaborators?.length > 0) {
        const existingCollaborators = post.collaborators.map((collab) =>
          collab?.id?.toString(),
        );

        const newCollaborators = postData.collaborators
          .filter(
            (collabId) =>
              !existingCollaborators.includes(collabId?.id?.toString()),
          )
          .map((collabId) => ({
            id: new mongoose.Types.ObjectId(collabId?.id),
            status: StatusEnum.PENDING,
          }));

        const collaboratorsToRemove = post.collaborators.filter(
          (collab) =>
            !postData.collaborators.some(
              (newCollab) =>
                newCollab?.id?.toString() === collab?.id.toString(),
            ),
        );

        if (collaboratorsToRemove.length > 0) {
          /// remove notification
          collaboratorsToRemove.map((col) => {
            this.notificationModel.deleteMany({
              notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
              redirectionType: RedirectionType.POST,
              sender: loggedInUser._id,
              receiver: col.id,
              postId: post._id,
            });
          });
        }

        payload.collaborators = [
          ...post.collaborators.filter((collab) =>
            postData.collaborators.some(
              (newCollab) =>
                newCollab?.id?.toString() === collab?.id?.toString(),
            ),
          ),
          ...newCollaborators,
        ];

        // send notification to new collaborators
        const notificationTasks = [];

        if (newCollaborators.length > 0) {
          notificationTasks.push(
            ...newCollaborators.map((collab) => {
              const params = {
                title: NOTIFICATION_MESSAGES.HAS_MADE_COLLABORATION_POST,
                notificationMessage: '',
                notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
                redirectionType: RedirectionType.POST,
                receiver: collab.id,
                sender: loggedInUser._id,
                postId: post._id,
                action: InvitationType.INITIAL,
              };
              return this.notificationService.sendToTopic(null, params);
            }),
          );
        }

        // // send notification to removed collaborators
        // if (collaboratorsToRemove.length > 0) {
        //   notificationTasks.push(
        //     ...collaboratorsToRemove.map((collab) => {
        //       const params = {
        //         title: 'You have been removed from a collaboration post.',
        //         notificationMessage: '',
        //         notificationType: PostCollaboratorTagPeopleEnum.COLLABORATOR,
        //         redirectionType: RedirectionType.POST,
        //         receiver: collab.id,
        //         sender: loggedInUser._id,
        //         postId: post._id,
        //         action: InvitationType.REMOVE,
        //       };
        //       return this.notificationService.sendToTopic(null, params);
        //     }),
        //   );
        // }

        await Promise.all(notificationTasks);
      }

      post = await this.postModel.findByIdAndUpdate(
        post._id,
        {
          ...payload,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      let post_data: any = await this.postModel.aggregate([
        { $match: { _id: new mongoose.Types.ObjectId(post._id) } },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userId',
            pipeline: [
              { $match: { isFakeAccount: false } },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        { $unwind: { path: '$userId', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedPeople',
            foreignField: '_id',
            as: 'taggedPeople',
            pipeline: [
              { $match: { isFakeAccount: false } },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            collaborators: {
              $filter: {
                input: '$collaborators',
                as: 'collab',
                cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'collaborators.id',
            foreignField: '_id',
            as: 'collaborators',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'repostBy',
            foreignField: '_id',
            as: 'repostBy',
            pipeline: [
              { $match: { isFakeAccount: false } },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        { $unwind: { path: '$repostBy', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
            pipeline: [
              {
                $lookup: {
                  from: 'users',
                  localField: 'createdBy',
                  foreignField: '_id',
                  as: 'createdBy',
                  pipeline: [
                    { $match: { isFakeAccount: false } },
                    {
                      $project: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  createdBy: { $arrayElemAt: ['$createdBy', 0] },
                },
              },
            ],
          },
        },
        { $unwind: { path: '$group', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            __v: 0,
          },
        },
      ]);

      post_data = post_data[0]; // Extract the first (and only) document from the aggregation result

      post_data.user = post_data.userId;
      delete post_data.userId;

      return successResponse(
        post_data,
        CONSTANT.UPDATED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      console.log(error);
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async likeUnlikePost(req, postData) {
    try {
      const { user: loggedInUser } = req;
      const { postId, type } = postData;

      const post: any = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postId),
      });

      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const doesAnyReactionExist = await this.postModel.exists({
        _id: new mongoose.Types.ObjectId(postId),
        reactions: {
          $elemMatch: {
            userId: new mongoose.Types.ObjectId(loggedInUser._id),
          },
        },
      });

      if (!doesAnyReactionExist) {
        const addReaction = await this.postModel
          .findOneAndUpdate(
            {
              _id: new mongoose.Types.ObjectId(postId),
            },
            {
              $push: {
                reactions: {
                  userId: new mongoose.Types.ObjectId(loggedInUser._id),
                  type,
                },
              },
              $inc: {
                [`reactionCounts.${type}`]: 1,
                totalReactions: 1,
              },
            },
            { new: true },
          )
          .populate({
            path: 'fundraisers',
            select:
              '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
            match: { isFakeAccount: false },
          })
          .lean();

        //Send Notification To mobile
        const notificationData = {
          enum: type,
          notificationType: NotificationsType.LIKE_POST,
        };

        if (post?.userId.toString() !== loggedInUser._id.toString()) {
          const params = {
            title: notificationMessage(notificationData, null),
            notificationMessage: '',
            notificationType: NotificationsType.LIKE_POST,
            redirectionType: RedirectionType.POST,
            receiver: post?.userId,
            postId: postId,
            sender: loggedInUser._id,
          };
          await this.notificationService.sendToTopic(null, params);
        }

        return successResponse(
          {
            ...addReaction,
            userId: null,
            repostBy: null,
            collaborators: null,
            taggedPeople: null,
            isLike: true,
            currentUserReaction: type,
            group: null,
            fundraisers: addReaction?.fundraisers || [],
          },
          '',
          HttpStatus.OK,
        );
      }

      const reaction = await this.postModel
        .findOne({
          _id: new mongoose.Types.ObjectId(postId),
          reactions: {
            $elemMatch: {
              userId: new mongoose.Types.ObjectId(loggedInUser._id),
              type: {
                $ne: type,
              },
            },
          },
        })
        .populate({
          path: 'fundraisers',
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
          match: { isFakeAccount: false },
        })
        .lean();

      const existingReaction = reaction
        ? reaction.reactions.find(
            (el) => el.userId.toString() === loggedInUser._id.toString(),
          ).type
        : null;

      if (existingReaction) {
        const addAnotherReaction = await this.postModel
          .findOneAndUpdate(
            {
              _id: new mongoose.Types.ObjectId(postId),
              reactions: {
                $elemMatch: {
                  userId: new mongoose.Types.ObjectId(loggedInUser._id),
                  type: {
                    $ne: type,
                  },
                },
              },
            },
            {
              $set: {
                'reactions.$[reaction].type': type,
              },
              $inc: {
                [`reactionCounts.${existingReaction}`]: -1,
                [`reactionCounts.${type}`]: 1,
              },
            },
            {
              new: true,
              arrayFilters: [
                {
                  'reaction.userId': new mongoose.Types.ObjectId(
                    loggedInUser._id,
                  ),
                },
              ],
            },
          )
          .populate({
            path: 'fundraisers',
            select:
              '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
            match: { isFakeAccount: false },
          })
          .lean();

        return successResponse(
          {
            ...addAnotherReaction,
            userId: null,
            repostBy: null,
            collaborators: null,
            taggedPeople: null,
            isLike: true,
            currentUserReaction: type,
            group: null,
            fundraisers: addAnotherReaction?.fundraisers || [],
          },
          '',
          HttpStatus.OK,
        );
      }

      const removeReaction = await this.postModel
        .findOneAndUpdate(
          {
            _id: new mongoose.Types.ObjectId(postId),
            reactions: {
              $elemMatch: {
                userId: new mongoose.Types.ObjectId(loggedInUser._id),
                type,
              },
            },
          },
          {
            $pull: {
              reactions: {
                userId: new mongoose.Types.ObjectId(loggedInUser._id),
              },
            },
            $inc: {
              [`reactionCounts.${type}`]: -1,
              totalReactions: -1,
            },
          },
          { new: true },
        )
        .populate({
          path: 'fundraisers',
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
          match: { isFakeAccount: false },
        })
        .lean();

      return successResponse(
        {
          ...removeReaction,
          userId: null,
          collaborators: null,
          taggedPeople: null,
          isLike: false,
          currentUserReaction: null,
          group: null,
          repostBy: null,
          fundraisers: removeReaction?.fundraisers || [],
        },
        '',
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addCommentPost(req, postData) {
    try {
      const { user: loggedInUser } = req;

      const post = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postData.postId),
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const data = {
        comment: postData.comment,
        postId: postData.postId,
        parentId: postData?.parentId || null,
        userId: loggedInUser._id,
        taggedUsers: postData.taggedUsers,
      };

      const comment = await this.commentModel.create(data);

      post.totalComments += 1;
      post.save();

      const comment_data: any = await this.commentModel
        .findOne(comment._id)
        .select('-updatedAt -__v')
        .populate({
          path: 'userId',
          select:
            '_id firstName lastName userName profileImage businessOrganizationName accountVerified isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        })
        .populate({
          path: 'taggedUsers',
          select: '-__v',
          populate: [
            {
              path: 'user',
              select:
                '_id firstName lastName userName profileImage businessOrganizationName accountVerified isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
            },
          ],
        })
        .lean();

      comment_data.user = comment_data.userId;
      delete comment_data.userId;

      //send notifications to tagged users
      if (postData.taggedUsers && postData.taggedUsers.length > 0) {
        const notificationTasks = postData.taggedUsers.map((user) => {
          const params = {
            title: NOTIFICATION_MESSAGES.COMMENT_MENTIONED,
            notificationMessage: '',
            notificationType: NotificationsType.TAGGED_COMMENT,
            redirectionType: RedirectionType.POST,
            receiver: user.user._id,
            sender: loggedInUser._id,
            postId: post._id,
          };
          return this.notificationService.sendToTopic(null, params);
        });

        await Promise.all(notificationTasks);
      }

      return successResponse(
        comment_data,
        CONSTANT.ADDED_SUCCESSFULLY('Comment'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async editPostComment(req, commentData) {
    try {
      const { user: loggedInUser } = req;

      let comment = await this.commentModel.findOne({
        _id: new mongoose.Types.ObjectId(commentData.commentId),
        userId: new mongoose.Types.ObjectId(loggedInUser._id),
      });
      if (!comment) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Comment'),
          HttpStatus.NOT_FOUND,
        );
      }

      comment = await this.commentModel.findByIdAndUpdate(
        comment._id,
        {
          comment: commentData.comment,
          isEdited: true,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      return successResponse(
        comment,
        CONSTANT.UPDATED_SUCCESSFULLY('Comment'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async likeUnlikeComments(req, postData) {
    try {
      const { user: loggedInUser } = req;
      const { commentId, type, postId } = postData;

      const postDetail = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postId),
      });

      if (!postDetail) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const comment = await this.commentModel.findOne({
        _id: new mongoose.Types.ObjectId(commentId),
      });

      if (!comment) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Comment'),
          HttpStatus.NOT_FOUND,
        );
      }

      const doesAnyReactionExist = await this.commentModel.exists({
        _id: new mongoose.Types.ObjectId(commentId),
        reactions: {
          $elemMatch: {
            userId: new mongoose.Types.ObjectId(loggedInUser._id),
          },
        },
      });

      if (!doesAnyReactionExist) {
        const addReaction = await this.commentModel
          .findOneAndUpdate(
            {
              _id: new mongoose.Types.ObjectId(commentId),
            },
            {
              $push: {
                reactions: {
                  userId: new mongoose.Types.ObjectId(loggedInUser._id),
                  type,
                },
              },
              $inc: {
                [`reactionCounts.${type}`]: 1,
                totalReactions: 1,
              },
            },
            { new: true },
          )
          .lean();

        //Send Notification To mobile
        const notificationData = {
          enum: type,
          notificationType: NotificationsType.COMMENT_REACTION,
        };

        if (addReaction?.userId.toString() !== loggedInUser._id.toString()) {
          const params = {
            title: notificationMessage(notificationData, null),
            notificationMessage: '',
            notificationType: NotificationsType.COMMENT_REACTION,
            redirectionType: RedirectionType.POST,
            receiver: addReaction?.userId,
            postId: postDetail._id,
            sender: loggedInUser._id,
          };
          await this.notificationService.sendToTopic(null, params);
        }

        return successResponse(
          {
            ...addReaction,
            isLike: true,
            userId: null,
            taggedUsers: null,
            currentUserReaction: type,
          },
          '',
          HttpStatus.OK,
        );
      }

      const reaction = await this.commentModel
        .findOne({
          _id: new mongoose.Types.ObjectId(commentId),
          reactions: {
            $elemMatch: {
              userId: new mongoose.Types.ObjectId(loggedInUser._id),
              type: {
                $ne: type,
              },
            },
          },
        })
        .lean();

      const existingReaction = reaction
        ? reaction.reactions.find(
            (el) => el.userId.toString() === loggedInUser._id.toString(),
          ).type
        : null;

      if (existingReaction) {
        const addAnotherReaction = await this.commentModel
          .findOneAndUpdate(
            {
              _id: new mongoose.Types.ObjectId(commentId),
              reactions: {
                $elemMatch: {
                  userId: new mongoose.Types.ObjectId(loggedInUser._id),
                  type: {
                    $ne: type,
                  },
                },
              },
            },
            {
              $set: {
                'reactions.$[reaction].type': type,
              },
              $inc: {
                [`reactionCounts.${existingReaction}`]: -1,
                [`reactionCounts.${type}`]: 1,
              },
            },
            {
              new: true,
              arrayFilters: [
                {
                  'reaction.userId': new mongoose.Types.ObjectId(
                    loggedInUser._id,
                  ),
                },
              ],
            },
          )
          .lean();

        //Send Notification To mobile
        const notificationData = {
          enum: type,
          notificationType: NotificationsType.COMMENT_REACTION,
        };

        if (
          addAnotherReaction?.userId.toString() !== loggedInUser._id.toString()
        ) {
          const params = {
            title: notificationMessage(notificationData, null),
            notificationMessage: '',
            notificationType: NotificationsType.COMMENT_REACTION,
            redirectionType: RedirectionType.POST,
            receiver: addAnotherReaction?.userId,
            postId: postDetail._id,
            sender: loggedInUser._id,
          };
          await this.notificationService.sendToTopic(null, params);
        }

        return successResponse(
          {
            ...addAnotherReaction,
            isLike: true,
            userId: null,
            taggedUsers: null,
            currentUserReaction: type,
          },
          '',
          HttpStatus.OK,
        );
      }

      const removeReaction = await this.commentModel
        .findOneAndUpdate(
          {
            _id: new mongoose.Types.ObjectId(commentId),
            reactions: {
              $elemMatch: {
                userId: new mongoose.Types.ObjectId(loggedInUser._id),
                type,
              },
            },
          },
          {
            $pull: {
              reactions: {
                userId: new mongoose.Types.ObjectId(loggedInUser._id),
              },
            },
            $inc: {
              [`reactionCounts.${type}`]: -1,
              totalReactions: -1,
            },
          },
          { new: true },
        )
        .lean();

      // delete notification

      await this.notificationModel.deleteMany({
        notificationType: NotificationsType.COMMENT_REACTION,
        sender: loggedInUser._id,
        receiver: removeReaction?.userId,
        postId: postDetail._id,
      });

      return successResponse(
        {
          ...removeReaction,
          isLike: false,
          userId: null,
          taggedUsers: null,
          currentUserReaction: null,
        },
        '',
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async commentReactions(req, commentId) {
    try {
      const { page, perPage, sort, sortBy, search, type } = req.query;

      // Search fields
      const searchFields = ['user.firstName', 'user.lastName', 'user.userName'];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };
      const matchObj = {
        _id: new mongoose.Types.ObjectId(commentId),
      };

      //Check For Post Exist Or Not
      const comment = await this.commentModel.findOne({
        _id: new mongoose.Types.ObjectId(commentId),
      });
      if (!comment) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      // Create Search, Sort and Pagination
      const { searchObj, filterObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      const pipelineArr: any = [
        {
          $match: matchObj,
        },
        {
          $unwind: {
            path: '$reactions',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            'reactions.type': type,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'reactions.userId',
            foreignField: '_id',
            as: 'user',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$user',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 1,
            user: 1,
            reactions: 1,
            reactionCounts: 1,
          },
        },
      ];

      if (type == 'all') {
        pipelineArr.splice(2, 1);
      }
      if (Object.keys(searchFilterObj).length)
        pipelineArr.push({ $match: searchFilterObj });

      if (Object.keys(sortObj).length) pipelineArr.push({ $sort: sortObj });

      if (skipData || skipData === 0) pipelineArr.push({ $skip: skipData });
      if (limitData || limitData === 0) pipelineArr.push({ $limit: limitData });

      // Get forms data query
      const reactionsData = await this.commentModel.aggregate(pipelineArr);

      let reactionsCount: any;
      if (Object.keys(searchFilterObj).length) {
        const lastPipelineCount = type === 'all' ? 6 : 7;
        reactionsCount = await this.commentModel.aggregate(
          pipelineArr.slice(0, lastPipelineCount),
        );
      } else {
        const lastPipelineCount = type === 'all' ? 5 : 6;
        reactionsCount = await this.commentModel.aggregate(
          pipelineArr.slice(0, lastPipelineCount),
        );
      }

      // Pagination parameters
      const totalResults = reactionsCount.length;
      const currentResults = reactionsData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };
      return successResponse(
        reactionsData,
        CONSTANT.FETCHED_SUCCESSFULLY('Post likes'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async deleteComment(req, commentId) {
    try {
      const { user: loggedInUser } = req;

      const comment = await this.commentModel.findOne({
        _id: new mongoose.Types.ObjectId(commentId),
        userId: loggedInUser._id,
      });
      if (!comment) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Comment'),
          HttpStatus.NOT_FOUND,
        );
      }

      const postId = comment.postId;

      await this.commentModel.deleteOne(comment._id);

      await this.postModel.updateOne(
        { _id: postId },
        { $inc: { totalComments: -1 } }, // Decrement totalComments by 1
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Comment'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getCollaborators(req) {
    try {
      const { user: loggedInUser } = req;
      const { page, perPage, sort, sortBy, search } = req.query;

      const searchFields = [
        'userName',
        'firstName',
        'lastName',
        'businessOrganizationName',
      ];

      // Get users who are connected or followed by the current user
      const connectedUsers = await this.connectionInfoModel.find({
        $or: [
          { userId: loggedInUser._id, status: StatusEnum.ACCEPT },
          { connectionWithId: loggedInUser._id, status: StatusEnum.ACCEPT },
        ],
      }).distinct('connectionWithId');

      const followedUsers = await this.followerInfoModel.find({
        followerId: loggedInUser._id,
        status: StatusEnum.ACCEPT,
      }).distinct('followingId');

      // Combine connected and followed users, excluding the current user
      const eligibleUserIds = [
        ...connectedUsers.filter(id => id.toString() !== loggedInUser._id.toString()),
        ...followedUsers.filter(id => id.toString() !== loggedInUser._id.toString())
      ];

      // Remove duplicates
      const uniqueUserIds = [...new Set(eligibleUserIds.map(id => id.toString()))];

      if (uniqueUserIds.length === 0) {
        return successResponse(
          [],
          CONSTANT.FETCHED_SUCCESSFULLY('Collaborators'),
          HttpStatus.OK,
          {
            totalResults: 0,
            currentResults: 0,
            totalPages: 0,
            currentPage: Number(page) || 1,
          },
        );
      }

      let matchObj = {
        _id: { 
          $in: uniqueUserIds.map(id => new mongoose.Types.ObjectId(id)),
          $ne: loggedInUser._id 
        },
        iAmMember: { $ne: iAmMemberEnum.AUDIENCE_MEMBER_FAN }, //Audience Members/Fans can not collaborate
        isFakeAccount: false,
      };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      const collaborators = await this.userModel.aggregate([
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'clientinfos',
            localField: '_id',
            foreignField: 'clientId',
            as: 'clientInfo',
            pipeline: [
              {
                $match: {
                  userId: loggedInUser._id,
                  status: StatusEnum.ACCEPT,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$clientInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            isClientAdded: {
              $cond: {
                if: { $ifNull: ['$clientInfo', false] },
                then: true,
                else: false,
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            firstName: 1,
            lastName: 1,
            userName: 1,
            isClientAdded: 1,
            profileImage: 1,
            businessOrganizationName: 1,
            followers: 1,
            following: 1,
          },
        },
        {
          $sort: sortObj,
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
      ]);

      // Get user count query
      const collaboratorsCount = await this.userModel.countDocuments(matchObj);

      // Pagination parameters
      const totalResults = collaboratorsCount;
      const currentResults = collaborators?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        collaborators,
        CONSTANT.FETCHED_SUCCESSFULLY('Collaborators'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async postCollaboratorsList(req, postData) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;
      const { type } = postData;

      const searchFields = [
        `${type}.userName`,
        `${type}.firstName`,
        `${type}.lastName`,
        `${type}.businessOrganizationName`,
      ];

      const matchObj = {
        _id: new mongoose.Types.ObjectId(postData.postId),
      };

      //Check for Post Exist or not
      const post = await this.postModel.findOne(matchObj);
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      const pipelineArr: PipelineStage[] = [
        {
          $match: matchObj,
        },
        ...(type === 'collaborators'
          ? [
              {
                $addFields: {
                  collaborators: {
                    $filter: {
                      input: '$collaborators',
                      as: 'collab',
                      cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
                    },
                  },
                },
              },
            ]
          : []),
        {
          $lookup: {
            from: 'users',
            localField: type === 'collaborators' ? 'collaborators.id' : type,
            foreignField: '_id',
            as: type,
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: `$${type}`,
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $group: {
            _id: '$_id',
            [type]: { $push: `$${type}` },
          },
        },
        {
          $addFields: {
            [type]: {
              $slice: [
                `$${type}`,
                Number(page - 1) * Number(perPage),
                Number(perPage),
              ],
            },
          },
        },
      ];

      if (Object.keys(searchFilterObj).length)
        pipelineArr.splice(3, 0, { $match: searchFilterObj });

      if (Object.keys(sortObj).length) pipelineArr.push({ $sort: sortObj });

      const resultData = await this.postModel.aggregate(pipelineArr);
      let resultCount: any;
      if (Object.keys(searchFilterObj).length) {
        resultCount = await this.postModel.aggregate(pipelineArr.slice(0, 5));
      } else {
        resultCount = await this.postModel.aggregate(pipelineArr.slice(0, 4));
      }

      // Pagination parameters
      const totalResults = resultCount[0]?.[type]?.length || 0;
      const currentResults = resultCount[0]?.[type]?.length || 0;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        resultData[0]?.[type] || [],
        CONSTANT.FETCHED_SUCCESSFULLY(`${type} list`),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async rePostUsers(req, postData) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      const searchFields = [
        `userName`,
        `firstName`,
        `lastName`,
        `businessOrganizationName`,
      ];

      const matchObj = {
        _id: new mongoose.Types.ObjectId(postData.postId),
      };

      const pipelineMatch = {
        post: new mongoose.Types.ObjectId(postData.postId),
      };

      //Check for Post Exist or not
      const post = await this.postModel.findOne(matchObj);
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      const pipelineArr: PipelineStage[] = [
        {
          $match: pipelineMatch,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'repostBy',
            foreignField: '_id',
            as: 'repostBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: `$repostBy`,
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: '$repostBy._id',
            firstName: '$repostBy.firstName',
            lastName: '$repostBy.lastName',
            profileImage: '$repostBy.profileImage',
            userName: '$repostBy.userName',
            businessOrganizationName: '$repostBy.businessOrganizationName',
          },
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
      ];

      if (Object.keys(searchFilterObj).length)
        pipelineArr.splice(4, 0, { $match: searchFilterObj });

      if (Object.keys(sortObj).length) pipelineArr.push({ $sort: sortObj });

      // Execute aggregation pipeline
      const resultData = await this.postModel.aggregate(pipelineArr);

      const resultCount = await this.postModel.aggregate([
        ...pipelineArr.slice(
          0,
          Object.keys(sortObj).length
            ? pipelineArr.length - 3
            : pipelineArr.length - 2,
        ), // Remove $sort, $skip, and $limit for count
        { $count: 'totalCount' },
      ]);

      // Pagination parameters
      const totalResults = resultCount[0]?.totalCount || 0;
      const currentResults = resultData?.length || 0;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        resultData || [],
        CONSTANT.FETCHED_SUCCESSFULLY(`Repost User list`),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async postLikes(req, postId) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      // Search fields
      const searchFields = ['user.firstName', 'user.lastName', 'user.userName'];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };
      const matchObj = {
        postId: new mongoose.Types.ObjectId(postId),
      };

      //Check For Post Exist Or Not
      const post = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postId),
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      // Create Search, Sort and Pagination
      const { searchObj, filterObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      const pipelineArr: any = [
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$user',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 1,
            postId: 1,
            userId: 1,
            createdAt: 1,
            updatedAt: 1,
            'user.firstName': 1,
            'user.lastName': 1,
            'user.userName': 1,
            'user.profileImage': 1,
          },
        },
      ];

      if (Object.keys(searchFilterObj).length)
        pipelineArr.push({ $match: searchFilterObj });

      if (Object.keys(sortObj).length) pipelineArr.push({ $sort: sortObj });

      if (skipData || skipData === 0) pipelineArr.push({ $skip: skipData });
      if (limitData || limitData === 0) pipelineArr.push({ $limit: limitData });

      // Get forms data query
      const likesData = await this.likeModel.aggregate(pipelineArr);

      let likesCount: any;
      if (Object.keys(searchFilterObj).length) {
        likesCount = await this.likeModel.aggregate([pipelineArr.slice(0, 4)]);
      } else {
        likesCount = await this.likeModel.aggregate([pipelineArr.slice(0, 3)]);
      }

      // Pagination parameters
      const totalResults = likesCount.length;
      const currentResults = likesData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };
      return successResponse(
        likesData,
        CONSTANT.FETCHED_SUCCESSFULLY('Post likes'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async postReactions(req, postId) {
    try {
      const { page, perPage, sort, sortBy, search, type } = req.query;

      // Search fields
      const searchFields = ['user.firstName', 'user.lastName', 'user.userName'];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };
      const matchObj = {
        _id: new mongoose.Types.ObjectId(postId),
      };

      //Check For Post Exist Or Not
      const post = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postId),
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      // Create Search, Sort and Pagination
      const { searchObj, filterObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      const pipelineArr: any = [
        {
          $match: matchObj,
        },
        {
          $unwind: {
            path: '$reactions',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            'reactions.type': type,
          },
        },

        {
          $lookup: {
            from: 'users',
            localField: 'reactions.userId',
            foreignField: '_id',
            as: 'user',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$user',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 1,
            user: 1,
            reactions: 1,
            reactionCounts: 1,
          },
        },
      ];

      if (type == 'all') {
        pipelineArr.splice(2, 1);
      }
      if (Object.keys(searchFilterObj).length)
        pipelineArr.push({ $match: searchFilterObj });

      if (Object.keys(sortObj).length) pipelineArr.push({ $sort: sortObj });

      if (skipData || skipData === 0) pipelineArr.push({ $skip: skipData });
      if (limitData || limitData === 0) pipelineArr.push({ $limit: limitData });

      // Get forms data query
      const reactionsData = await this.postModel.aggregate(pipelineArr);

      let reactionsCount: any;
      if (Object.keys(searchFilterObj).length) {
        const lastPipelineCount = type === 'all' ? 6 : 7;
        reactionsCount = await this.postModel.aggregate(
          pipelineArr.slice(0, lastPipelineCount),
        );
      } else {
        const lastPipelineCount = type === 'all' ? 5 : 6;
        reactionsCount = await this.postModel.aggregate(
          pipelineArr.slice(0, lastPipelineCount),
        );
      }

      // Pagination parameters
      const totalResults = reactionsCount.length;
      const currentResults = reactionsData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };
      return successResponse(
        reactionsData,
        CONSTANT.FETCHED_SUCCESSFULLY('Post likes'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async postComments(req, postId) {
    try {
      const { user: loggedInUser } = req;

      const { page, perPage, sort, sortBy, search } = req.query;

      // Search fields
      const searchFields = [
        'user.firstName',
        'user.lastName',
        'user.userName',
        'user.businessOrganizationName',
      ];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };
      const matchObj = {
        postId: new mongoose.Types.ObjectId(postId),
        parentId: null,
      };

      //Check Post Exist or Not
      const post = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postId),
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      // Create Search, Sort and Pagination
      const { searchObj, filterObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      const pipelineArr: any = [
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$user',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            taggedUsers: { $ifNull: ['$taggedUsers', []] },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedUsers.user',
            foreignField: '_id',
            as: 'taggedUserDetails',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            'taggedUsers.user': {
              $arrayElemAt: ['$taggedUserDetails', 0],
            },
          },
        },
        {
          $unset: 'taggedUserDetails',
        },
        {
          $addFields: {
            taggedUsers: {
              $cond: {
                if: {
                  $eq: [
                    {
                      $size: '$taggedUsers',
                    },
                    0,
                  ],
                },
                then: [],
                else: '$taggedUsers',
              },
            },
            isLike: {
              $cond: {
                if: {
                  $in: [
                    mongoose.Types.ObjectId.createFromHexString(
                      loggedInUser._id.toString(),
                    ),
                    { $ifNull: ['$reactions.userId', []] },
                  ],
                },
                then: true,
                else: false,
              },
            },
            currentUserReaction: {
              $let: {
                vars: {
                  userReaction: {
                    $filter: {
                      input: '$reactions',
                      as: 'reaction',
                      cond: {
                        $eq: [
                          '$$reaction.userId',
                          mongoose.Types.ObjectId.createFromHexString(
                            loggedInUser._id.toString(),
                          ),
                        ],
                      },
                    },
                  },
                },
                in: {
                  $cond: {
                    if: {
                      $gt: [
                        {
                          $size: { $ifNull: ['$$userReaction', []] },
                        },
                        0,
                      ],
                    },
                    then: {
                      $arrayElemAt: ['$$userReaction.type', 0],
                    },
                    else: null,
                  },
                },
              },
            },
          },
        },
        {
          $unset: 'reactions',
        },
        {
          $lookup: {
            from: 'comments',
            let: { parentId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$parentId', '$$parentId'] },
                },
              },
              {
                $lookup: {
                  from: 'users',
                  let: { replyUserId: '$userId' },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $eq: ['$_id', { $toObjectId: '$$replyUserId' }],
                        },
                      },
                    },
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        userName: 1,
                        profileImage: 1,
                        businessOrganizationName: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                  as: 'user',
                },
              },
              {
                $addFields: {
                  user: { $arrayElemAt: ['$user', 0] },
                },
              },
              {
                $lookup: {
                  from: 'users',
                  let: { taggedUserId: '$taggedUsers.user' }, // Pass the tagged user's ID
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $in: ['$_id', '$$taggedUserId'], // Match the user ID with taggedUsers.user
                        },
                      },
                    },
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        userName: 1,
                        profileImage: 1,
                        businessOrganizationName: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                  as: 'taggedUserDetails', // Output array of matched users
                },
              },
              {
                $addFields: {
                  'taggedUsers.user': {
                    $arrayElemAt: ['$taggedUserDetails', 0],
                  },
                },
              },
              {
                $project: {
                  userId: 0,
                  taggedUserDetails: 0,
                },
              },
            ],
            as: 'replies',
          },
        },
        {
          $addFields: {
            replies: {
              $map: {
                input: '$replies',
                as: 'reply',
                in: {
                  $mergeObjects: [
                    '$$reply',
                    {
                      isLike: {
                        $cond: {
                          if: {
                            $in: [
                              mongoose.Types.ObjectId.createFromHexString(
                                loggedInUser._id.toString(),
                              ),
                              { $ifNull: ['$$reply.reactions.userId', []] },
                            ],
                          },
                          then: true,
                          else: false,
                        },
                      },
                      currentUserReaction: {
                        $let: {
                          vars: {
                            userReaction: {
                              $filter: {
                                input: '$$reply.reactions',
                                as: 'reaction',
                                cond: {
                                  $eq: [
                                    '$$reaction.userId',
                                    mongoose.Types.ObjectId.createFromHexString(
                                      loggedInUser._id.toString(),
                                    ),
                                  ],
                                },
                              },
                            },
                          },
                          in: {
                            $cond: {
                              if: {
                                $gt: [
                                  {
                                    $size: { $ifNull: ['$$userReaction', []] },
                                  },
                                  0,
                                ],
                              },
                              then: {
                                $arrayElemAt: ['$$userReaction.type', 0],
                              },
                              else: null,
                            },
                          },
                        },
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $unset: 'replies.reactions',
        },
        {
          $project: {
            _id: 1,
            postId: 1,
            comment: 1,
            taggedUsers: {
              user: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
            isLike: 1,
            currentUserReaction: 1,
            totalReactions: 1,
            reactionCounts: 1,
            isEdited: 1,
            createdAt: 1,
            updatedAt: 1,
            replies: 1,
            'user._id': 1,
            'user.firstName': 1,
            'user.lastName': 1,
            'user.userName': 1,
            'user.businessOrganizationName': 1,
            'user.profileImage': 1,
            'user.followers': 1,
            'user.following': 1,
            'user.connections': 1,
            'user.accountVerified': 1,
            'user.iAmMember': 1,
            'user.professions': 1,
            'user.isFakeAccount': 1,
          },
        },
      ];

      if (Object.keys(searchFilterObj).length)
        pipelineArr.push({ $match: searchFilterObj });

      if (Object.keys(sortObj).length) pipelineArr.push({ $sort: sortObj });

      if (skipData || skipData === 0) pipelineArr.push({ $skip: skipData });
      if (limitData || limitData === 0) pipelineArr.push({ $limit: limitData });

      // Get forms data query
      const commentsData = await this.commentModel.aggregate(pipelineArr);

      let commentCount: any;
      if (Object.keys(searchFilterObj).length) {
        commentCount = await this.commentModel.aggregate(
          pipelineArr.slice(0, 4),
        );
      } else {
        commentCount = await this.commentModel.aggregate(
          pipelineArr.slice(0, 3),
        );
      }

      // Pagination parameters
      const totalResults = commentCount.length;
      const currentResults = commentsData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        commentsData,
        CONSTANT.FETCHED_SUCCESSFULLY('Post comments'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async bookmarkPost(req, postData) {
    try {
      const { user: loggedInUser } = req;

      const post = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postData.postId),
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const bookmarkExist = await this.bookmarkModel.findOne({
        postId: post._id,
        userId: loggedInUser._id,
      });
      if (bookmarkExist && postData?.isBookmark === true) {
        throw new HttpException(
          CONSTANT.ALREADY_EXIST('Bookmark'),
          HttpStatus.NOT_FOUND,
        );
      }

      let successMessage;
      if (bookmarkExist && postData?.isBookmark === false) {
        await this.bookmarkModel.deleteOne({ _id: bookmarkExist._id });

        await this.postModel.findByIdAndUpdate(post._id, {
          totalBookmarks: post.totalBookmarks ? post.totalBookmarks - 1 : 0,
        });
        successMessage = CONSTANT.BOOKMARK_REMOVE;
      }

      if (!bookmarkExist && postData?.isBookmark === true) {
        await new this.bookmarkModel({
          postId: post._id,
          userId: loggedInUser._id,
        }).save();
        await this.postModel.findByIdAndUpdate(
          post._id,
          {
            totalBookmarks: post.totalBookmarks + 1,
          },
          { new: true },
        );
        successMessage = CONSTANT.ADDED_SUCCESSFULLY('Bookmark');
      }

      return successResponse(null, successMessage, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async viewBookmarks(req) {
    try {
      const { user: loggedInUser } = req;

      const queryData: ParsedQs = req.query;
      let page = Number(queryData.page);
      let perPage = Number(queryData.perPage);

      if (!page) page = 1;
      if (!perPage) perPage = 10;

      const bookmark: any = await this.bookmarkModel.aggregate([
        {
          $match: {
            userId: loggedInUser._id,
            postId: { $exists: true },
          },
        },
        {
          $lookup: {
            from: 'posts',
            localField: 'postId',
            foreignField: '_id',
            as: 'postId',
            pipeline: [
              {
                $lookup: {
                  from: 'users',
                  localField: 'userId',
                  foreignField: '_id',
                  as: 'userId',
                  pipeline: [
                    {
                      $match: { isFakeAccount: false },
                    },
                    {
                      $project: {
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $unwind: {
                  path: '$userId',
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $addFields: {
                  collaborators: {
                    $filter: {
                      input: '$collaborators',
                      as: 'collab',
                      cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
                    },
                  },
                },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'collaborators.id',
                  foreignField: '_id',
                  as: 'collaborators',
                  pipeline: [
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'fundraisers',
                  foreignField: '_id',
                  as: 'fundraisers',
                  pipeline: [
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'taggedPeople',
                  foreignField: '_id',
                  as: 'taggedPeople',
                  pipeline: [
                    {
                      $match: { isFakeAccount: false },
                    },
                    {
                      $project: {
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $lookup: {
                  from: 'groups',
                  localField: 'group',
                  foreignField: '_id',
                  as: 'group',
                  pipeline: [
                    {
                      $project: {
                        name: 1,
                      },
                    },
                  ],
                },
              },
              {
                $unwind: {
                  path: '$group',
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'repostBy',
                  foreignField: '_id',
                  as: 'repostBy',
                  pipeline: [
                    {
                      $match: { isFakeAccount: false },
                    },
                    {
                      $project: {
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $unwind: {
                  path: '$repostBy',
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $project: {
                  __v: 0,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$postId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $sort: { createdAt: -1 },
        },
        {
          $skip: (page - 1) * perPage,
        },
        {
          $limit: perPage,
        },
      ]);

      const bookmarks = bookmark.map((item: any) => item.postId);

      const bookmarks_data = await Promise.all(
        bookmarks.map(async (post: any) => {
          if (!post) return null;
          // If post is a Mongoose document, use .toObject(), else fallback to post itself
          const bookmarksData = post._doc ? { ...post._doc } : { ...post };
          bookmarksData.isBookmark = true;

          // Check if the logged-in user has reacted to the post
          const isLike = Array.isArray(post.reactions)
            ? post.reactions.some(
                (reaction: any) =>
                  reaction.userId.toString() === loggedInUser._id.toString(),
              )
            : false;

          // Get the currentUserReaction based on the logged-in user's reaction type
          const userReaction = Array.isArray(post.reactions)
            ? post.reactions.find(
                (reaction: any) =>
                  reaction.userId.toString() === loggedInUser._id.toString(),
              )
            : null;
          const currentUserReaction = userReaction ? userReaction.type : null;

          bookmarksData.isLike = isLike;
          bookmarksData.currentUserReaction = currentUserReaction;

          return bookmarksData;
        }),
      );

      const bookmarkCount: any = await this.bookmarkModel
        .find({
          userId: loggedInUser._id,
          postId: { $exists: true },
        })
        .countDocuments();

      const totalResults: any = bookmarkCount;
      const currentResults = bookmarks.length;
      let totalPages = Math.ceil(totalResults / perPage);
      totalPages = totalPages == 0 ? 1 : totalPages;
      const currentPage = page;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        bookmarks_data,
        CONSTANT.FETCHED_SUCCESSFULLY('Bookmark'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getStories(req) {
    try {
      const { user: loggedInUser } = req;
      const { page, perPage = 10 } = req.query;
      const skipData = Number((page - 1) * perPage);
      const pipelineArr: any = [
        {
          $addFields: {
            isLike: {
              $cond: {
                if: {
                  $in: [loggedInUser._id, '$likeUsers'],
                },
                then: true,
                else: false,
              },
            },
            isLoggedInUser: {
              $cond: {
                if: {
                  $eq: ['$userId', loggedInUser._id],
                },
                then: true,
                else: false,
              },
            },
          },
        },
        {
          $group: {
            _id: '$userId',
            media: {
              $push: {
                _id: '$_id',
                mediaType: '$mediaType',
                url: '$url',
                thumbUrl: '$thumbUrl',
                createdAt: '$createdAt',
                updatedAt: '$updatedAt',
                shareableLink: '$shareableLink',
                isLike: '$isLike',
              },
            },
            isLoggedInUser: { $first: '$isLoggedInUser' },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$user',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'connectioninfos',
            let: {
              userId: '$user._id',
              loggedInUserId: loggedInUser._id,
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $or: [
                      {
                        $and: [
                          {
                            $eq: ['$$userId', '$userId'],
                          },
                          {
                            $eq: ['$connectionWithId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$status', connectionRequestStatus.ACCEPT],
                          },
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: ['$userId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$status', connectionRequestStatus.ACCEPT],
                          },
                          {
                            $eq: ['$$userId', '$connectionWithId'],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
            as: 'connectionInfo',
          },
        },
        {
          $unwind: {
            path: '$connectionInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'followerinfos',
            let: {
              userId: '$user._id',
              loggedInUserId: loggedInUser._id,
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $or: [
                      {
                        $and: [
                          {
                            $eq: ['$followerId', '$$userId'],
                          },
                          {
                            $eq: ['$followingId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$status', StatusEnum.ACCEPT],
                          },
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: ['$followerId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$followingId', '$$userId'],
                          },
                          {
                            $eq: ['$status', StatusEnum.ACCEPT],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
            as: 'followerInfo',
          },
        },
        {
          $unwind: {
            path: '$followerInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $or: [
              { connectionInfo: { $exists: true, $ne: null } },
              { followerInfo: { $exists: true, $ne: null } },
              { 'user._id': loggedInUser._id },
            ],
          },
        },

        {
          $sort: {
            isLoggedInUser: -1,
            _id: -1, // Default sorting logic
          },
        },
        {
          $project: {
            _id: 1,
            media: 1,
            userIdKey: '$user._id',
            firstName: '$user.firstName',
            lastName: '$user.lastName',
            userName: '$user.userName',
            profileImage: '$user.profileImage',
          },
        },
      ];
      if (skipData || skipData === 0) pipelineArr.push({ $skip: skipData });
      if (perPage || perPage === 0)
        pipelineArr.push({ $limit: Number(perPage) });

      const userStories = await this.storyModel.aggregate(pipelineArr);

      const countUserStories = await this.storyModel.aggregate(
        pipelineArr.slice(0, 5),
      );

      // Pagination parameters
      const totalResults = countUserStories?.length || 0;
      const currentResults = userStories?.length;
      const totalPages = Math.ceil(totalResults / perPage);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };
      const currentUser: any = await this.userModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(loggedInUser._id.toString()),
          },
        },
        {
          $unwind: {
            path: '$signUpData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'signUpData.itemId',
            foreignField: '_id',
            as: 'signUpDetails',
            // pipeline: [
            //   {
            //     $match: {
            //       title: 'who_can_message',
            //     },
            //   },
            // ],
          },
        },
        {
          $unwind: {
            path: '$signUpDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'signUpData.itemId': {
              _id: '$signUpDetails._id',
              title: '$signUpDetails.title',
              slug: '$signUpDetails.slug',
              itemText: '$signUpDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: '$_id',
            user: { $first: '$$ROOT' }, // grab first user fields
            signUpData: { $push: '$signUpData' }, // re-assemble signUpData array
          },
        },
        {
          $addFields: {
            'user.signUpData': '$signUpData',
          },
        },
        {
          $replaceRoot: {
            newRoot: '$user',
          },
        },
      ]);

      // Run whoCanMessageValidatorService.validate in parallel for all stories and add isMessageEnable to story.userId
      const formatedResponse = await Promise.all(
        userStories.map(async (story) => {
          // Get target user with signUpData
          const targetUserArr: any = await this.userModel.aggregate([
            {
              $match: {
                _id: new mongoose.Types.ObjectId(story.userIdKey),
              },
            },
            {
              $unwind: {
                path: '$signUpData',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'usersignupdatas',
                localField: 'signUpData.itemId',
                foreignField: '_id',
                as: 'signUpDetails',
              },
            },
            {
              $unwind: {
                path: '$signUpDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                'signUpData.itemId': {
                  _id: '$signUpDetails._id',
                  title: '$signUpDetails.title',
                  slug: '$signUpDetails.slug',
                  itemText: '$signUpDetails.itemText',
                },
              },
            },
            {
              $group: {
                _id: '$_id',
                user: { $first: '$$ROOT' },
                signUpData: { $push: '$signUpData' },
              },
            },
            {
              $addFields: {
                'user.signUpData': '$signUpData',
              },
            },
            {
              $replaceRoot: {
                newRoot: '$user',
              },
            },
          ]);
          const targetUser = targetUserArr[0];

          if (!targetUser) {
            throw new HttpException(
              { message: 'User not found' },
              HttpStatus.NOT_FOUND,
            );
          }

          // Validate messaging permission
          const { status } = await this.whoCanMessageValidatorService.validate(
            currentUser[0],
            targetUser,
          );

          return {
            ...story,
            isMessageEnable: status,
          };
        }),
      );

      return successResponse(
        formatedResponse,
        CONSTANT.FETCHED_SUCCESSFULLY('Stories'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async addStory(req, postData) {
    try {
      const { user: loggedInUser } = req;

      if (loggedInUser.iAmMember == iAmMemberEnum.AUDIENCE_MEMBER_FAN) {
        throw new HttpException(CONSTANT.STORY_NOT_ALLOW, HttpStatus.FORBIDDEN);
      }

      const { media } = postData;

      if (!media.length) {
        throw new HttpException(
          'Please add atleast one story',
          HttpStatus.FORBIDDEN,
        );
      }
      const storiesData = await Promise.all(
        media.map(async (data) => {
          const storyData = await this.storyModel.create({
            ...data,
            userId: loggedInUser._id,
          });

          const deepLink = await this.branchDeeplinkService.generateDeepLink(
            storyData._id.toString(),
            {
              title: loggedInUser.userName,
              description: loggedInUser.userName,
              image_url:
                storyData.mediaType === 'video'
                  ? storyData.thumbUrl
                  : storyData.url,
            },
          );

          return await this.storyModel.findByIdAndUpdate(
            storyData._id,
            {
              shareableLink: deepLink,
            },
            {
              new: true,
              runValidators: true,
            },
          );
        }),
      );

      return successResponse(
        { media: storiesData },
        CONSTANT.ADDED_SUCCESSFULLY('Story'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async likeUnlikeStory(req, likeUnlikeStoryData) {
    try {
      const { user: loggedInUser } = req;
      let successMessage;

      if (likeUnlikeStoryData.isLike) {
        const story = await this.storyModel.findByIdAndUpdate(
          {
            _id: new mongoose.Types.ObjectId(likeUnlikeStoryData.storyId),
            likeUsers: { $in: loggedInUser._id },
          },
          {
            $addToSet: { likeUsers: loggedInUser._id },
          },
        );
        successMessage = CONSTANT.LIKE_STORY;

        //Send Notification To mobile
        const notificationData = {
          enum: 'like',
          notificationType: NotificationsType.STORY_REACTION,
        };

        if (story?.userId.toString() !== loggedInUser._id.toString()) {
          const params = {
            title: notificationMessage(notificationData, null),
            notificationMessage: '',
            notificationType: NotificationsType.STORY_REACTION,
            redirectionType: RedirectionType.POST,
            receiver: story?.userId,
            storyId: story._id,
            sender: loggedInUser._id,
          };
          await this.notificationService.sendToTopic(null, params);
        }
      }

      if (!likeUnlikeStoryData.isLike) {
        const story = await this.storyModel.findByIdAndUpdate(
          {
            _id: new mongoose.Types.ObjectId(likeUnlikeStoryData.storyId),
            likeUsers: { $in: loggedInUser._id },
          },
          {
            $pull: { likeUsers: loggedInUser._id },
          },
        );
        successMessage = CONSTANT.UNLIKE_STORY;

        // delete notification
        await this.notificationModel.deleteMany({
          storyId: story._id,
          sender: loggedInUser._id,
          receiver: story?.userId,
        });
      }

      return successResponse(null, successMessage, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async deleteStory(req, storyId) {
    try {
      const { user: loggedInUser } = req;

      const story = await this.storyModel.findOne({
        _id: new mongoose.Types.ObjectId(storyId),
        userId: loggedInUser._id,
      });
      if (!story) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Story'),
          HttpStatus.NOT_FOUND,
        );
      }

      await this.storyModel.deleteOne(story._id);

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Story'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getPostById(req: any, postId: string) {
    try {
      const { user: loggedInUser } = req;

      const isEdit = req.query.isEdit;

      let post: any = await this.postModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(postId),
          },
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
            pipeline: [
              {
                $project: {
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$group',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$userId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'repostBy',
            foreignField: '_id',
            as: 'repostBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$repostBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedPeople',
            foreignField: '_id',
            as: 'taggedPeople',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        ...(isEdit == 'true'
          ? [
              {
                $lookup: {
                  from: 'users',
                  let: { collaborators: '$collaborators' },
                  localField: 'collaborators.id',
                  foreignField: '_id',
                  as: 'collaborators',
                  pipeline: [
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                        status: {
                          $first: {
                            $map: {
                              input: {
                                $filter: {
                                  input: '$$collaborators',
                                  as: 'collab',
                                  cond: { $eq: ['$$collab.id', '$_id'] },
                                },
                              },
                              as: 'matched',
                              in: '$$matched.status',
                            },
                          },
                        },
                      },
                    },
                  ],
                },
              },
            ]
          : [
              {
                $addFields: {
                  collaborators: {
                    $filter: {
                      input: '$collaborators',
                      as: 'collab',
                      cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
                    },
                  },
                },
              },
              {
                $lookup: {
                  from: 'users',
                  localField: 'collaborators.id',
                  foreignField: '_id',
                  as: 'collaborators',
                  pipeline: [
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
            ]),
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $project: {
            updatedAt: 0,
            __v: 0,
          },
        },
      ]);

      post = post[0]; // Extract the first (and only) document from the aggregation result

      // if (isEdit === 'true') {
      //   post.collaborators = post.collaborators?.user_details?.map((colab) => {
      //     return {
      //       ...colab,
      //       status: post.collaborators.status,
      //     };
      //   });
      // }

      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      const isBookmarkPost = await this.bookmarkModel.findOne({
        postId: post._id,
        userId: loggedInUser._id,
      });

      // Check if the logged-in user has reacted to the post (isLike)
      const isLike = post.reactions.some(
        (reaction) =>
          reaction.userId.toString() === loggedInUser._id.toString(),
      );

      // Get the currentUserReaction based on the logged-in user's reaction type
      const userReaction = post.reactions.find(
        (reaction) =>
          reaction.userId.toString() === loggedInUser._id.toString(),
      );
      const currentUserReaction = userReaction ? userReaction.type : null;

      post.isBookmark = isBookmarkPost ? true : false;
      post.isLike = isLike;
      post.currentUserReaction = currentUserReaction;

      return successResponse(
        post,
        CONSTANT.FETCHED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async deletePost(req, postId) {
    try {
      const post: any = await this.postModel.findOne({
        _id: new mongoose.Types.ObjectId(postId),
      });
      if (!post) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Post'),
          HttpStatus.NOT_FOUND,
        );
      }

      await this.postModel.deleteOne(post._id);

      await this.commentModel.deleteMany({ postId: post._id });

      await this.bookmarkModel.deleteMany({ postId: post._id });

      await this.userModel.updateOne(
        { _id: post.userId },
        { $inc: { posts: -1 } }, // Decrement post counts by 1
      );

      if (post.post) {
        await this.postModel.updateOne(
          { _id: post.post },
          { $inc: { repostCount: -1 } }, // Decrement repost counts by 1
        );
      }

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async userPosts(req, userId) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      const user = await this.userModel.findOne({
        _id: new mongoose.Types.ObjectId(userId),
        isFakeAccount: false,
      });
      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const searchFields = ['caption'];

      let matchObj = {
        userId: new mongoose.Types.ObjectId(userId),
        $or: [{ repostBy: { $exists: false } }, { repostBy: null }],
      };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      const pipelineArr: PipelineStage[] = [
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$userId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedPeople',
            foreignField: '_id',
            as: 'taggedPeople',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            collaborators: {
              $filter: {
                input: '$collaborators',
                as: 'collab',
                cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'collaborators.id',
            foreignField: '_id',
            as: 'collaborators',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'repostBy',
            foreignField: '_id',
            as: 'repostBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$repostBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
            pipeline: [
              {
                $project: {
                  name: 1,
                  privacy: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$group',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $or: [
              {
                $and: [
                  { group: { $exists: true } },
                  { 'group.privacy': Privacy.PUBLIC },
                ],
              },
              {
                group: { $exists: false },
              },
            ],
          },
        },
        {
          $sort: sortObj,
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
        {
          $project: {
            updatedAt: 0,
            __v: 0,
            group: 0,
          },
        },
      ];

      const postData = await this.postModel.aggregate(pipelineArr);

      // Get user count query
      const postCount = await this.postModel.countDocuments(matchObj);

      // Pagination parameters
      const totalResults = postCount;
      const currentResults = postData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        postData,
        CONSTANT.FETCHED_SUCCESSFULLY('Posts'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async userPodcasts(req, userId) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      const user = await this.userModel.findOne({
        _id: new mongoose.Types.ObjectId(userId),
        isFakeAccount: false,
      });
      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const searchFields = ['caption'];

      let matchObj = {
        userId: new mongoose.Types.ObjectId(userId),
        postLabel: PostLabelEnum.POD_CAST,
        $or: [{ repostBy: { $exists: false } }, { repostBy: null }],
      };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      const pipelineArr: PipelineStage[] = [
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$userId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedPeople',
            foreignField: '_id',
            as: 'taggedPeople',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            collaborators: {
              $filter: {
                input: '$collaborators',
                as: 'collab',
                cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'collaborators.id',
            foreignField: '_id',
            as: 'collaborators',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,

                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
            pipeline: [
              {
                $project: {
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$group',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            $or: [
              {
                $and: [
                  { group: { $exists: true } },
                  { 'group.privacy': Privacy.PUBLIC },
                ],
              },
              {
                group: { $exists: false },
              },
            ],
          },
        },
        {
          $sort: sortObj,
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
        {
          $project: {
            updatedAt: 0,
            __v: 0,
            group: 0,
            post: 0,
          },
        },
      ];

      const postData = await this.postModel.aggregate(pipelineArr);

      // Get user count query
      const postCount = await this.postModel.countDocuments(matchObj);

      // Pagination parameters
      const totalResults = postCount;
      const currentResults = postData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        postData,
        CONSTANT.FETCHED_SUCCESSFULLY('Posts'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async userComments(req, userId) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      const user = await this.userRepository.findById(userId);

      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const searchFields = ['comment'];

      let matchObj = {
        userId: new mongoose.Types.ObjectId(userId),
      };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      const commentData = await this.commentRepository.getAllUserComments(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      // Get user count query
      const commentCount = await this.commentRepository.countDocuments(
        matchObj,
      );

      // Pagination parameters
      const totalResults = commentCount;
      const currentResults = commentData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        commentData,
        CONSTANT.FETCHED_SUCCESSFULLY('Comments'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
  async userReposts(req, userId) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      const user = await this.userRepository.findById(userId);

      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const searchFields = ['title'];

      let matchObj = {
        repostBy: new mongoose.Types.ObjectId(userId),
      };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      const postData = await this.postRepository.getAllUserReposts(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      // Get user count query
      const postCount = await this.postRepository.countDocuments(matchObj);

      // Pagination parameters
      const totalResults = postCount;
      const currentResults = postData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        postData,
        CONSTANT.FETCHED_SUCCESSFULLY('Reposts'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async userPostedImages(req, userId) {
    try {
      const { page, perPage, sort, sortBy, search } = req.query;

      const user = await this.userRepository.findById(userId);

      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const searchFields = ['title'];

      let matchObj = {
        userId: new mongoose.Types.ObjectId(userId),
      };

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const searchFilterObj = {
        ...searchObj,
        ...filterObj,
      };

      matchObj = { ...matchObj, ...searchFilterObj };

      const postData = await this.postRepository.getAllPostedImages(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      // Get user count query
      const postCount = await this.postRepository.countDocuments(matchObj);

      // Pagination parameters
      const totalResults = postCount;
      const currentResults = postData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        postData,
        CONSTANT.FETCHED_SUCCESSFULLY('User posted images'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeCollaboratorsOrTagPeopleFromPost(
    userId: string,
    postId: string,
    notificationType: string,
  ) {
    try {
      // Find the post by ID
      const post = await this.postModel.findById(postId);
      if (!post) {
        throw new HttpException({ message: 'Post not found' }, 404);
      }

      // Remove collaborators if notificationType is 'collaborators'
      if (notificationType === 'collaborators' && post.collaborators) {
        post.collaborators = post.collaborators.filter(
          (collab: any) => collab.id.toString() !== userId.toString(),
        );
      }

      // Remove taggedPeople if notificationType is 'taggedPeople'
      if (notificationType === 'taggedPeople' && post.taggedPeople) {
        post.taggedPeople = post.taggedPeople.filter(
          (taggedId: any) => taggedId.toString() !== userId.toString(),
        );
      }

      // Save the updated post
      await post.save();

      return { message: 'Updated successfully', post };
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
