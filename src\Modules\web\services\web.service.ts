import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import CONSTANT from '../../../common/constant/common.constant';
import { BetaLeadDocument } from 'src/Models/beta_leads.schema';
import { ContactUsDocument } from 'src/Models/contactus.schema';
import { MailService, templates } from 'src/common/services/mail.service';
import { BetaLeadDto, ContactUsDto } from '../dtos/web.dtos';
import { WebBetaProgramSurveyEnum } from 'src/common/constant/enum';
import { TwillioService } from 'src/common/services/twillio.service';

@Injectable()
export class WebService {
  constructor(
    @InjectModel('BetaLead')
    private readonly leadModel: Model<BetaLeadDocument>,
    @InjectModel('ContactUs')
    private readonly contactUsModel: Model<ContactUsDocument>,
    private readonly mailService: MailService,
    private readonly twillioService: TwillioService,
  ) {}

  public async joinBetaProgram(data: BetaLeadDto) {
    try {
      // check phone has valid country code included
      if (!data.phone.startsWith('+')) {
        throw new HttpException(
          { message: 'Phone number must include country code' },
          HttpStatus.BAD_REQUEST,
        );
      }

      const isExist = await this.leadModel.findOne({
        $or: [{ email: data.email }, { phone: data.phone }],
      });

      if (!Object.keys(WebBetaProgramSurveyEnum).includes(data.type?.trim())) {
        throw new HttpException(
          { message: 'invalid survey type' },
          HttpStatus.BAD_REQUEST,
        );
      }

      if (isExist) {
        return successResponse(
          null,
          CONSTANT.ALREADY_JOINED('Beta program'),
          HttpStatus.OK,
        );
      }

      const surveyLink = WebBetaProgramSurveyEnum[data.type?.trim()];

      //send email to user
      await this.mailService.SendMail(
        data.email,
        'Beta Test Waitlist',
        templates.betaProgramTemplate({
          user_name: data.firstName?.trim(),
          surveyLink: surveyLink.link,
        }),
      );
      if (data.isMessagingEnable) {
        const message = `
      Welcome to Pepli, ${data.firstName?.trim()}!
      Thank you for successfully joining our beta tester group waitlist! Tap the link below to fill out our survey & get started.
      
      ${surveyLink.link}
      
      — Pepli Team`;

        await this.twillioService.sendMessageTwilio({
          message: message,
          phone: data.phone,
        });
      }

      await this.leadModel.create({
        ...data,
        type: WebBetaProgramSurveyEnum[data.type?.trim()],
        isMessagingEnable: data.isMessagingEnable,
      });

      return successResponse(
        null,
        CONSTANT.SUCCESSFULL('Beta program joined'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async contactUs(data: ContactUsDto) {
    try {
      await this.contactUsModel.create(data);
      const email = process.env.CONTACT_US_EMAIL || '<EMAIL>';

      //send email to user
      await this.mailService.SendMail(
        email,
        'Verification mail',
        templates.contactUsTemplate(data),
      );

      return successResponse(null, CONSTANT.CONTACT_US, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
