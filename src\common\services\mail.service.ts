import { HttpException, Injectable } from '@nestjs/common';
import Handlebars, { compile } from 'handlebars';
import fs from 'fs';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

// Register Handlebars helpers
Handlebars.registerHelper('eq', (a, b) => a == b);

// Utility to load and compile templates
const template = (file) => {
  return compile(fs.readFileSync(file, 'utf8'));
};

export const templates = {
  feedbackTemplate: template('src/Templates/feedback.html'),
  sendOtp: template('src/Templates/sendOtp.html'),
  forgotPassword: template('src/Templates/forgotPassword.html'),
  joinGroup: template('src/Templates/joinGroup.html'),
  betaProgramTemplate: template('src/Templates/betaProgram.html'),
  hirerVerificationTemplate: template('src/Templates/hirerVerification.html'),
  contactUsTemplate: template('src/Templates/contact.html'),
};

@Injectable()
export class MailService {
  private accessToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor(private readonly configService: ConfigService) {
    this.initializeToken();
  }

  private async initializeToken() {
    try {
      await this.refreshAccessToken();
      console.log('Microsoft 365 OAuth 2.0 initialized successfully');
    } catch (error) {
      console.error('Failed to initialize OAuth 2.0:', error);
      throw new Error('Failed to initialize Microsoft 365 OAuth 2.0');
    }
  }

  private async getAccessToken(): Promise<any> {
    try {
      const tenantId = this.configService.get<string>('MICROSOFT_TENANT_ID');
      const clientId = this.configService.get<string>('MICROSOFT_CLIENT_ID');
      const clientSecret = this.configService.get<string>('MICROSOFT_CLIENT_SECRET');

      if (!tenantId || !clientId || !clientSecret) {
        throw new Error('Missing required OAuth 2.0 configuration');
      }

      const tokenUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`;
      
      const response = await axios.post(tokenUrl, 
        new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: clientId,
          client_secret: clientSecret,
          scope: 'https://graph.microsoft.com/.default'
        }), 
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get access token:', error.response?.data || error.message);
      throw error;
    }
  }

  private async refreshAccessToken(): Promise<string> {
    try {
      console.log('Refreshing access token...');
      const tokenResponse = await this.getAccessToken();

      if (tokenResponse && tokenResponse.access_token) {
        this.accessToken = tokenResponse.access_token;
        this.tokenExpiry = Date.now() + (tokenResponse.expires_in * 1000);
        console.log('Access token refreshed successfully');
        return this.accessToken;
      } else {
        throw new Error('Invalid token response from Microsoft');
      }
    } catch (error) {
      console.error('Failed to refresh access token:', error);
      throw error;
    }
  }

  public async SendMail(
    to: string,
    subject: string,
    templateHtml: string,
    attachments?: Express.Multer.File[],
  ) {
    try {
      // Ensure we have a valid access token
      if (!this.accessToken || Date.now() >= this.tokenExpiry) {
        await this.refreshAccessToken();
      }

      const fromEmail = this.configService.get<string>('SMTP_MAIL');
      
      const emailMessage: any = {
        message: {
          subject: subject,
          body: {
            contentType: 'HTML',
            content: templateHtml,
          },
          toRecipients: [
            {
              emailAddress: {
                address: to,
              },
            },
          ],
          from: {
            emailAddress: {
              address: fromEmail,
            },
          },
        },
        saveToSentItems: true,
      };

      // Add attachments if provided
      if (attachments && attachments.length > 0) {
        emailMessage.message.attachments = attachments.map((att) => ({
          '@odata.type': '#microsoft.graph.fileAttachment',
          name: att.originalname,
          contentType: att.mimetype,
          contentBytes: att.buffer.toString('base64'),
        }));
      }

      const response = await axios.post(
        'https://graph.microsoft.com/v1.0/users/' + encodeURIComponent(fromEmail) + '/sendMail',
        emailMessage,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      console.log('Email sent successfully via Microsoft Graph API');
      
      return {
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        response: response.data,
      };

    } catch (error) {
      console.error('Email sending error:', error);
      
      // If it's an authentication error, try to refresh the token and retry once
      if (error.response?.status === 401 || error.message.includes('access token')) {
        try {
          console.log('Authentication error detected, refreshing token and retrying...');
          await this.refreshAccessToken();
          
          // Retry with the same logic
          return await this.SendMail(to, subject, templateHtml, attachments);
        } catch (retryError) {
          console.error('Retry failed:', retryError);
          throw new HttpException({ message: retryError.message }, retryError?.status || 500);
        }
      }
      
      // Handle 403 errors specifically
      if (error.response?.status === 403) {
        console.error('403 Forbidden - Permission denied. Check your Azure AD app permissions.');
        throw new HttpException({ 
          message: 'Permission denied. Please check your Azure AD application permissions and ensure Mail.Send permission is granted with admin consent.',
          details: error.response?.data 
        }, 403);
      }
      
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
