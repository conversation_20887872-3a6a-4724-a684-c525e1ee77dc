import { Module } from '@nestjs/common';
import { AdminController } from './controller/admin.controller';
import { AdminService } from './services/admin.service';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { RecordSchema } from 'src/Models/record.schema';
import { RecordJobSchema } from 'src/Models/recordJob.schema';
import { UserSignupDataSchema } from 'src/Models/userSignupData.schema';
import {
  MissingInformation,
  MissingInformationSchema,
} from 'src/Models/missinginformation.schema';
import { JwtStrategyService } from '../auth/jwt.strategy';
import { AuthService } from '../auth/services/auth.service';
import { User, UserSchema } from 'src/Models/user.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Record', schema: RecordSchema },
      { name: 'RecordJob', schema: RecordJobSchema },
      { name: 'UserSignupData', schema: UserSignupDataSchema },
      { name: MissingInformation.name, schema: MissingInformationSchema },
      { name: User.name, schema: UserSchema },
    ]),
    CommonModule,
  ],
  controllers: [AdminController],
  providers: [AdminService, AuthService, JwtStrategyService],
})
export class AdminModule {}
