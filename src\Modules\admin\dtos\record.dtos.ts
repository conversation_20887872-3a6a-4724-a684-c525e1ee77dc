import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>yNotEmpty,
  ArrayUnique,
  IsE<PERSON>,
  IsOptional,
  IsBoolean,
  IsNotEmpty,
} from 'class-validator';

export enum storeData {
  /**
   * Unions
   */
  AMERICAN_UNIONS = 'americanUnions',
  GLOBAL_UNIONS = 'globalUnions',
  /**
   * Affiliate Organizations
   */
  AMERICAN_AFFILLIATE_ORGANIZATION = 'americanAffiliateOrganization',
  GLOBAL_AFFILIATE_ORGANIZATION = 'globalAffiliateOrganization',
  NON_PROFIT_PHILANTHROPIC_INSTITUTION = 'nonProfitPhilanthropicInstitution',
  /**
   * Affiliate Businesses
   */
  AMERICAN_CATERING_CRAFTIES = 'americanCateringCrafties',
  ART_GALLERIES = 'artGalleries',
  CATERING_CRAFT_SERVICE_SGLOBALLY = 'cateringCraftServicesGlobally',
  MUSEUMS = 'museums',
  TALENT_REPS_AGENCIES_MANAGEMENT_COMPANIES = 'talentRepsAgenciesManagementCompanies',
  GLOBAL_TALENT_REPS_AGENCIES_MANAGEMENT_COMPANIES = 'globalTalentRepsAgenciesManagementCompanies',
  TALENT_REPS_AGENCIES_MANAGEMENT_COMPANIES_FOR_SPEAKERS = 'talentRepsAgenciesManagementCompaniesForSpeakers',
  ENTERTAINMENT_LAW_FIRMS = 'entertainmentLawFirms',
  GLOBAL_COMEDY_CLUBS = 'globalComedyClubs',
  AUDIO_PRODUCTION_RECORDING_STUDIOS = 'audioProductionRecordingStudios',
  AMERICAN_COMEDY_CLUBS = 'americanComedyClubs',
  GLOBAL_THEATERS = 'globalTheaters',
  AMERICAN_THEATERS = 'americanTheaters',
  CASTING = 'casting',
  GLOBAL_RECORDING_STUDIOS = 'globalRecordingStudios',
  LIFE_COACH_AGENCIES = 'lifeCoachAgencies',
  ANIMAL_ACTORS_AGENCIES_COMPANIES = 'animalActorsAgenciesCompanies',
  PUBLICATIONS = 'publications',
  INVESTMENT_ENTITIES = 'investmentEntities',
  PRODUCTION_ACQUISITION_DISTRIBUTION_COMPANIES = 'productionAcquisitionDistributionCompanies',
  /**
   * Schools / Training Facilities
   */
  SCHOOL_TRAINING_IN_ARTS_ENTERTAINMENT = 'schoolsTrainingInArtsEntertainment',
  VISUAL_ART = 'visualArt',
  PERFORMANING_ART = 'performingArt',
  DANCE = 'dance',
  ACTING = 'acting',
  MUSIC = 'music',
  FILM_MEDIA = 'filmMedia',
  DESIGN = 'design',
  LITERARY_ART = 'literaryArt',
  CRAFTS = 'crafts',
  APPLIED_ART = 'appliedArt',
  FIELD_OF_STUDY = 'fieldOfStudy',
  DEGREE = 'degree',
  OTHER = 'other',
  /**
   * Share Feedback Categories
   */
  FEEDBACK_CATEGORIES = 'feedbackCategories',

  // PRODUCTIONCOMPANIES = 'productionCompanies',
  // PROFESSION = 'profession',
  // PROJECTTYPEGENRE = 'projectTypeGenre',
  // DEGREE = 'degree',
  // PASSPORTCOUNTRIES = 'passportCountries',
  // SCHOOLSTRAININGINENTERTAINMENT = 'schoolsTrainingInEntertainment',
  // JOBLISTINGWORKSHOPSEVENTS = 'jobListingsWorkshopsEvents',
  // INTERESTS = 'interests',
  ETHNICITY = 'ethnicity',
  NATIONALITY = 'nationality',
  DISABILITY = 'disability',
  GENDER = 'gender',
  LANGUAGESSPOKENSIGNED = 'languagesSpokenSigned',
  PROFICIENCYLEVEL = 'proficiencyLevel',
  SELFIOTHER = 'selfIother',
  AGE = 'age',
  SELFIGENDER = 'selfIgender',
  CURRENCY = 'currency',
  SKILLS = 'skills',
  INDUSTRY = 'industry',
  ORGANIZATION_TYPE = 'organizationType',
  SPECIALIZATION = 'specialization',
  COMPANY_SIZE = 'companySize',
  PRONOUNS = 'pronouns',
  CAUSES = 'causes',
  PROFESSIONS = 'professions',
  POSITIONS = 'positions',
  FANDEGREE = 'fanDegree',
  SEXUAL_ORIENTATION = 'sexualOrientation',
  /**
   * JobList fields
   */
  // ROLETYPE = 'roleType',
  // PROJECTTYPE = 'projectType',
  // COMMERCIAL_BRANDED_CONTENT = 'commercialBrandedContent',
  // CONCERT = 'concert',
  // FASHION = 'fashion',
  // LITERATURE = 'literature',
  // MOVIE_FILM = 'movieFilm',
  // MUSEUM = 'museum',
  // MUSIC = 'music',
  // OPERA = 'opera',
  // HIPHOP = 'hipHop',
  // RAP = 'rap',
  // COUNTRY_MUSIC = 'countryMusic',
  // OTHER_DIGITAL_MEDIA = 'otherDigitalMedia',
  // RELIGIOUS_SPIRITUAL_MUSIC = 'religiousSpiritualMusic',
  // STAND_UP_COMEDY = 'standUpComedy',
  // TV_SHOW_SERIES = 'tVShowSeries',
  // THEATER = 'theater',
  // USER_GENERATED_CONTENT_UGC = 'userGeneratedContentUGC',
  // TYPE_OF_ORGANIZATION_YOU_WORKING_WITH_PROJECT = 'typeOfOrganizationYouAreWorkingWithForThisProject',
  // ADDITIONAL = 'additional',
}
export enum getRecordsData {
  UNION = 'union',
  AFFILIATE_ORGANIZATION = 'affiliateOrganization',
  AFFILIATE_BUSINESSES = 'affiliateBusinesses',
  SCHOOL_TRAINING_FACILITIES = 'schoolTrainingFacilities',
  PROFESSION = 'profession',
  VISUAL_ART = 'visualArt',
  PERFORMANING_ART = 'performingArt',
  DANCE = 'dance',
  ACTING = 'acting',
  MUSIC = 'music',
  FILM_MEDIA = 'filmMedia',
  DESIGN = 'design',
  LITERARY_ART = 'literaryArt',
  CRAFTS = 'crafts',
  APPLIED_ART = 'appliedArt',
  OTHER = 'other',
  NON_PROFIT_PHILANTHROPIC_INSTITUTION = 'nonProfitPhilanthropicInstitution',
  PROJECTTYPEGENRE = 'projectTypeGenre',
  DEGREE = 'degree',
  PASSPORTCOUNTRIES = 'passportCountries',
  PRODUCTIONCOMPANIES = 'productionCompanies',
  INTERESTS = 'interests',
  ETHNICITY = 'ethnicity',
  NATIONALITY = 'nationality',
  DISABILITY = 'disability',
  LANGUAGESSPOKENSIGNED = 'languagesSpokenSigned',
  PROFICIENCYLEVEL = 'proficiencyLevel',
  AGE = 'age',
  SELFIGENDER = 'selfIgender',
  SELFIOTHER = 'selfIother',

  SKILLS = 'skills',

  FIELD_OF_STUDY = 'fieldOfStudy',

  CURRENCY = 'currency',
  FEEDBACK_CATEGORIES = 'feedbackCategories',
  CAUSES = 'causes',
  INDUSTRY = 'industry',
  SEXUAL_ORIENTATION = 'sexualOrientation',
  ORGANIZATION_TYPE = 'organizationType',
  SPECIALIZATION = 'specialization',
  COMPANY_SIZE = 'companySize',
  PRONOUNS = 'pronouns',

  /**
   * JobList fields
   */
  ROLETYPE = 'roleType',
  PROJECTTYPE = 'projectType',
  COMMERCIAL_BRANDED_CONTENT = 'commercialBrandedContent',
  CONCERT = 'concert',
  FASHION = 'fashion',
  LITERATURE = 'literature',
  MOVIE_FILM = 'movieFilm',
  MUSEUM = 'museum',
  OPERA = 'opera',
  HIPHOP = 'hipHop',
  RAP = 'rap',
  COUNTRY_MUSIC = 'countryMusic',
  OTHER_DIGITAL_MEDIA = 'otherDigitalMedia',
  RELIGIOUS_SPIRITUAL_MUSIC = 'religiousSpiritualMusic',
  STAND_UP_COMEDY = 'standUpComedy',
  TV_SHOW_SERIES = 'tVShowSeries',
  THEATER = 'theater',
  USER_GENERATED_CONTENT_UGC = 'userGeneratedContentUGC',
  TYPE_OF_ORGANIZATION_YOU_WORKING_WITH_PROJECT = 'typeOfOrganizationYouAreWorkingWithForThisProject',
  ADDITIONAL = 'additional',
  PROFESSIONS = 'professions',
  POSITIONS = 'positions',
  FANDEGREE = 'fanDegree',
}
export class AreYouDataDto {
  @IsString()
  memberId: string;

  @IsBoolean()
  memberAs: boolean;
}

export class recordsDto {
  @IsBoolean()
  isJobRecords: boolean;

  @IsEnum(storeData)
  type: storeData;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsString({ each: true })
  data: string[];
}

export class getRecordsDto {
  @IsOptional()
  @IsArray()
  listOfType: string[];

  @IsOptional()
  @IsEnum(getRecordsData)
  type: getRecordsData;

  @IsOptional()
  @IsString()
  search: string;
}
