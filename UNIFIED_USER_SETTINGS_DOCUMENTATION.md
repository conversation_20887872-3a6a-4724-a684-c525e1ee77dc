# Unified User Settings Architecture

## Overview

This document outlines the new unified user settings architecture that consolidates notification permissions and other user settings into a scalable and efficient system, while keeping the block/unblock functionality separate for performance reasons.

## Architecture Design

### **Hybrid Collection Strategy**

We use a hybrid approach with three main collections:

1. **`usersettings`** - Unified user settings (notification permissions, privacy, preferences)
2. **`userblocks`** - Blocked users (separate for performance with large lists)
3. **`defaultsettings`** - Global default settings (admin-managed)

## Database Schema

### 1. UserSettings Collection
```javascript
// Collection: usersettings
{
  _id: ObjectId,
  userId: ObjectId, // Reference to User (unique)
  
  // Notification Permissions (Array of permission objects)
  notificationPermissions: {
    permissions: [
      {
        permissionId: ObjectId, // Reference to NotificationPermission
        isEnabled: Boolean
      }
    ]
  },
  
  // Privacy Settings (8 settings)
  privacySettings: {
    profileVisibility: "public" | "private" | "connections",
    showEmail: <PERSON><PERSON><PERSON>,
    showPhone: <PERSON><PERSON><PERSON>,
    showLocation: <PERSON><PERSON><PERSON>,
    showConnections: <PERSON><PERSON><PERSON>,
    showFollowers: <PERSON>olean,
    postVisibility: "public" | "private" | "connections",
    allowMessagesFrom: "all" | "connections" | "none"
  },
  
  // User Preferences (6 settings)
  preferences: {
    language: String,
    timezone: String,
    theme: "light" | "dark" | "auto",
    emailDigest: Boolean,
    digestFrequency: "daily" | "weekly" | "never",
    showOnlineStatus: Boolean
  },
  
  createdAt: Date,
  updatedAt: Date
}
```

### 2. UserBlock Collection (Separate - Unchanged)
```javascript
// Collection: userblocks (existing, unchanged)
{
  _id: ObjectId,
  blockerId: ObjectId, // User who is blocking
  blockedUserId: ObjectId, // User being blocked
  reason: String,
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### 3. DefaultSettings Collection
```javascript
// Collection: defaultsettings
{
  _id: ObjectId,
  category: "notificationPermissions" | "privacySettings" | "userPreferences" | "notificationPermissionDefaults",
  settings: Object, // Default settings for the category
  isActive: Boolean,
  description: String,
  permissionName: String, // For notification permissions
  userType: String, // For notification permissions
  createdAt: Date,
  updatedAt: Date
}
```

**Example Notification Permission Documents:**
```javascript
{
  category: "notificationPermissions",
  permissionName: "Message reminders",
  userType: "unionAffiliateMember",
  isActive: true,
  createdAt: "2024-01-15T10:30:00.000Z",
  updatedAt: "2024-01-15T10:30:00.000Z"
}

{
  category: "notificationPermissions",
  permissionName: "New messages in my inbox",
  userType: "union_1",
  isActive: true,
  createdAt: "2024-01-15T10:30:00.000Z",
  updatedAt: "2024-01-15T10:30:00.000Z"
}
```

## API Endpoints

### User Settings Management

#### 1. Get User Settings
```http
GET /user/settings
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "User Settings fetched successfully",
  "data": {
    "userSettings": {
      "userId": "64f8a1b2c3d4e5f6a7b8c9d0",
             "notificationPermissions": {
         "permissions": [
           {
             "permissionId": "68877d7d6daa29ee37d5bf1d",
             "isEnabled": true
           },
           {
             "permissionId": "688783bf6daa29ee37d5bf2d",
             "isEnabled": false
           }
         ]
       },
      "privacySettings": {
        "profileVisibility": "public",
        "showEmail": false,
        "showPhone": false,
        "showLocation": true,
        "showConnections": true,
        "showFollowers": true,
        "postVisibility": "public",
        "allowMessagesFrom": "connections"
      },
      "preferences": {
        "language": "en",
        "timezone": "UTC",
        "theme": "auto",
        "emailDigest": true,
        "digestFrequency": "weekly",
        "showOnlineStatus": true
      }
    }
  },
  "statusCode": 200
}
```

#### 2. Update Notification Permissions
```http
PUT /user/settings/notifications
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "permissions": [
    {
      "permissionId": "68877d7d6daa29ee37d5bf1d",
      "isEnabled": true
    },
    {
      "permissionId": "688783bf6daa29ee37d5bf2d",
      "isEnabled": false
    }
  ]
}
```

#### 3. Update Privacy Settings
```http
PUT /user/settings/privacy
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "profileVisibility": "connections",
  "showEmail": false,
  "showPhone": false,
  "showLocation": true,
  "showConnections": true,
  "showFollowers": false,
  "postVisibility": "private",
  "allowMessagesFrom": "connections"
}
```

#### 4. Update User Preferences
```http
PUT /user/settings/preferences
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "language": "es",
  "timezone": "America/New_York",
  "theme": "dark",
  "emailDigest": false,
  "digestFrequency": "never",
  "showOnlineStatus": false
}
```

#### 5. Update Multiple Settings
```http
PUT /user/settings
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "notificationPermissions": {
    "permissions": [
      {
        "permissionId": "68877d7d6daa29ee37d5bf1d",
        "isEnabled": false
      }
    ]
  },
  "privacySettings": {
    "profileVisibility": "private"
  },
  "preferences": {
    "theme": "dark",
    "language": "fr"
  }
}
```

#### 6. Reset to Defaults
```http
POST /user/settings/reset
Authorization: Bearer <jwt_token>
```

#### 7. Initialize Notification Permissions
```http
POST /user/settings/notifications/initialize
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Notification permissions initialized successfully",
  "data": {
    "notificationPermissions": {
      "permissions": [
        {
          "permissionId": "68877d7d6daa29ee37d5bf1d",
          "isEnabled": true
        },
        {
          "permissionId": "688783bf6daa29ee37d5bf2d",
          "isEnabled": true
        }
      ]
    }
  },
  "statusCode": 200
}
```

### Block/Unblock Users (Existing - Unchanged)

#### 1. Block/Unblock User
```http
POST /user/block-unblock
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "userId": "683d7a1c5020b02e398684fe",
  "reason": "Inappropriate behavior"
}
```

#### 2. Get Blocked Users
```http
GET /user/blocked-users?page=1&perPage=10
Authorization: Bearer <jwt_token>
```

## Migration Strategy

### Phase 1: Setup New Collections
1. Create `usersettings` and `defaultsettings` collections
2. Run migration script to populate default settings
3. Migrate existing notification permissions

### Phase 2: Migrate Existing Data
Run the migration script (`migrate_notification_permissions.js`):

```bash
# Option 1: Run in MongoDB shell
mongo your_database migrate_notification_permissions.js

# Option 2: Run as Node.js script
node migrate_notification_permissions.js
```

### Phase 3: Update Application Code
1. Update notification service to use new UserSettings
2. Keep existing block/unblock functionality unchanged
3. Test all endpoints thoroughly

### Phase 4: Deprecate Old Collections
1. Verify all data is migrated correctly
2. Update application to use new endpoints
3. Remove old notification permission collections

## Benefits of This Architecture

### 1. **Performance**
- **UserSettings**: Lightweight, single document per user
- **UserBlocks**: Separate collection for potentially large lists
- **Efficient Queries**: Indexed lookups for fast access

### 2. **Scalability**
- **Horizontal Growth**: Each collection can scale independently
- **Vertical Growth**: Easy to add new setting categories
- **Flexible Schema**: Mixed types for different setting types

### 3. **Maintainability**
- **Clear Separation**: Different concerns in different collections
- **Easy Updates**: Atomic updates for specific setting categories
- **Default Management**: Centralized default settings

### 4. **User Experience**
- **Lazy Initialization**: Settings created on first access
- **Default Values**: Automatic fallback to defaults
- **Partial Updates**: Update only what's needed

## Indexes and Performance

### UserSettings Indexes
```javascript
// Primary index for fast user lookups
db.usersettings.createIndex({ userId: 1 }, { unique: true });

// Compound index for settings queries
db.usersettings.createIndex({ 
  "notificationPermissions.email": 1,
  "privacySettings.profileVisibility": 1 
});
```

### UserBlock Indexes (Existing)
```javascript
// Compound index for unique blocker-blocked pairs
db.userblocks.createIndex({ blockerId: 1, blockedUserId: 1 }, { unique: true });

// Index for active blocks
db.userblocks.createIndex({ blockerId: 1, isActive: 1 });
```

### DefaultSettings Indexes
```javascript
// Compound index for category and active status
db.defaultsettings.createIndex({ category: 1, isActive: 1 });

// Index for notification permissions by user type
db.defaultsettings.createIndex({ category: 1, userType: 1 });
```

## Backward Compatibility

### Existing Notification Permission Service
The existing `NotificationPermissionService` and `NotificationPermissionController` remain unchanged to maintain backward compatibility during the transition period.

### Migration Helper Method
The `UserSettingsService` includes a `getNotificationPermissions()` method for backward compatibility:

```typescript
// Get notification permissions for a user (for backward compatibility)
async getNotificationPermissions(userId: string) {
  let userSettings = await this.userSettingsModel.findOne({ userId });
  
  if (!userSettings) {
    userSettings = await this.initializeUserSettings(userId);
  }
  
  return userSettings.notificationPermissions;
}
```

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Users can only access their own settings
3. **Validation**: All inputs validated using DTOs
4. **Data Isolation**: Settings are user-specific
5. **Audit Trail**: Timestamps track all changes

## Testing

### Test Scenarios

1. **New User**: Settings should be created automatically with defaults
2. **Existing User**: Settings should be migrated from old collections
3. **Partial Updates**: Only specified settings should be updated
4. **Defaults**: Reset should restore default values
5. **Validation**: Invalid values should be rejected
6. **Performance**: Large user bases should perform well

### Test Commands

```bash
# Test get user settings
curl -X GET "http://localhost:3000/user/settings" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test update notification permissions
curl -X PUT "http://localhost:3000/user/settings/notifications" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "permissions": [
      {
        "permissionId": "68877d7d6daa29ee37d5bf1d",
        "isEnabled": false
      },
      {
        "permissionId": "688783bf6daa29ee37d5bf2d",
        "isEnabled": true
      }
    ]
  }'

# Test reset to defaults
curl -X POST "http://localhost:3000/user/settings/reset" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Monitoring and Analytics

### Key Metrics
- Settings initialization rate
- Settings update frequency by category
- Default settings usage
- Performance metrics for each endpoint

### Health Checks
- Default settings availability
- Settings document size
- Index performance
- Query response times

This architecture provides a robust, scalable, and maintainable solution for managing user settings while maintaining excellent performance and user experience. The block/unblock functionality remains separate for optimal performance with large user lists. 