import { NotificationPermission, UserTypeEnum } from '../Models/notificationPermission.schema';

export const defaultNotificationPermissions = [
  // Audience Member/Fan permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.AUDIENCE_MEMBER_FAN,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.AUDIENCE_MEMBER_FAN,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.AUDIENCE_MEMBER_FAN,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.AUDIENCE_MEMBER_FAN,
  },
  {
    permissionName: 'Someone mentions me in a post',
    userType: UserTypeEnum.AUDIENCE_MEMBER_FAN,
  },

  // Creator Member permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'Someone mentions me in a post',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'Collaboration request',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'Job application received',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },
  {
    permissionName: 'New client request',
    userType: UserTypeEnum.CREATOR_MEMBER,
  },

  // Hirer/Employer permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.HIRER_EMPLOYER,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.HIRER_EMPLOYER,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.HIRER_EMPLOYER,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.HIRER_EMPLOYER,
  },
  {
    permissionName: 'Job application received',
    userType: UserTypeEnum.HIRER_EMPLOYER,
  },
  {
    permissionName: 'New member request',
    userType: UserTypeEnum.HIRER_EMPLOYER,
  },

  // Affiliate Organization permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.AFFILIATE_ORGANIZATION,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.AFFILIATE_ORGANIZATION,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.AFFILIATE_ORGANIZATION,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.AFFILIATE_ORGANIZATION,
  },
  {
    permissionName: 'New member request',
    userType: UserTypeEnum.AFFILIATE_ORGANIZATION,
  },
  {
    permissionName: 'Collaboration request',
    userType: UserTypeEnum.AFFILIATE_ORGANIZATION,
  },

  // Business School permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.BUSINESS_SCHOOL,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.BUSINESS_SCHOOL,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.BUSINESS_SCHOOL,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.BUSINESS_SCHOOL,
  },
  {
    permissionName: 'New student enrollment',
    userType: UserTypeEnum.BUSINESS_SCHOOL,
  },
  {
    permissionName: 'Collaboration request',
    userType: UserTypeEnum.BUSINESS_SCHOOL,
  },

  // Training Facility permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.TRAINING_FACILITY,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.TRAINING_FACILITY,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.TRAINING_FACILITY,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.TRAINING_FACILITY,
  },
  {
    permissionName: 'New student enrollment',
    userType: UserTypeEnum.TRAINING_FACILITY,
  },
  {
    permissionName: 'Collaboration request',
    userType: UserTypeEnum.TRAINING_FACILITY,
  },

  // Union permissions
  {
    permissionName: 'New messages in my inbox',
    userType: UserTypeEnum.UNION,
  },
  {
    permissionName: 'Someone follows me',
    userType: UserTypeEnum.UNION,
  },
  {
    permissionName: 'Someone likes my post',
    userType: UserTypeEnum.UNION,
  },
  {
    permissionName: 'Someone comments on my post',
    userType: UserTypeEnum.UNION,
  },
  {
    permissionName: 'New member request',
    userType: UserTypeEnum.UNION,
  },
  {
    permissionName: 'Collaboration request',
    userType: UserTypeEnum.UNION,
  },
]; 