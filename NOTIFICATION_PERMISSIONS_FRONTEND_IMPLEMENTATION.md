# Notification Permissions Frontend Implementation Guide

## Overview

This guide explains how to properly implement multi-selectable notification permissions with checkboxes in the frontend, ensuring that all selected values are properly saved to the backend.

## Key Requirements

1. **Multi-Selectable**: Users can select multiple notification permissions
2. **Checkbox Interface**: Each permission is represented as a checkbox
3. **Complete State Management**: All permission states are saved, not just enabled ones
4. **Real-time Updates**: UI reflects current state and updates immediately

## Backend API Endpoints

### 1. Get All Permissions
```
GET /api/v1/notification-permissions?userType=creator_member
```

**Response**:
```json
{
  "success": true,
  "data": {
    "userId": "507f1f77bcf86cd799439011",
    "userType": "creator_member",
    "totalPermissions": 13,
    "permissions": [
      {
        "permissionId": "507f1f77bcf86cd799439012",
        "permissionName": "New messages in my inbox",
        "userType": "creator_member",
        "isEnabled": true,
        "isActive": true
      },
      {
        "permissionId": "507f1f77bcf86cd799439013",
        "permissionName": "Someone follows me",
        "userType": "creator_member",
        "isEnabled": false,
        "isActive": true
      }
    ]
  }
}
```

### 2. Save Permissions
```
POST /api/v1/notification-permissions/save?userType=creator_member
```

**Request Body**:
```json
{
  "permissions": [
    {
      "permissionId": "507f1f77bcf86cd799439012",
      "isEnabled": true
    },
    {
      "permissionId": "507f1f77bcf86cd799439013",
      "isEnabled": false
    }
  ]
}
```

## Frontend Implementation

### 1. React Component Example

```jsx
import React, { useState, useEffect } from 'react';

const NotificationPermissions = ({ userType, authToken }) => {
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');

  // Load permissions on component mount
  useEffect(() => {
    loadPermissions();
  }, [userType]);

  const loadPermissions = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/v1/notification-permissions?userType=${userType}`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const result = await response.json();
      
      if (result.success) {
        setPermissions(result.data.permissions);
      } else {
        setMessage('Failed to load permissions');
      }
    } catch (error) {
      setMessage('Error loading permissions');
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = (permissionId, isEnabled) => {
    setPermissions(prev => 
      prev.map(permission => 
        permission.permissionId === permissionId 
          ? { ...permission, isEnabled }
          : permission
      )
    );
  };

  const handleSelectAll = () => {
    setPermissions(prev => 
      prev.map(permission => ({ ...permission, isEnabled: true }))
    );
  };

  const handleDeselectAll = () => {
    setPermissions(prev => 
      prev.map(permission => ({ ...permission, isEnabled: false }))
    );
  };

  const savePermissions = async () => {
    try {
      setSaving(true);
      setMessage('');

      // Prepare data with ALL permissions and their current states
      const permissionsToSave = permissions.map(permission => ({
        permissionId: permission.permissionId,
        isEnabled: permission.isEnabled
      }));

      const response = await fetch(
        `/api/v1/notification-permissions/save?userType=${userType}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            permissions: permissionsToSave
          })
        }
      );

      const result = await response.json();
      
      if (result.success) {
        setMessage('Preferences saved successfully!');
        // Update local state with server response
        if (result.data.permissions) {
          setPermissions(result.data.permissions);
        }
      } else {
        setMessage(result.message || 'Failed to save preferences');
      }
    } catch (error) {
      setMessage('Error saving preferences');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div>Loading permissions...</div>;
  }

  return (
    <div className="notification-permissions">
      <h2>Notification Preferences</h2>
      <p>Select which notifications you'd like to receive:</p>

      <div className="permissions-list">
        {permissions.map(permission => (
          <div key={permission.permissionId} className="permission-item">
            <label className="checkbox-container">
              <input
                type="checkbox"
                checked={permission.isEnabled}
                onChange={(e) => handlePermissionChange(permission.permissionId, e.target.checked)}
                className="permission-checkbox"
              />
              <span className="permission-name">{permission.permissionName}</span>
            </label>
          </div>
        ))}
      </div>

      <div className="actions">
        <button 
          onClick={savePermissions} 
          disabled={saving}
          className="btn btn-primary"
        >
          {saving ? 'Saving...' : 'Save Preferences'}
        </button>
        
        <button onClick={handleSelectAll} className="btn btn-secondary">
          Select All
        </button>
        
        <button onClick={handleDeselectAll} className="btn btn-secondary">
          Deselect All
        </button>
      </div>

      {message && (
        <div className={`message ${message.includes('success') ? 'success' : 'error'}`}>
          {message}
        </div>
      )}
    </div>
  );
};

export default NotificationPermissions;
```

### 2. Vanilla JavaScript Example

```javascript
class NotificationPermissionsManager {
  constructor(apiBaseUrl, authToken, userType) {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
    this.userType = userType;
    this.permissions = [];
    this.container = null;
  }

  async initialize(containerId) {
    this.container = document.getElementById(containerId);
    await this.loadPermissions();
    this.renderUI();
    this.attachEventListeners();
  }

  async loadPermissions() {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}/notification-permissions?userType=${this.userType}`,
        {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const result = await response.json();
      
      if (result.success) {
        this.permissions = result.data.permissions;
      }
    } catch (error) {
      console.error('Error loading permissions:', error);
    }
  }

  renderUI() {
    if (!this.container) return;

    const html = `
      <div class="notification-permissions-container">
        <h2>Notification Preferences</h2>
        <p>Select which notifications you'd like to receive:</p>
        
        <div class="permissions-list">
          ${this.permissions.map(permission => this.renderPermissionCheckbox(permission)).join('')}
        </div>

        <div class="actions">
          <button id="save-permissions" class="btn btn-primary">
            Save Preferences
          </button>
          <button id="select-all" class="btn btn-secondary">Select All</button>
          <button id="deselect-all" class="btn btn-secondary">Deselect All</button>
        </div>

        <div id="save-status" class="status-message"></div>
      </div>
    `;

    this.container.innerHTML = html;
  }

  renderPermissionCheckbox(permission) {
    const checked = permission.isEnabled ? 'checked' : '';
    return `
      <div class="permission-item">
        <label class="checkbox-container">
          <input 
            type="checkbox" 
            data-permission-id="${permission.permissionId}"
            ${checked}
            class="permission-checkbox"
          >
          <span class="permission-name">${permission.permissionName}</span>
        </label>
      </div>
    `;
  }

  attachEventListeners() {
    // Save button
    document.getElementById('save-permissions').addEventListener('click', () => {
      this.savePermissions();
    });

    // Select all button
    document.getElementById('select-all').addEventListener('click', () => {
      this.selectAllPermissions();
    });

    // Deselect all button
    document.getElementById('deselect-all').addEventListener('click', () => {
      this.deselectAllPermissions();
    });

    // Individual checkboxes
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        this.updatePermissionState(e.target.dataset.permissionId, e.target.checked);
      });
    });
  }

  updatePermissionState(permissionId, isEnabled) {
    const permission = this.permissions.find(p => p.permissionId === permissionId);
    if (permission) {
      permission.isEnabled = isEnabled;
    }
  }

  selectAllPermissions() {
    this.permissions.forEach(permission => {
      permission.isEnabled = true;
    });
    
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      checkbox.checked = true;
    });
  }

  deselectAllPermissions() {
    this.permissions.forEach(permission => {
      permission.isEnabled = false;
    });
    
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
      checkbox.checked = false;
    });
  }

  async savePermissions() {
    try {
      this.showStatus('Saving preferences...', 'info');
      
      // Prepare data with ALL permissions and their current states
      const permissionsToSave = this.permissions.map(permission => ({
        permissionId: permission.permissionId,
        isEnabled: permission.isEnabled
      }));

      const response = await fetch(
        `${this.apiBaseUrl}/notification-permissions/save?userType=${this.userType}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            permissions: permissionsToSave
          })
        }
      );

      const result = await response.json();
      
      if (result.success) {
        this.showStatus('Preferences saved successfully!', 'success');
        if (result.data.permissions) {
          this.permissions = result.data.permissions;
        }
      } else {
        this.showStatus(result.message || 'Failed to save preferences', 'error');
      }
    } catch (error) {
      this.showStatus('Error saving preferences', 'error');
    }
  }

  showStatus(message, type = 'info') {
    const statusElement = document.getElementById('save-status');
    if (statusElement) {
      statusElement.className = `status-message ${type}`;
      statusElement.textContent = message;
    }
  }
}
```

### 3. Vue.js Component Example

```vue
<template>
  <div class="notification-permissions">
    <h2>Notification Preferences</h2>
    <p>Select which notifications you'd like to receive:</p>

    <div class="permissions-list">
      <div 
        v-for="permission in permissions" 
        :key="permission.permissionId" 
        class="permission-item"
      >
        <label class="checkbox-container">
          <input
            type="checkbox"
            v-model="permission.isEnabled"
            class="permission-checkbox"
          />
          <span class="permission-name">{{ permission.permissionName }}</span>
        </label>
      </div>
    </div>

    <div class="actions">
      <button 
        @click="savePermissions" 
        :disabled="saving"
        class="btn btn-primary"
      >
        {{ saving ? 'Saving...' : 'Save Preferences' }}
      </button>
      
      <button @click="selectAll" class="btn btn-secondary">
        Select All
      </button>
      
      <button @click="deselectAll" class="btn btn-secondary">
        Deselect All
      </button>
    </div>

    <div v-if="message" :class="['message', messageType]">
      {{ message }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotificationPermissions',
  props: {
    userType: {
      type: String,
      required: true
    },
    authToken: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      permissions: [],
      loading: true,
      saving: false,
      message: '',
      messageType: 'info'
    };
  },
  async mounted() {
    await this.loadPermissions();
  },
  methods: {
    async loadPermissions() {
      try {
        this.loading = true;
        const response = await fetch(
          `/api/v1/notification-permissions?userType=${this.userType}`,
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Content-Type': 'application/json'
            }
          }
        );

        const result = await response.json();
        
        if (result.success) {
          this.permissions = result.data.permissions;
        } else {
          this.showMessage('Failed to load permissions', 'error');
        }
      } catch (error) {
        this.showMessage('Error loading permissions', 'error');
      } finally {
        this.loading = false;
      }
    },

    selectAll() {
      this.permissions.forEach(permission => {
        permission.isEnabled = true;
      });
    },

    deselectAll() {
      this.permissions.forEach(permission => {
        permission.isEnabled = false;
      });
    },

    async savePermissions() {
      try {
        this.saving = true;
        this.message = '';

        // Prepare data with ALL permissions and their current states
        const permissionsToSave = this.permissions.map(permission => ({
          permissionId: permission.permissionId,
          isEnabled: permission.isEnabled
        }));

        const response = await fetch(
          `/api/v1/notification-permissions/save?userType=${this.userType}`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              permissions: permissionsToSave
            })
          }
        );

        const result = await response.json();
        
        if (result.success) {
          this.showMessage('Preferences saved successfully!', 'success');
          if (result.data.permissions) {
            this.permissions = result.data.permissions;
          }
        } else {
          this.showMessage(result.message || 'Failed to save preferences', 'error');
        }
      } catch (error) {
        this.showMessage('Error saving preferences', 'error');
      } finally {
        this.saving = false;
      }
    },

    showMessage(message, type = 'info') {
      this.message = message;
      this.messageType = type;
    }
  }
};
</script>
```

## CSS Styles

```css
.notification-permissions {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.permissions-list {
  margin-bottom: 20px;
}

.permission-item {
  margin-bottom: 12px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.permission-checkbox {
  margin-right: 12px;
  transform: scale(1.2);
}

.permission-name {
  font-size: 14px;
  color: #333;
}

.actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.message {
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}
```

## Key Implementation Points

### 1. Complete State Management
- Always send ALL permissions in the save request, not just changed ones
- Include both enabled and disabled permissions
- Maintain complete state on both frontend and backend

### 2. Real-time Updates
- Update local state immediately when checkboxes change
- Provide visual feedback during save operations
- Show success/error messages to users

### 3. User Experience
- Provide "Select All" and "Deselect All" buttons
- Show loading states during API calls
- Validate data before sending to backend

### 4. Error Handling
- Handle network errors gracefully
- Show meaningful error messages
- Retry failed operations when appropriate

### 5. Data Validation
- Ensure all required fields are present
- Validate permission IDs against available permissions
- Handle edge cases (empty permissions array, etc.)

## Testing

### Manual Testing Checklist
- [ ] Load permissions correctly for different user types
- [ ] Checkboxes reflect current permission states
- [ ] Individual checkbox changes update local state
- [ ] Select All button enables all permissions
- [ ] Deselect All button disables all permissions
- [ ] Save button sends all permissions to backend
- [ ] Success message shows after successful save
- [ ] Error message shows after failed save
- [ ] UI updates with server response after save

### Automated Testing
```javascript
// Example test for React component
describe('NotificationPermissions', () => {
  it('should save all permission states when save button is clicked', async () => {
    const mockPermissions = [
      { permissionId: '1', permissionName: 'Test 1', isEnabled: true },
      { permissionId: '2', permissionName: 'Test 2', isEnabled: false }
    ];

    // Mock API responses
    fetch.mockResponseOnce(JSON.stringify({
      success: true,
      data: { permissions: mockPermissions }
    }));

    fetch.mockResponseOnce(JSON.stringify({
      success: true,
      data: { permissions: mockPermissions }
    }));

    render(<NotificationPermissions userType="creator_member" authToken="token" />);
    
    // Wait for permissions to load
    await waitFor(() => {
      expect(screen.getByText('Test 1')).toBeInTheDocument();
    });

    // Click save button
    fireEvent.click(screen.getByText('Save Preferences'));

    // Verify that all permissions were sent in the request
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/notification-permissions/save'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            permissions: mockPermissions
          })
        })
      );
    });
  });
});
```

## Troubleshooting

### Common Issues

1. **Only enabled permissions are saved**
   - Ensure you're sending ALL permissions in the save request
   - Check that the backend expects the complete permissions array

2. **Checkboxes don't reflect current state**
   - Verify that the load API returns the correct `isEnabled` values
   - Check that the checkbox `checked` property is properly bound

3. **Save operation fails**
   - Check network tab for API errors
   - Verify that all required fields are included in the request
   - Ensure the userType parameter is correct

4. **UI doesn't update after save**
   - Check that the save API returns the updated permissions
   - Verify that the frontend updates local state with the response

### Debug Tips

1. **Console Logging**
   ```javascript
   console.log('Current permissions:', permissions);
   console.log('Saving permissions:', permissionsToSave);
   ```

2. **Network Tab**
   - Check request payload and response
   - Verify all permissions are included in the save request

3. **State Inspection**
   - Use React DevTools or Vue DevTools to inspect component state
   - Verify that permission states are correctly maintained

## Conclusion

This implementation ensures that notification permissions are properly multi-selectable with checkboxes and that all selected values are correctly saved to the backend. The key is maintaining complete state management and sending all permissions (both enabled and disabled) in every save operation. 