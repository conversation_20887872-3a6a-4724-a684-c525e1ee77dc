import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notifications } from 'src/Models/notification.schema';
// import { ConfigService } from '@nestjs/config';
// import * as admin from 'firebase-admin';
// import { FcmToken } from './entities/fcm-token.entity';

@Injectable()
export class NotificationService {
  private readonly firebaseAdmin;

  constructor(
    @InjectModel(Notifications.name)
    private notificationModel: Model<Notifications>, // @InjectModel(FcmToken.name) // private fcmTokenModel: Model<FcmToken>, // private readonly configService: ConfigService,
  ) {
    // this.firebaseAdmin = admin.initializeApp({
    //   credential: admin.credential.cert({
    //     projectId: this.configService.get<string>('FIREBASE_PROJECT_ID'),
    //     clientEmail: this.configService.get<string>('FIREBASE_CLIENT_EMAIL'),
    //     privateKey: this.configService.get<string>('FIREBASE_PRIVATE_KEY'),
    //   }),
    //   databaseURL: 'https://shiplyy-d6ece.firebaseio.com',
    // });
  }

  async sendToTopic(message, params) {
    try {
      console.log('notification= ', message);
      if (message) {
        this.firebaseAdmin
          .messaging()
          .send(message)
          .then((res) => {
            console.log('Successfully sent notification= ', res);
            return res;
          })
          .catch((err) => {
            console.log(err);
          });
      }

      if (params) {
        await this.notificationModel.create(params);
      }

      // return;
    } catch (error) {
      console.log('error=', error);
    }
  }

  async sendMulticast(message, params) {
    try {
      console.log('notification= ', message);
      this.firebaseAdmin
        .messaging()
        .sendMulticast(message)
        .then(async (response: any) => {
          if (response.successCount) {
            if (params && params.length) {
              await this.notificationModel.insertMany(params);
            }
            console.log('Successfully sent notification= ', response);
          }

          const failedTokens: any = [];
          if (response.failureCount) {
            response.responses.forEach((resp, idx) => {
              if (!resp.success) {
                failedTokens.push(message.tokens[idx]);
                console.log(resp.error);
              }
            });
            console.log(`List of tokens that caused failures: ${failedTokens}`);
            return response;
          }

          return response;
        })
        .catch((error) => {
          console.log(error);
        });
    } catch (error) {
      console.log('error=', error);
    }
  }
}
