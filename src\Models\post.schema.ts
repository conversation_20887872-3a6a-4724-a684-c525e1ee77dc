import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { Group } from './group.schema';
import { User } from './user.schema';
import { InvitationType } from 'src/common/constant/enum';
import { StatusEnum } from './connectionInfo.schema';

export type postDocument = Post & Document;

export enum MediaEnum {
  IMAGE = 'image',
  VIDEO = 'video',
}

export enum PostLabelEnum {
  GENERAL_POST = 'generalPost',
  COLLAB = 'collab',
  AVAILABLE_GIG = 'availableGig',
  // JOB = 'job',
  DISCUSS = 'discuss',
  SUCCESS_CORNER = 'successCorner',
  INTERVIEW = 'interview',
  POD_CAST = 'podcast',
  POLL = 'poll',
}

export enum CommentorEnum {
  MY_CONNECTIONS = 'myConnections',
  FOLLOWERS = 'followers',
  FAN = 'fan',
}
export enum ReactionEnum {
  LIKE = 'like',
  LOVE = 'love',
  SUPPORT = 'support',
  INSIGHTFULL = 'insightFull',
  CURIOUS = 'curious',
  CLAP = 'clap',
  STAR = 'star',
  THUMB = 'thumb',
}
interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

//Who Can Comment
export enum WhoCanCommentEnum {
  MyConnections = 'myConnections',
  Followers = 'followers',
  Fans = 'fans',
}

@Schema({ timestamps: true, _id: false })
export class ReactionSchema {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: string;

  @Prop({
    type: String,
    enum: ReactionEnum,
  })
  type: ReactionEnum;
}

@Schema({ _id: false })
export class TaggedUsers {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  user: string;

  @Prop({
    type: String,
    trim: true,
    required: true,
  })
  username: string;
}

@Schema({ timestamps: false, _id: false })
export class ReactionCounts {
  @Prop({ type: Number, default: 0 })
  like: number;

  @Prop({ type: Number, default: 0 })
  love: number;

  @Prop({ type: Number, default: 0 })
  support: number;

  @Prop({ type: Number, default: 0 })
  insightFull: number;

  @Prop({ type: Number, default: 0 })
  curious: number;

  @Prop({ type: Number, default: 0 })
  clap: number;

  @Prop({ type: Number, default: 0 })
  star: number;

  @Prop({ type: Number, default: 0 })
  thumb: number;
}

class Collaborator {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  id: mongoose.Schema.Types.ObjectId;

  @Prop({ type: String, enum: StatusEnum })
  status: StatusEnum;
}

@Schema({ timestamps: true, versionKey: false })
export class Post {
  @Prop({ type: String })
  caption: string;

  @Prop({ type: String })
  title: string;

  @Prop({
    type: [TaggedUsers],
  })
  taggedCaptionUsers: TaggedUsers[];

  @Prop({ type: Boolean, default: false })
  isCaptionEdited: boolean;

  @Prop({ type: String })
  repostCaption: string;

  @Prop({
    type: [TaggedUsers],
  })
  repostTaggedCaptionUsers: TaggedUsers[];

  @Prop({ type: Boolean, default: false })
  isRepostCaptionEdited: boolean;

  @Prop({
    type: Object,
  })
  location: object;

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: String, trim: true })
  shareableLink: string;

  @Prop({
    type: [Collaborator],
  })
  collaborators: Collaborator[];

  @Prop({
    type: [String], // Indicating that this is an array of strings (enum values)
    enum: WhoCanCommentEnum, // Reference the enum
  })
  whoCanComment: WhoCanCommentEnum[];

  @Prop({
    type: Boolean,
    default: false,
  })
  isTurnOffComment: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  isHideLikeViews: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  isDisableRepostShare: boolean;

  @Prop({
    enum: PostLabelEnum,
  })
  postLabel: string;

  @Prop({
    type: [ReactionSchema],
  })
  reactions: [ReactionSchema];

  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  })
  taggedPeople: mongoose.Schema.Types.ObjectId[];

  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  })
  fundraisers: mongoose.Schema.Types.ObjectId[];

  @Prop({
    type: [String],
  })
  taggedProduct: string[];

  @Prop({
    type: [String],
  })
  aiLabels: string[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Schema.Types.ObjectId;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  repostBy: mongoose.Schema.Types.ObjectId;

  @Prop({ type: ReactionCounts, default: { ReactionCounts } })
  reactionCounts: ReactionCounts;

  @Prop({ type: Number, default: 0 })
  totalBookmarks: number;

  @Prop({ type: Number, default: 0 })
  totalReactions: number;

  @Prop({ type: Number, default: 0 })
  totalComments: number;

  @Prop({ type: Number, default: 0 })
  repostCount: number;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Post' })
  post: Post;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Group' })
  group: Group;
}

export const PostSchema = SchemaFactory.createForClass(Post);
