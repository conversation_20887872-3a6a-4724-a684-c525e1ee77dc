# Global Search API - Profile Visibility Enhancement

## Current Issue

The global search API currently **does NOT consider profile settings preferences**, which means:

1. **Privacy violations**: Users with "No One" profile discovery settings still appear in search results
2. **No respect for user preferences**: Profile visibility settings are ignored
3. **Inconsistent with platform privacy**: Search results don't align with user privacy choices

## Profile Visibility Settings Available

### Profile Discovery Options
- **"Everyone"** - Profile visible to everyone
- **"My Connections"** - Only visible to connections  
- **"People I Follow"** - Only visible to people they follow
- **"No One"** - Profile not discoverable

### Profile Viewing Options
- **"Your name headline"** - Show name and headline
- **"Private profile character"** - Hide profile details

## Proposed Enhancement

### 1. Add Profile Visibility Filtering

**For Accounts Search:**
```typescript
// Check user's profile discovery settings
const userSettings = await this.userSettingsModel.findOne({ 
  userId: targetUserId 
});

// Apply visibility rules
if (userSettings?.profileVisibility?.profileDiscoveryOptions) {
  const discoverySettings = userSettings.profileVisibility.profileDiscoveryOptions;
  
  // If "No One" is enabled, exclude from search
  const noOneEnabled = discoverySettings.find(s => s.Name === "No One")?.isEnabled;
  if (noOneEnabled) {
    return []; // User doesn't want to be discovered
  }
  
  // Check other visibility rules
  const everyoneEnabled = discoverySettings.find(s => s.Name === "Everyone")?.isEnabled;
  const connectionsEnabled = discoverySettings.find(s => s.Name === "My Connections")?.isEnabled;
  const followersEnabled = discoverySettings.find(s => s.Name === "People I Follow")?.isEnabled;
  
  // Apply filtering logic based on searcher's relationship
  const isConnection = await this.checkIfConnection(searcherId, targetUserId);
  const isFollower = await this.checkIfFollower(searcherId, targetUserId);
  
  if (!everyoneEnabled && !isConnection && !isFollower) {
    return []; // User not visible to searcher
  }
}
```

### 2. Enhanced Search Logic

**For All Search Types:**
```typescript
private async applyProfileVisibilityFilter(
  req: any, 
  searchResults: any[], 
  searchType: 'accounts' | 'posts' | 'podcasts' | 'jobs' | 'events' | 'union_organizations'
) {
  const searcherId = req.user.id;
  const filteredResults = [];
  
  for (const result of searchResults) {
    const targetUserId = result.userId || result._id;
    
    // Get target user's profile visibility settings
    const userSettings = await this.userSettingsModel.findOne({ 
      userId: targetUserId 
    });
    
    // Check if user should be visible to searcher
    const isVisible = await this.checkUserVisibility(
      searcherId, 
      targetUserId, 
      userSettings
    );
    
    if (isVisible) {
      // Apply profile viewing options (what data to show)
      const filteredResult = await this.applyProfileViewingOptions(
        result, 
        userSettings, 
        searcherId, 
        targetUserId
      );
      filteredResults.push(filteredResult);
    }
  }
  
  return filteredResults;
}
```

### 3. Profile Viewing Options Application

```typescript
private async applyProfileViewingOptions(
  result: any, 
  userSettings: any, 
  searcherId: string, 
  targetUserId: string
) {
  const viewingOptions = userSettings?.profileVisibility?.profileViewingOptions;
  
  if (viewingOptions) {
    const privateProfileEnabled = viewingOptions.find(
      s => s.Name === "Private profile character"
    )?.isEnabled;
    
    if (privateProfileEnabled) {
      // Hide sensitive profile information
      return {
        _id: result._id,
        firstName: result.firstName,
        lastName: result.lastName,
        userName: result.userName,
        profileImage: result.profileImage,
        // Hide: headline, position, industry, skills, etc.
      };
    }
  }
  
  return result;
}
```

## Implementation Plan

### Phase 1: Core Visibility Filtering
1. **Inject UserSettings Model** into UserService
2. **Add visibility check method** for each search type
3. **Update search methods** to apply visibility filters
4. **Test with different privacy settings**

### Phase 2: Profile Viewing Options
1. **Implement profile viewing logic** for accounts search
2. **Filter sensitive information** based on user preferences
3. **Maintain search relevance** while respecting privacy

### Phase 3: Performance Optimization
1. **Add database indexes** for profile visibility queries
2. **Implement caching** for user settings
3. **Optimize queries** to minimize performance impact

## Code Changes Required

### 1. Update UserService Constructor
```typescript
constructor(
  // ... existing injections
  @InjectModel('UserSettings') private readonly userSettingsModel: Model<UserSettingsDocument>,
  // ... rest of constructor
) {}
```

### 2. Add Profile Visibility Helper Methods
```typescript
private async checkUserVisibility(
  searcherId: string, 
  targetUserId: string, 
  userSettings: any
): Promise<boolean> {
  // Implementation for visibility checking
}

private async checkIfConnection(
  searcherId: string, 
  targetUserId: string
): Promise<boolean> {
  // Check if users are connected
}

private async checkIfFollower(
  searcherId: string, 
  targetUserId: string
): Promise<boolean> {
  // Check if searcher follows target user
}
```

### 3. Update Search Methods
```typescript
private async searchAccountsInternal(req: any, searchData: any, regexSearch: any, limit: number, skipData: number = 0) {
  // ... existing search logic
  
  const results = await this.userModel.find(searchObj)...
  
  // Apply profile visibility filtering
  const filteredResults = await this.applyProfileVisibilityFilter(req, results, 'accounts');
  
  // ... rest of method
}
```

## Benefits

1. **Privacy Compliance**: Respects user privacy preferences
2. **Consistent Experience**: Search results align with profile visibility
3. **User Control**: Users have full control over their discoverability
4. **Platform Trust**: Builds trust by respecting user choices

## Testing Scenarios

### Test Case 1: "No One" Discovery Setting
- **Setup**: User sets profile discovery to "No One"
- **Search**: Another user searches for this user
- **Expected**: User should NOT appear in search results

### Test Case 2: "My Connections" Discovery Setting
- **Setup**: User sets profile discovery to "My Connections"
- **Search**: Non-connection user searches for this user
- **Expected**: User should NOT appear in search results

### Test Case 3: "Everyone" Discovery Setting
- **Setup**: User sets profile discovery to "Everyone"
- **Search**: Any user searches for this user
- **Expected**: User should appear in search results

### Test Case 4: Private Profile Character
- **Setup**: User enables "Private profile character"
- **Search**: User appears in search results
- **Expected**: Limited profile information shown

## Migration Strategy

1. **Backward Compatibility**: Existing search functionality remains unchanged initially
2. **Feature Flag**: Add feature flag to enable/disable profile visibility filtering
3. **Gradual Rollout**: Enable for specific user groups first
4. **Monitoring**: Track search result changes and user feedback
5. **Full Deployment**: Enable for all users after validation

## Performance Considerations

1. **Database Indexes**: Add indexes on userSettings.userId and profileVisibility fields
2. **Caching**: Cache user settings to avoid repeated database queries
3. **Batch Processing**: Process visibility checks in batches for better performance
4. **Query Optimization**: Use aggregation pipelines to minimize database round trips

## Security Implications

1. **Data Protection**: Ensures sensitive profile data is not exposed
2. **Privacy Compliance**: Aligns with privacy regulations and user expectations
3. **Access Control**: Prevents unauthorized access to private profile information
4. **Audit Trail**: Log visibility check decisions for security monitoring
