# Profile Visibility Business Rules Documentation

## Overview

This document explains the different business rules implemented for profile visibility settings. The system applies **different logic based on the type of profile visibility setting**.

## Supported Profile Visibility Types

The system handles these profile visibility types with different business rules:

1. **`profileViewingOptions`** - Controls what profile information is visible (2 options)
2. **`profileDiscoveryOptions`** - Controls profile discovery settings (4 options)
3. **`activeStatusOptions`** - Controls active status visibility (4 options)

## Business Rules by Type

### 1. Profile Viewing Options (2 Options) - MUTUAL EXCLUSIVITY
**Rule**: Exactly one option must be `true`, all others must be `false`

**Logic Flow**:
1. **Find all options set to `true`** in the current type
2. **If multiple options are `true`**, use the first one (maintains mutual exclusivity)
3. **If no options are `true`**, check for options set to `false` and make the other option `true`
4. **Set all options to `false`**, then set only the chosen one to `true`

### 2. Profile Discovery Options & Active Status Options (4 Options) - INVERSE LOGIC
**Rule**: When any item is set to `false`, all other items must be set to `true`

**Logic Flow**:
1. **Check if any option was explicitly set to `false`**
2. **If an option is `false`**, keep it `false` and set all others to `true`
3. **If no option is `false`**, set all options to `true` (default behavior)

## Implementation Details

### 1. Enhanced Logic in `validateAndCorrectProfileVisibilityOptions`

```typescript
// LOGIC: Different rules for different field types
if (fieldName === 'profileViewingOptions') {
  // MUTUAL EXCLUSIVITY LOGIC: When any item is true, make all others false
  // ... mutual exclusivity logic
} else if (fieldName === 'profileDiscoveryOptions' || fieldName === 'activeStatusOptions') {
  // INVERSE LOGIC: When any item is false, make all others true
  let hasExplicitFalse = false;
  let falseOptionIndex = -1;
  
  // Check if any option was explicitly set to false
  for (let i = 0; i < options.length; i++) {
    if (options[i].isEnabled === false) {
      hasExplicitFalse = true;
      falseOptionIndex = i;
      break;
    }
  }
  
  if (hasExplicitFalse) {
    // Set the explicitly false option to false, and all others to true
    options.forEach((option: any, index: number) => {
      option.isEnabled = index !== falseOptionIndex;
    });
  } else {
    // If no explicit false was found, set all options to true (default behavior)
    options.forEach((option: any) => {
      option.isEnabled = true;
    });
  }
}
```

### 2. Enhanced Logic in `mergeProfileVisibilityOptions`

The merge function applies the same business rules when handling partial updates.

## Examples

### Example 1: Profile Viewing Options (Mutual Exclusivity)

**Frontend Input:**
```json
{
  "profileViewingOptions": [
    {
      "Name": "Private profile character",
      "isEnabled": true
    }
  ]
}
```

**System Processing:**
1. Finds "Private profile character" is set to `true`
2. Automatically sets "Your name headline" to `false`
3. Returns complete data with mutual exclusivity

**Expected Output:**
```json
{
  "profileViewingOptions": [
    {
      "Name": "Your name headline",
      "isEnabled": false
    },
    {
      "Name": "Private profile character",
      "isEnabled": true
    }
  ]
}
```

### Example 2: Active Status Options (Inverse Logic)

**Frontend Input:**
```json
{
  "activeStatusOptions": [
    {
      "Name": "Everyone",
      "isEnabled": true
    }
  ]
}
```

**System Processing:**
1. Finds "Everyone" is set to `true`
2. Since no option is `false`, sets all options to `true` (default behavior)
3. Returns complete data with inverse logic

**Expected Output:**
```json
{
  "activeStatusOptions": [
    {
      "Name": "Everyone",
      "isEnabled": true
    },
    {
      "Name": "My Connections",
      "isEnabled": true
    },
    {
      "Name": "People I Follow",
      "isEnabled": true
    },
    {
      "Name": "No One",
      "isEnabled": true
    }
  ]
}
```

### Example 3: Active Status Options - Setting One to False

**Frontend Input:**
```json
{
  "activeStatusOptions": [
    {
      "Name": "Everyone",
      "isEnabled": false
    }
  ]
}
```

**System Processing:**
1. Finds "Everyone" is set to `false`
2. Automatically sets all other options to `true`
3. Returns complete data with inverse logic

**Expected Output:**
```json
{
  "activeStatusOptions": [
    {
      "Name": "Everyone",
      "isEnabled": false
    },
    {
      "Name": "My Connections",
      "isEnabled": true
    },
    {
      "Name": "People I Follow",
      "isEnabled": true
    },
    {
      "Name": "No One",
      "isEnabled": true
    }
  ]
}
```

### Example 4: Profile Discovery Options (Inverse Logic)

**Frontend Input:**
```json
{
  "profileDiscoveryOptions": [
    {
      "Name": "No One",
      "isEnabled": false
    }
  ]
}
```

**System Processing:**
1. Finds "No One" is set to `false`
2. Automatically sets all other options to `true`
3. Returns complete data with inverse logic

**Expected Output:**
```json
{
  "profileDiscoveryOptions": [
    {
      "Name": "Everyone",
      "isEnabled": true
    },
    {
      "Name": "My Connections",
      "isEnabled": true
    },
    {
      "Name": "People I Follow",
      "isEnabled": true
    },
    {
      "Name": "No One",
      "isEnabled": false
    }
  ]
}
```

## API Endpoints

The business rules are applied in these endpoints:

- `POST /api/v1/profile-visibility/save` (Legacy format)
- `POST /api/v1/profile-visibility/save` (New unified format)

## Frontend Implementation

### Best Practices

1. **Send Only Changed Options**: Frontend can send only the options that need to be changed
2. **System Handles the Rest**: The backend automatically applies the correct business rules
3. **No Need to Send All Options**: The system will add missing options and apply logic

### Example Frontend Code

```javascript
// Send only the option you want to disable for active status
const response = await fetch('/api/v1/profile-visibility/save', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    activeStatusOptions: [
      {
        Name: "Everyone",
        isEnabled: false
      }
    ]
  })
});

// The system automatically:
// 1. Sets "My Connections", "People I Follow", "No One" to true
// 2. Returns complete data with inverse logic
const result = await response.json();
```

## Validation Rules

The business rules ensure:

1. **Profile Viewing Options**: Exactly one option has `isEnabled: true`
2. **Profile Discovery & Active Status Options**: When one option is `false`, all others are `true`
3. **Automatic correction** is applied when these rules are violated
4. **Partial updates** are properly handled with correct business logic
5. **Edge cases** are handled gracefully

## Benefits

### For Frontend Developers
- **Simplified Logic**: No need to manually ensure business rules
- **Flexible Input**: Can send partial data, system handles the rest
- **Consistent Behavior**: Always returns valid data with correct business logic

### For Backend
- **Data Integrity**: Ensures profile visibility settings follow business rules
- **Backward Compatibility**: Works with existing data and API contracts
- **Robust Error Handling**: Handles edge cases gracefully

## Error Handling

The system handles these edge cases:

1. **Multiple True Values** (Profile Viewing): Uses the first one, sets others to false
2. **No True Values** (Profile Viewing): Sets the first option to true by default
3. **Multiple False Values** (Discovery/Active Status): Uses the first one, sets others to true
4. **No False Values** (Discovery/Active Status): Sets all options to true by default
5. **Missing Options**: Automatically adds missing options with appropriate defaults
6. **Invalid Data**: Gracefully handles malformed input

## Conclusion

The business rules ensure that profile visibility settings maintain proper data integrity while providing a flexible and user-friendly API. Frontend developers can send partial data, and the system automatically applies the correct business logic based on the type of profile visibility setting. 