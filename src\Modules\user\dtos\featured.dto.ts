import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class MediaDto {
  @IsString()
  mediaType: string;

  @IsString()
  url: string;

  @IsString()
  thumbUrl: string | null;
}
export class featuredDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  title: string;

  @IsString()
  url: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}
