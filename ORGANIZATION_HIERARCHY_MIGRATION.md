# Organization Hierarchy Migration

## Overview

This migration implements a more efficient approach to query organization data by adding dedicated fields to the User schema instead of relying on complex nested queries through the `usersignupdatas` collection.

## Changes Made

### 1. Schema Changes

Added two new fields to the User schema in `src/Models/user.schema.ts`:

```typescript
@Prop({ type: String, default: null })
organizationMainCategory: string;

@Prop({ type: String, default: null })
organizationSubcategory: string;
```

### 2. API Modifications

Modified the three organization APIs to query the `users` table directly:

#### API 1: `/api/v1/user/organization-categories`
- **Before**: Queried `usersignupdatas` collection
- **After**: Aggregates unique main categories from `users` table
- **Benefits**: Faster queries, includes organization counts

#### API 2: `/api/v1/user/organization-subcategories/:mainCategorySlug`
- **Before**: Queried `usersignupdatas` collection
- **After**: Aggregates unique subcategories from `users` table for the given main category
- **Benefits**: Faster queries, includes organization counts

#### API 3: `/api/v1/user/organizations-by-subcategory/:subcategorySlug`
- **Before**: Queried users with complex `signUpData` array matching
- **After**: Direct query on `organizationSubcategory` field
- **Benefits**: Much faster queries, simpler logic

### 3. Signup Process

The signup process now populates the new fields:
```typescript
const data = {
  ...signupData,
  organizationMainCategory: signupData.main_category_slug || null,
  organizationSubcategory: signupData.subcategory_slug || null,
  // ... other fields
};
```

## Migration Process

### Step 1: Run the Migration Script

```bash
node migrate_organization_fields.js
```

This script will:
1. Find all organization users without the new fields
2. Extract organization info from their existing `signUpData`
3. Populate `organizationMainCategory` and `organizationSubcategory` fields
4. Set default values for users where info cannot be extracted

### Step 2: Verify Migration

Check the migration results in the console output. The script will show:
- Number of users found
- Number of users updated
- Number of users skipped
- Details for each updated user

### Step 3: Test the APIs

After migration, test the three APIs to ensure they return the expected data:

```bash
# Test API 1
curl -X GET "http://localhost:3000/api/v1/user/organization-categories"

# Test API 2
curl -X GET "http://localhost:3000/api/v1/user/organization-subcategories/union_1"

# Test API 3
curl -X GET "http://localhost:3000/api/v1/user/organizations-by-subcategory/production_company"
```

## Benefits

1. **Performance**: Direct field queries are much faster than array searches
2. **Simplicity**: Cleaner API logic without complex aggregation pipelines
3. **Scalability**: Better performance as the number of organizations grows
4. **Maintainability**: Easier to understand and modify the code
5. **Backward Compatibility**: Existing signup process remains unchanged

## Data Structure

### Before Migration
```json
{
  "signUpData": [
    {
      "itemId": "64e4b2c2f1a2b2c3d4e5f6a7",
      "isSelected": true,
      "slug": "production_company",
      "subCategory": "sub_type"
    }
  ]
}
```

### After Migration
```json
{
  "organizationMainCategory": "union_1",
  "organizationSubcategory": "production_company",
  "signUpData": [
    // ... existing data preserved
  ]
}
```

## Rollback Plan

If needed, the changes can be rolled back by:

1. Reverting the schema changes in `user.schema.ts`
2. Reverting the API modifications in `user.service.ts`
3. The migration script can be modified to remove the new fields if needed

## Notes

- The existing `signUpData` structure is preserved for backward compatibility
- New signups will automatically populate the new fields
- The migration script handles existing data gracefully
- Default values are assigned for edge cases where organization info cannot be extracted
