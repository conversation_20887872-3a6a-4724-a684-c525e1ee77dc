import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { GroupService } from './group.service';
import { CreateGroupDto } from './dto/create-group.dto';
import { AuthGuard } from '../auth/auth.guard';
import { GetGroupsDto } from './dto/get-groups.dto';
import { UpdateGroupDto } from './dto/update-group.dto';
import { IDParamDTO } from 'src/Custom/helpers/dto.helper';
import { GetSuggestedUsersDto } from './dto/get-suggested-users.dto';
import { InviteUserDto } from './dto/invite-user.dto';
import {
  blockUnblockMemberDto,
  InviteAcceptRejectDto,
  removeGroupMemberDto,
} from './dto/invite-accept-reject.dto';
import { addGroupPostDto, repostInGroupDto } from './dto/add-post-group.dto';
import { GetGroupPostsDto } from './dto/get-group-posts.dto';
import {
  InviteUserViaEmialDto,
  JoinGroupDTO,
} from './dto/invite-user-email.dto';

@Controller('group')
export class GroupController {
  constructor(private readonly groupService: GroupService) {}

  // Create Group
  @UseGuards(AuthGuard)
  @Post('create')
  async createGroup(@Req() req: Request, @Body() postData: CreateGroupDto) {
    return this.groupService.createGroup(req, postData);
  }

  // Get all Group
  @UseGuards(AuthGuard)
  @Get()
  getAllGroups(@Req() req: Request, @Query() queryParam: GetGroupsDto) {
    return this.groupService.getAllGroups(req, queryParam);
  }

  // Get Suggested Users for invite
  @UseGuards(AuthGuard)
  @Get('suggested-users/invite')
  suggestedUsers(
    @Req() req: Request,
    @Query() queryParam: GetSuggestedUsersDto,
  ) {
    return this.groupService.suggestedUsers(req, queryParam);
  }

  // Invite User for group
  @UseGuards(AuthGuard)
  @Post('invite/user')
  @HttpCode(HttpStatus.OK)
  async inviteUser(@Req() req: Request, @Body() body: InviteUserDto) {
    return this.groupService.inviteUser(req, body);
  }

  // Invite User for group
  @UseGuards(AuthGuard)
  @Post('invite/email')
  @HttpCode(HttpStatus.OK)
  async inviteUserViaEmail(
    @Req() req: Request,
    @Body() body: InviteUserViaEmialDto,
  ) {
    return this.groupService.inviteUserViaEmail(req, body);
  }

  // Accept/Reject Invitation for group
  @UseGuards(AuthGuard)
  @Post('invite/accept-reject')
  @HttpCode(HttpStatus.OK)
  async inviteAcceptReject(
    @Req() req: Request,
    @Body() body: InviteAcceptRejectDto,
  ) {
    return this.groupService.inviteAcceptReject(req, body);
  }

  @UseGuards(AuthGuard)
  @Post('join')
  async joinGroup(@Req() req: Request, @Body() body: JoinGroupDTO) {
    return this.groupService.joinGroup(req, body);
  }

  // blockUnblockGroupMember
  @UseGuards(AuthGuard)
  @Post('block-unblock/member')
  async blockUnblockGroupMember(
    @Req() req: Request,
    @Body() body: blockUnblockMemberDto,
  ) {
    return this.groupService.blockUnblockGroupMember(req, body);
  }

  //getBlockedMembers
  @UseGuards(AuthGuard)
  @Get('blocked/members/:groupId')
  async getBlockedMembers(@Param() params: any) {
    return this.groupService.getBlockedMembers(params.groupId);
  }

  // Remove Group Member
  @UseGuards(AuthGuard)
  @Post('remove/member')
  @HttpCode(HttpStatus.OK)
  async removeGroupMember(
    @Req() req: Request,
    @Body() body: removeGroupMemberDto,
  ) {
    return this.groupService.removeMemberFromGroup(req, body);
  }

  @UseGuards(AuthGuard)
  @Post('add/post')
  async addpost(@Req() req: Request, @Body() postData: addGroupPostDto) {
    return this.groupService.addPostInGroup(req, postData);
  }

  // Get Group Posts
  @UseGuards(AuthGuard)
  @Get('posts')
  getGroupPosts(@Req() req: Request, @Query() queryParam: GetGroupPostsDto) {
    return this.groupService.getGroupPosts(req, queryParam);
  }

  // Get all Group Rule
  @UseGuards(AuthGuard)
  @Get('group-rule')
  getGroupRules() {
    return this.groupService.getGroupRules();
  }

  // Get all Group
  @UseGuards(AuthGuard)
  @Get(':id')
  getGroupById(@Param() params: IDParamDTO, @Req() req: Request) {
    return this.groupService.getGroupById(req, params.id);
  }

  // Update group
  @UseGuards(AuthGuard)
  @Patch(':id')
  updateGroup(@Param() params: IDParamDTO, @Body() body: UpdateGroupDto) {
    return this.groupService.updateGroup(params.id, body);
  }

  @UseGuards(AuthGuard)
  @Post('repost')
  async repostPost(@Req() req: Request, @Body() postData: repostInGroupDto) {
    return this.groupService.repostInGroup(req, postData);
  }
}
