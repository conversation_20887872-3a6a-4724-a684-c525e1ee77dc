# Testing Blocked Users Filter in Home Feed

## Overview
This document outlines the testing approach to verify that blocked users' posts are properly filtered out from the home screen feeds.

## Test Cases

### 1. Basic Blocking Test
**Objective**: Verify that posts from blocked users don't appear in home feed

**Steps**:
1. Create two test users: User A and User B
2. User A creates a post
3. User B blocks User A
4. User B fetches home feed
5. Verify that User A's post is not in the feed

**Expected Result**: User A's post should not appear in User B's home feed

### 2. Repost Blocking Test
**Objective**: Verify that reposts from blocked users are filtered out

**Steps**:
1. Create three test users: User A, User B, and User C
2. User A creates a post
3. User C reposts User A's post
4. User B blocks User C
5. User B fetches home feed
6. Verify that User C's repost is not in the feed

**Expected Result**: User C's repost should not appear in User B's home feed

### 3. Unblocking Test
**Objective**: Verify that unblocking a user makes their posts visible again

**Steps**:
1. Create two test users: User A and User B
2. User A creates a post
3. User B blocks User A
4. User B fetches home feed (should not see User A's post)
5. User B unblocks User A
6. User B fetches home feed again
7. Verify that User A's post is now visible

**Expected Result**: User A's post should appear in User B's home feed after unblocking

### 4. Multiple Blocked Users Test
**Objective**: Verify that multiple blocked users are handled correctly

**Steps**:
1. Create multiple test users: User A, User B, User C, User D
2. User A creates posts
3. User B creates posts
4. User C creates posts
5. User D blocks User A and User B
6. User D fetches home feed
7. Verify that only User C's posts are visible

**Expected Result**: Only User C's posts should appear in User D's home feed

## API Endpoints to Test

### Home Feed API
- **Endpoint**: `GET /api/user/home-data`
- **Headers**: Include authentication token
- **Query Parameters**: 
  - `page`: Page number (optional)
  - `perPage`: Items per page (optional)
  - `sortBy`: Sort field (optional)
  - `sort`: Sort order (optional)

### Block User API
- **Endpoint**: `POST /api/user/block`
- **Headers**: Include authentication token
- **Body**: 
  ```json
  {
    "userId": "user_id_to_block",
    "reason": "optional_reason"
  }
  ```

### Unblock User API
- **Endpoint**: `POST /api/user/unblock`
- **Headers**: Include authentication token
- **Body**: 
  ```json
  {
    "userId": "user_id_to_unblock"
  }
  ```

## Implementation Details

### Changes Made
1. **homeData method** in `src/Modules/user/services/user.service.ts`:
   - Added blocking filter to exclude posts from blocked users
   - Added filter to exclude reposts from blocked users
   - Uses efficient `$nin` query with pre-fetched blocked users list

### Code Changes
```typescript
// Get blocked users for the logged-in user
const blockedUsers = await this.userBlockModel
  .find({ blockerId: loggedInUser._id, isActive: true })
  .select('blockedUserId')
  .then(blocks => blocks.map(b => b.blockedUserId));

// Filter out posts from blocked users
{
  $match: {
    userId: { $nin: blockedUsers }
  }
},
// Filter out posts reposted by blocked users
{
  $match: {
    $or: [
      { repostBy: { $exists: false } }, // No repost
      { repostBy: { $nin: blockedUsers } } // Repost not by blocked user
    ]
  }
},
```

## Performance Considerations
- The blocking filter is applied early in the aggregation pipeline for better performance
- Blocked users are fetched once per request and reused in the pipeline
- The filter uses MongoDB's `$nin` operator which is efficient for array-based exclusions

## Monitoring
- Monitor query performance for users with many blocked users
- Consider adding indexes on `userblocks` collection if not already present
- Monitor the impact on home feed loading times 