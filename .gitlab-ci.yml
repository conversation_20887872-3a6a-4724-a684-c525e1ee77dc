include:
  remote: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/build.yaml'

stages:
  - build
  - deploy

variables:
  PROJECT: "pepli-beta"
  TECHNOLOGY: "node"
  
build:
  stage: build
  extends: .build
  variables:
    BUILD_ARGS: "--build-arg APP_NAME=${PROJECT} --build-arg NODE_ENV=${CI_COMMIT_REF_NAME}"
  only:
    - pre-beta

deploy_prod:
  stage: deploy
  extends: .deploy
  environment:
    name: $CI_COMMIT_REF_NAME
  variables:
    CONT_PORT: "2030"
    DEPLOY_PATH: $PROJECT
    IP_ADDRESS: "*************"
    DOCKER_COMPOSE_TEMPLATE: "https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/docker/docker-compose.yaml"
  script:
    - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker system prune -f
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} docker login $CI_REGISTRY -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} mkdir -p ${DEPLOY_PATH}
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk $DOCKER_COMPOSE_TEMPLATE | sed 's%__PROJECT_NAME__%${PROJECT}%g;s%__PORT__%${CONT_PORT}%g;s%__IMAGE_URI__%${CI_REGISTRY_IMAGE}%g;s%__IMAGE_TAG__%${IMAGE_TAG}%g' > ${DEPLOY_PATH}/docker-compose.yml"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose pull"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose down"
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "cd ${DEPLOY_PATH}; docker-compose up -d --force-recreate"
  only:
    - pre-beta