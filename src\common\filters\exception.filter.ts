import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import CONSTANT from '../constant/common.constant';
import { errorResponse } from '../../Custom/helpers/responseHandler';

@Catch(HttpException)
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    // console.log('Error: ', exception.message, exception.getStatus());

    const error = exception.getResponse() as any;

    if (exception.getStatus() === HttpStatus.PAYLOAD_TOO_LARGE) {
      // Check if the exception message contains the Multer error message.
      if (error.message?.includes(CONSTANT.FILE_SIZE_LARGE)) {
        return response
          .status(HttpStatus.PAYLOAD_TOO_LARGE)
          .json(
            errorResponse(
              null,
              CONSTANT.FILE_SIZE_EXCEED,
              HttpStatus.PAYLOAD_TOO_LARGE,
            ),
          );
      }
    } else if (exception.getStatus() === HttpStatus.BAD_REQUEST) {
      if (typeof error.message === 'string') {
        // Handle the case where errors is a string
        return response
          .status(HttpStatus.BAD_REQUEST)
          .json(errorResponse(null, error.message, HttpStatus.BAD_REQUEST));
      } else {
        // Handle the case where errors is an object
        return response
          .status(HttpStatus.BAD_REQUEST)
          .json(errorResponse(null, error.message[0], HttpStatus.BAD_REQUEST));
      }
    } else {
      // For other exceptions, let NestJS handle them.
      return response
        .status(exception.getStatus())
        .json(errorResponse(null, error.message, exception.getStatus()));
    }
  }
}
