import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';

export type FeaturedDocument = Featured & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class Featured {
  @Prop({ type: String })
  title: string;

  @Prop({ type: String })
  url: string;

  @Prop({ type: String })
  description: string;

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const FeaturedSchema = SchemaFactory.createForClass(Featured);
