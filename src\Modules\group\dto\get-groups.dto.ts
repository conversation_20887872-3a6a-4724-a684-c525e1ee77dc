import { IsOptional, IsString } from 'class-validator';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';

export class GetGroupsDto {
  @IsStringValidation('search', 50, true)
  readonly search: string;

  @IsStringValidation('page', 3, true)
  readonly page: string;

  @IsStringValidation('per page', 3, true)
  readonly perPage: string;

  @IsStringValidation('sort', 2, true)
  readonly sort: string;

  @IsStringValidation('sort by', 20, true)
  readonly sortBy: string;

  @IsOptional()
  @IsString()
  userId: string;
}
