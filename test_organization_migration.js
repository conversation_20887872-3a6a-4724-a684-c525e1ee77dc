const mongoose = require('mongoose');
require('dotenv').config();

// Test the migration script
async function testMigration() {
  try {
    console.log('🧪 Testing Organization Migration Script...\n');
    
    // Test 1: Create sample Excel file
    console.log('Test 1: Creating sample Excel file...');
    const { createSampleExcelFile } = require('./migrate_organizations_from_excel');
    createSampleExcelFile();
    console.log('✅ Sample Excel file created successfully\n');
    
    // Test 2: Test Excel reading functionality
    console.log('Test 2: Testing Excel reading functionality...');
    const { readExcelFile } = require('./migrate_organizations_from_excel');
    
    try {
      const testData = readExcelFile('sample_organizations.xlsx');
      console.log(`✅ Excel file read successfully. Found ${testData.length} organizations`);
      
      // Show sample data
      console.log('\nSample data from Excel:');
      testData.slice(0, 3).forEach((org, index) => {
        console.log(`  ${index + 1}. ${org.mainCategory} > ${org.subCategory} > ${org.organization}`);
      });
      
    } catch (error) {
      console.log('❌ Excel reading test failed:', error.message);
    }
    
    console.log('\n🎉 Test completed!');
    console.log('\nNext steps:');
    console.log('1. Review the sample_organizations.xlsx file');
    console.log('2. Replace with your actual organization data');
    console.log('3. Run: node migrate_organizations_from_excel.js your_file.xlsx');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run test if called directly
if (require.main === module) {
  testMigration()
    .then(() => {
      console.log('\nTest script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testMigration };
