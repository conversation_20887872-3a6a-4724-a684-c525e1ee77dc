# Deactivated Accounts Migration Guide

## Problem
When implementing the enhanced account deactivation system, you may have existing users who were deactivated before the `deactivatedOn` field was added. These accounts will have:
- `isDeactivated: true`
- `deactivatedOn: null` or field doesn't exist

## Example Legacy Account
```json
{
  "_id": "685eb301475871c6852994a1",
  "email": "<EMAIL>", 
  "isDeactivated": true,
  // deactivatedOn field is missing
}
```

## Solution Options

### Option 1: Automatic Handling (Recommended)
The enhanced cleanup service now automatically handles legacy accounts by treating any deactivated account without a `deactivatedOn` timestamp as immediately eligible for cleanup.

**No action required** - the system will:
- ✅ Include legacy deactivated accounts in cleanup process
- ✅ Log them as "legacy deactivation (no timestamp)"
- ✅ Delete them in the next cleanup cycle

### Option 2: Migration Script (Optional)
If you want to give existing deactivated users more time, run the migration script:

```bash
# Update database connection in the script first
node migrate_deactivated_accounts.js
```

**What the migration does:**
- Finds all deactivated accounts without `deactivatedOn`
- Sets `deactivatedOn` to 15 days ago (giving 15 more days before cleanup)
- Allows users to reactivate their accounts if needed

## Implementation Details

### Enhanced Cleanup Query
```typescript
// Old query (missed legacy accounts)
{ isDeactivated: true, deactivatedOn: { $lte: thirtyDaysAgo, $ne: null } }

// New query (includes legacy accounts)  
{
  isDeactivated: true,
  $or: [
    { deactivatedOn: { $lte: thirtyDaysAgo } },
    { deactivatedOn: { $exists: false } },
    { deactivatedOn: null }
  ]
}
```

### Enhanced Deactivation Service
```typescript
// Now properly sets deactivatedOn timestamp
const updateData: any = { isDeactivated: deactivateData.isDeactivated };

if (deactivateData.isDeactivated) {
  updateData.deactivatedOn = new Date(); // ✅ Sets timestamp
} else {
  updateData.deactivatedOn = null; // ✅ Clears on reactivation
}
```

## Verification Steps

### 1. Check Current Deactivated Accounts
```javascript
// In MongoDB shell or script
db.users.find({
  isDeactivated: true,
  $or: [
    { deactivatedOn: { $exists: false } },
    { deactivatedOn: null }
  ]
}).count()
```

### 2. Test New Deactivation Flow
```bash
# Test deactivation API
curl -X POST http://localhost:3000/user/deactivate-account \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"isDeactivated": true}'

# Should now return deactivatedOn timestamp
{
  "status": true,
  "message": "Account deactivated successfully",
  "data": {
    "isDeactivated": true,
    "deactivatedOn": "2024-01-15T10:30:00.000Z"
  }
}
```

### 3. Monitor Cleanup Logs
```bash
# Check application logs for cleanup activity
tail -f application.log | grep "cleanup"
```

Expected log output:
```
--- Starting cleanup of expired deactivated accounts ---
Found 3 expired deactivated accounts for cleanup
Cleaned up account data for user: <EMAIL> (ID: 123) - legacy deactivation (no timestamp)
Cleaned up account data for user: <EMAIL> (ID: 456) - deactivated on 2024-01-01T00:00:00.000Z
Successfully deleted 3 expired deactivated accounts
--- Completed cleanup of expired deactivated accounts ---
```

## Migration Script Usage

### Prerequisites
```bash
npm install mongodb
```

### Configuration
Update these values in `migrate_deactivated_accounts.js`:
```javascript
const MONGODB_URI = 'mongodb://localhost:27017/your-database-name';
const DATABASE_NAME = 'pepli-database'; // Your actual database name
```

### Run Migration
```bash
# Dry run - see what would be updated
node migrate_deactivated_accounts.js

# The script will show:
# - Number of accounts to update
# - Sample account emails
# - Default timestamp that will be set
```

### Migration Output Example
```
=== Deactivated Accounts Migration ===
Found 5 deactivated accounts without deactivatedOn field.
Sample accounts:
- <EMAIL> (ID: 685eb301475871c6852994a1)
- <EMAIL> (ID: 123456789abcdef123456789)

Migration completed successfully!
Updated 5 accounts with deactivatedOn field.
Default timestamp used: 2024-01-01T10:30:00.000Z
These accounts will be cleaned up after: 2024-01-31T10:30:00.000Z
```

## Recommendations

### For Production Deployment
1. **Deploy the enhanced system** with automatic legacy handling
2. **Monitor the first cleanup cycle** to see how many legacy accounts are processed
3. **Optionally run migration** if you want to give users more time
4. **Communicate with users** about the account cleanup policy

### For Testing
1. Create test accounts and deactivate them
2. Verify `deactivatedOn` timestamp is set
3. Test reactivation clears the timestamp
4. Test cleanup process with mixed legacy/new accounts

## Troubleshooting

### Issue: Legacy accounts not being cleaned up
**Solution:** Check the cleanup query includes the `$or` condition for missing/null `deactivatedOn`

### Issue: Migration script connection error
**Solution:** Verify MongoDB URI and database name in the script

### Issue: Accounts reactivated but still have deactivatedOn
**Solution:** Ensure the enhanced deactivation service sets `deactivatedOn: null` on reactivation

## Safety Considerations

1. **Backup First:** Always backup your database before running migrations
2. **Test Environment:** Test the migration on a copy of production data first
3. **User Communication:** Notify users about account cleanup policies
4. **Recovery Plan:** Have a plan to restore accounts if cleanup happens too early

## Files Modified for Legacy Support

1. **accountCleanup.service.ts** - Enhanced queries to include legacy accounts
2. **user.service.ts** - Fixed deactivateAccount method to set timestamps
3. **migrate_deactivated_accounts.js** - New migration script
4. **user.schema.ts** - Added deactivatedOn field (already done)

The enhanced system now gracefully handles both new deactivations (with timestamps) and legacy deactivations (without timestamps), ensuring no accounts fall through the cracks.
