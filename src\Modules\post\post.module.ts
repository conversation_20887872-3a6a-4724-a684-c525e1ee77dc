import { forwardRef, Module } from '@nestjs/common';
import { PostService } from './services/post.service';
import { PostController } from './controller/post.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { UserSchema } from 'src/Models/user.schema';
import { PostSchema } from 'src/Models/post.schema';
import { LikeSchema } from 'src/Models/like.schema';
import { CommentSchema } from 'src/Models/comment.schema';
import { BookmarkSchema } from 'src/Models/bookmark.schema';
import { StorySchema } from 'src/Models/story.schema';
import { GroupModule } from '../group/group.module';
import { UserRepository } from 'src/Repositories/user.repository';
import { CommentRepository } from 'src/Repositories/comment.repository';
import { PostRepository } from 'src/Repositories/post.repository';
import { Subscriber, SubscriberSchema } from 'src/Models/subscriber.schema';
import {
  Notifications,
  NotificationsSchema,
} from 'src/Models/notification.schema';
import { WhoCanMessageValidatorService } from '../user/helper/whoCanMessageValidator';
import {
  FollowerInfo,
  FollowerInfoSchema,
} from 'src/Models/followerInfo.schema';
import {
  ConnectionInfo,
  ConnectionInfoSchema,
} from 'src/Models/connectionInfo.schema';
import { People, PeopleSchema } from 'src/Models/peoples.schema';

@Module({
  imports: [
    forwardRef(() => GroupModule),
    MongooseModule.forFeature([
      { name: 'User', schema: UserSchema },
      { name: 'Post', schema: PostSchema },
      { name: 'Like', schema: LikeSchema },
      { name: 'Comment', schema: CommentSchema },
      { name: 'Bookmark', schema: BookmarkSchema },
      { name: 'Story', schema: StorySchema },
      { name: Subscriber.name, schema: SubscriberSchema },
      { name: Notifications.name, schema: NotificationsSchema },
      { name: FollowerInfo.name, schema: FollowerInfoSchema },
      { name: ConnectionInfo.name, schema: ConnectionInfoSchema },
      { name: People.name, schema: PeopleSchema },
    ]),
    CommonModule,
  ],
  controllers: [PostController],
  providers: [
    PostService,
    UserRepository,
    CommentRepository,
    PostRepository,
    WhoCanMessageValidatorService,
  ],
  exports: [PostService],
})
export class PostModule {}
