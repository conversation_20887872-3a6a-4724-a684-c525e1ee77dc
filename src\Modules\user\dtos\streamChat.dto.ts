import {
  ArrayNotEmpty,
  ArrayUnique,
  IsArray,
  IsNotEmpty,
  IsString,
} from 'class-validator';

export class groupChatDto {
  @IsString()
  @IsNotEmpty()
  groupName: string;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsString({ each: true })
  users: string[];
}

export class leaveChannelDto {
  @IsString()
  @IsNotEmpty()
  groupId: string;
}

export class addMembersToGroupChannelDto {
  @IsString()
  @IsNotEmpty()
  groupId: string;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsString({ each: true })
  users: string[];
}
