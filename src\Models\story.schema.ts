import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from './user.schema';

export type storyDocument = Story & Document;

export enum MediaEnum {
  IMAGE = 'image',
  VIDEO = 'video',
}

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class Story {
  @Prop({ type: String, default: null })
  mediaType: string;

  @Prop({ type: String, default: null })
  url: string;

  @Prop({ type: String, default: null })
  thumbUrl: string;

  @Prop({ default: null })
  shareableLink: string;

  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    default: [],
  })
  likeUsers: User[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: User;
}

export const StorySchema = SchemaFactory.createForClass(Story);
