{"name": "pepli.backend", "version": "0.0.1", "description": "nestjs-mongodb-project", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build && cp -r src/views dist/views && cp -r src/Templates dist/src/Templates && cp pushNotification.json dist/pushNotification.json", "build-local": "nest build && xcopy src\\views dist\\views /E /I /Y && xcopy src\\Templates dist\\src\\Templates /E /I /Y && copy pushNotification.json dist\\pushNotification.json", "build-prod": "nest build && cp -r src/views dist/views && cp -r src/Templates dist/src/Templates && cp pushNotification.json dist/pushNotification.json", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:stg": "NODE_ENV=mock nest start --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.465.0", "@aws-sdk/client-ses": "^3.465.0", "@aws-sdk/s3-request-presigner": "^3.472.0", "@nestjs/common": "^9.4.3", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^9.0.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.2.0", "@nestjs/schedule": "^4.0.0", "@types/fluent-ffmpeg": "^2.1.24", "api": "^6.1.1", "axios": "^1.6.0", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cloudinary": "^1.32.0", "cookie-parser": "^1.4.6", "country-state-city": "^3.2.1", "crypto-js": "^4.1.1", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "ejs": "^3.1.10", "firebase": "^9.14.0", "firebase-admin": "^11.2.1", "fluent-ffmpeg": "^2.1.3", "handlebars": "^4.7.7", "i": "^0.3.7", "jsonwebtoken": "^8.5.1", "mongoose": "^8.7.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.8.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "stream-chat": "^8.14.4", "twilio": "^5.3.5", "uuid": "^9.0.0", "webpack": "^5.99.8", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.14", "@types/jest": "28.1.8", "@types/multer": "^1.4.7", "@types/node": "^16.0.0", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "cpy-cli": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.3", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.8", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}