import { iAmMemberEnum } from 'src/Models/user.schema';

export const NOTIFICATION_MESSAGES = {
  JOIN_GROUP: `invited you to join the group `,
  JOINED_GROUP: `joined the group`,
  HAS_MADE_COLLABORATION_POST: 'has made collaboration post with you.',
  TAGGED_YOU_IN_POST: 'tagged you in a post',
  HAS_MADE_POST: 'has made a post',
  COLLABORATION_REQUEST_STATUS: (status) => `collaboration request ${status}`,
  CONNECTION_REQUEST_STATUS: (status) => `connection request ${status}.`,
  CLIENT_REQUEST_STATUS: (type, status) => `${type} ${status} client request.`,
  MEMBERSHIP_REQUEST_STATUS: (status) => `${status} membership request.`,
  REPOST_BY: (name) => `Your post has been reposted by ${name}`,
  COMMENT_MENTIONED: 'mentioned you in a comment',
  MEMBERSHIP_REQUEST_SENT: ' has sent you request for membership verification.',
  FOL<PERSON>OW_REQUEST_SENT: ' sent you following request.',
  F<PERSON><PERSON><PERSON>_YOU: ' has followed you.',
  ACCEPTED_F<PERSON>LOWING: ' accepted your following request.',
  CONNECTION_REQUEST_SENT: 'sent you connection request.',
  ADD_CLIENT_NOTIFICATION: 'wants to add you as their client.',
  PENDING_CLIENT_REQUEST_NOTIFICATION:
    'client request is pending. Please wait for the client to respond.',
  HAS_SUBSCRIBED_YOU: ' has subscribed you.',
  EMPLOYER_HIRER_VERIFICATION_SUBMITION_NOTIFICATION:
    'Your employer/hirer verification request has been submitted successfully. Please wait for admin approval.',
  EMPLOYER_HIRER_VERIFICATION_APPROVED:
    'Your Employer / Hirer verification has been approved.',
  EMPLOYER_HIRER_VERIFICATION_REJECTED: (rejectReason) =>
    `Your Employer / Hirer verification has been rejected.\nReason: ${rejectReason}`,
};

export const notificationMessage = (
  notification,
  action,
  loggedInUser = null,
  groupDetails = null,
) => {
  if (!notification?.notificationType) return '';

  const { notificationType } = notification;

  const isInstitutionMember =
    loggedInUser?.iAmMember ===
    iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY;

  switch (notificationType) {
    case 'collaborators':
      if (action === 'accept')
        return isInstitutionMember
          ? 'Collaboration request accepted.'
          : `accepted the collaboration request.`;

      if (action === 'reject')
        return isInstitutionMember
          ? 'Collaboration request rejected.'
          : `rejected the collaboration request.`;
      break;

    case 'groupinvite':
      if (groupDetails?.name) {
        return action === 'accept' ? `joined the group` : `rejected the group`;
      }
      break;

    case 'taggedPeople':
      if (action === 'accept')
        return isInstitutionMember
          ? 'Tagged request accepted.'
          : `accepted the tagged request.`;

      if (action === 'reject')
        return isInstitutionMember
          ? 'Tagged request rejected.'
          : `rejected the tagged request.`;
      break;

    case 'likePost':
      return notification.enum === 'like'
        ? 'Liked your post.'
        : 'Reacted to this post.';
    case 'commentReaction':
      return notification.enum === 'like'
        ? 'Liked your comment.'
        : 'Reacted to your comment.';
    case 'storyReaction':
      return notification.enum === 'like'
        ? 'Liked your story.'
        : 'Reacted to your story.';
    default:
      return '';
  }
};
