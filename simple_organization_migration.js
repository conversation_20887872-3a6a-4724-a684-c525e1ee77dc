const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const db = mongoose.connection;

db.on('error', console.error.bind(console, 'MongoDB connection error:'));
db.once('open', async () => {
  console.log('Connected to MongoDB');
  
  try {
    // Get the users collection
    const usersCollection = db.collection('users');
    
    // Find all organization users
    const organizationUsers = await usersCollection.find({
      iAmMember: 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility',
      isFakeAccount: false
    }).toArray();
    
    console.log(`Found ${organizationUsers.length} organization users`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const user of organizationUsers) {
      try {
        // Skip if already has the new fields
        if (user.organizationMainCategory && user.organizationSubcategory) {
          skippedCount++;
          continue;
        }
        
        // Set default values for organizations without category info
        const mainCategory = user.organizationMainCategory || 'union_1';
        const subcategory = user.organizationSubcategory || 'other';
        
        // Update the user document
        const result = await usersCollection.updateOne(
          { _id: user._id },
          {
            $set: {
              organizationMainCategory: mainCategory,
              organizationSubcategory: subcategory
            }
          }
        );
        
        if (result.modifiedCount > 0) {
          updatedCount++;
          console.log(`✅ Updated: ${user.userName || user.businessOrganizationName}`);
        } else {
          skippedCount++;
        }
        
      } catch (error) {
        console.error(`❌ Error updating user ${user._id}:`, error.message);
        skippedCount++;
      }
    }
    
    console.log('\n🎉 Migration completed!');
    console.log(`✅ Updated: ${updatedCount} users`);
    console.log(`⏭️  Skipped: ${skippedCount} users`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
});
