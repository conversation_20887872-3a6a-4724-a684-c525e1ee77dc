import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { postDocument } from 'src/Models/post.schema';
import { Privacy } from 'src/common/constant/enum';
import { StatusEnum } from 'src/Models/connectionInfo.schema';

@Injectable()
export class PostRepository extends AbstractRepository<postDocument> {
  constructor(@InjectModel('Post') postModel: Model<postDocument>) {
    super(postModel);
  }

  async getAllUserReposts(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const data = await this.model.aggregate([
      { $match: filter },
      { $sort: sortObj },
      { $skip: skipData },
      { $limit: limitData },
      {
        $lookup: {
          from: 'users',
          localField: 'repostBy',
          foreignField: '_id',
          as: 'repostBy',
          pipeline: [
            { $match: { isFakeAccount: false } },
            {
              $project: {
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$repostBy',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          collaborators: {
            $filter: {
              input: '$collaborators',
              as: 'collab',
              cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
            },
          },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'collaborators.id',
          foreignField: '_id',
          as: 'collaborators',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'fundraisers',
          foreignField: '_id',
          as: 'fundraisers',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'taggedPeople',
          foreignField: '_id',
          as: 'taggedPeople',
          pipeline: [
            { $match: { isFakeAccount: false } },
            {
              $project: {
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'userId',
          pipeline: [
            { $match: { isFakeAccount: false } },
            {
              $project: {
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$userId',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'groups',
          localField: 'group',
          foreignField: '_id',
          as: 'group',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$group',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            {
              $and: [
                { group: { $exists: true } },
                { 'group.privacy': Privacy.PUBLIC },
              ],
            },
            {
              group: { $exists: false },
            },
          ],
        },
      },
      {
        $project: {
          updatedAt: 0,
          __v: 0,
          group: 0,
        },
      },
    ]);

    return data;
  }

  async getAllPostedImages(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const data = await this.model.aggregate([
      { $match: filter },
      { $sort: sortObj },
      {
        $lookup: {
          from: 'groups',
          localField: 'group',
          foreignField: '_id',
          as: 'group',
          pipeline: [
            {
              $project: {
                name: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$group',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            {
              $and: [
                { group: { $exists: true } },
                { 'group.privacy': Privacy.PUBLIC },
              ],
            },
            {
              group: { $exists: false },
            },
          ],
        },
      },
      { $skip: skipData },
      { $limit: limitData },
      { $project: { _id: 1, media: 1 } },
    ]);

    return data;
  }
}
