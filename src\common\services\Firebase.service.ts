import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { ServiceAccount } from 'firebase-admin';
import * as serviceAccount from '../../../pushNotification.json';

@Injectable()
export class FirebaseService {
  constructor() {
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount as ServiceAccount),
      });
    }
  }

  async send(pushNotificationData: {
    title: string;
    body: string;
    deviceToken: string | string[];
  }) {
    const { title, body, deviceToken } = pushNotificationData;

    const message = {
      notification: {
        title,
        body,
      },
      tokens: Array.isArray(deviceToken) ? deviceToken : [deviceToken],
    };

    const response = await admin.messaging().sendEachForMulticast(message);
    return response;
  }
}
