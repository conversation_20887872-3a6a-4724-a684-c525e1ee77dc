import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PostService } from '../services/post.service';
import { AuthGuard } from 'src/Modules/auth/auth.guard';
import {
  acceptRejectCollabRequestDTO,
  addCommentPostDto,
  addPostDto,
  addStoryDto,
  bookmarkPostDto,
  editPostCommentDto,
  editPostDto,
  likeUnlikeCommentDto,
  likeUnlikePostDto,
  likeUnlikeStoryDto,
  postCollaboratorTagPeopleDto,
  postIdDto,
  repostUserDto,
} from '../dtos/post.dtos';
import { IDParamDTO } from 'src/Custom/helpers/dto.helper';

@Controller('post')
export class PostController {
  constructor(private readonly postService: PostService) {}

  @UseGuards(AuthGuard)
  @Post('add')
  async addpost(@Req() req: Request, @Body() postData: addPostDto) {
    return this.postService.addPost(req, postData);
  }

  @UseGuards(AuthGuard)
  @Post('repost')
  async repost(@Req() req: Request, @Body() postData: postIdDto) {
    return this.postService.repost(req, postData);
  }

  @UseGuards(AuthGuard)
  @Patch('edit')
  async editpost(@Req() req: Request, @Body() editPostData: editPostDto) {
    return this.postService.editPost(req, editPostData);
  }

  @UseGuards(AuthGuard)
  @Get('collaborator')
  async getcollaborators(@Req() req: Request) {
    return this.postService.getCollaborators(req);
  }

  @UseGuards(AuthGuard)
  @Post('collaborator-list')
  async postcollaboratorslist(
    @Req() req: Request,
    @Body() postData: postCollaboratorTagPeopleDto,
  ) {
    return this.postService.postCollaboratorsList(req, postData);
  }

  //acceptRejectCollaborationRequest
  @UseGuards(AuthGuard)
  @Post('collaborator/accept-reject')
  async acceptRejectCollaborationRequest(
    @Req() req: Request,
    @Body() data: acceptRejectCollabRequestDTO,
  ) {
    return this.postService.acceptRejectCollaborationRequest(req, data);
  }

  @UseGuards(AuthGuard)
  @Post('repost-user-list')
  async repostuserlist(@Req() req: Request, @Body() postData: repostUserDto) {
    return this.postService.rePostUsers(req, postData);
  }

  @UseGuards(AuthGuard)
  @Post('like-unlike')
  async likeunlikepost(
    @Req() req: Request,
    @Body() postData: likeUnlikePostDto,
  ) {
    return this.postService.likeUnlikePost(req, postData);
  }

  @UseGuards(AuthGuard)
  @Post('comment')
  async addcommentpost(
    @Req() req: Request,
    @Body() postData: addCommentPostDto,
  ) {
    return this.postService.addCommentPost(req, postData);
  }

  @UseGuards(AuthGuard)
  @Patch('edit/comment')
  async editpostcomment(
    @Req() req: Request,
    @Body() editPostCommentData: editPostCommentDto,
  ) {
    return this.postService.editPostComment(req, editPostCommentData);
  }

  @UseGuards(AuthGuard)
  @Post('/comment/like-unlike')
  async likeUnlikeComment(
    @Req() req: Request,
    @Body() commentData: likeUnlikeCommentDto,
  ) {
    return this.postService.likeUnlikeComments(req, commentData);
  }

  @UseGuards(AuthGuard)
  @Get('/comment/:commentId/reactions')
  async commentReactions(
    @Req() req: Request,
    @Param('commentId') commentId: string,
  ) {
    return this.postService.commentReactions(req, commentId);
  }

  @UseGuards(AuthGuard)
  @Post('bookmark')
  async bookmarkpost(@Req() req: Request, @Body() postData: bookmarkPostDto) {
    return this.postService.bookmarkPost(req, postData);
  }

  @UseGuards(AuthGuard)
  @Get('bookmark')
  async bookmark(@Req() req: Request) {
    return this.postService.viewBookmarks(req);
  }

  @UseGuards(AuthGuard)
  @Post('story')
  async addstory(@Req() req: Request, @Body() postData: addStoryDto) {
    return this.postService.addStory(req, postData);
  }

  @UseGuards(AuthGuard)
  @Get('stories')
  async getstories(@Req() req: Request) {
    return this.postService.getStories(req);
  }

  @UseGuards(AuthGuard)
  @Post('story-like-unlike')
  async likeunlikestory(
    @Req() req: Request,
    @Body() likeUnlikeStoryData: likeUnlikeStoryDto,
  ) {
    return this.postService.likeUnlikeStory(req, likeUnlikeStoryData);
  }

  @UseGuards(AuthGuard)
  @Delete('comment/:id')
  async deletecomment(@Req() req: Request, @Param('id') id: number) {
    return this.postService.deleteComment(req, id);
  }

  @UseGuards(AuthGuard)
  @Get('/:id')
  async getpostbyid(@Req() req: Request, @Param() params: IDParamDTO) {
    return this.postService.getPostById(req, params.id);
  }

  @UseGuards(AuthGuard)
  @Delete('/:id')
  async deletepost(@Req() req: Request, @Param() params: IDParamDTO) {
    return this.postService.deletePost(req, params.id);
  }

  @UseGuards(AuthGuard)
  @Get('/:postId/reactions')
  async postReactions(@Req() req: Request, @Param('postId') postId: string) {
    return this.postService.postReactions(req, postId);
  }

  @UseGuards(AuthGuard)
  @Get('/:postId/comment-list')
  async postcomments(@Req() req: Request, @Param('postId') postId: string) {
    return this.postService.postComments(req, postId);
  }

  @UseGuards(AuthGuard)
  @Delete('story/:id')
  async deletestory(@Req() req: Request, @Param('id') id: number) {
    return this.postService.deleteStory(req, id);
  }

  @UseGuards(AuthGuard)
  @Get('user-posts/:id')
  async userposts(@Req() req: Request, @Param('id') userId: number) {
    return this.postService.userPosts(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('user-podcasts/:id')
  async userpodcasts(@Req() req: Request, @Param('id') userId: number) {
    return this.postService.userPodcasts(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('user-comments/:id')
  async usercomments(@Req() req: Request, @Param('id') userId: number) {
    return this.postService.userComments(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('user-reposts/:id')
  async userreposts(@Req() req: Request, @Param('id') userId: number) {
    return this.postService.userReposts(req, userId);
  }

  @UseGuards(AuthGuard)
  @Get('posted-images/:id')
  async userPostedImages(@Req() req: Request, @Param('id') userId: number) {
    return this.postService.userPostedImages(req, userId);
  }
}
