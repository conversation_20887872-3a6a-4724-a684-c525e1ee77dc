# Profile Visibility Fix Documentation

## Issue Description

When sending profile visibility settings with only one option and `isEnabled: false`, the system was not properly setting the other option to `isEnabled: true` to maintain mutual exclusivity.

### Problem Example

**Input:**
```json
{
  "profileViewingOptions": [
    {
      "Name": "Your name headline",
      "isEnabled": false
    }
  ]
}
```

**Expected Output:**
```json
{
  "subMenuDetails": [
    {
      "settingName": "Profile viewing options",
      "settingDescription": "Control what profile information is visible",
      "settings": [
        {
          "Name": "Your name headline",
          "isEnabled": false
        },
        {
          "Name": "Private profile character",
          "isEnabled": true
        }
      ]
    }
  ]
}
```

**Actual Output (Before Fix):**
The system was not automatically setting "Private profile character" to `isEnabled: true`.

## Root Cause

The issue was in the logic of both:
1. `validateAndCorrectProfileVisibilityOptions` function in `src/Modules/user/helper/profileVisibilityValidator.ts`
2. `mergeProfileVisibilityOptions` method in `src/Modules/user/services/userSettings.service.ts`

The logic was not properly handling the case where only one option was provided with `isEnabled: false`.

## Solution

### 1. Enhanced Logic in `validateAndCorrectProfileVisibilityOptions`

The key issue was that when only partial data is sent (e.g., only one option), the system didn't know about the missing options. The fix ensures all required options are present before processing.

**Key Changes:**

1. **Ensure All Required Options Exist:**
```typescript
// For profileViewingOptions - ensure both options exist
if (fieldName === 'profileViewingOptions') {
  const hasNameHeadline = options.some((o: any) => o.Name === "Your name headline");
  const hasPrivateCharacter = options.some((o: any) => o.Name === "Private profile character");
  
  if (!hasNameHeadline) {
    options.push({ Name: "Your name headline", isEnabled: false });
  }
  if (!hasPrivateCharacter) {
    options.push({ Name: "Private profile character", isEnabled: false });
  }
}
```

2. **Enhanced Logic Flow:**
```typescript
// If no option was explicitly set to true, check if any option was explicitly set to false
if (!hasExplicitTrue) {
  let hasExplicitFalse = false;
  for (let i = 0; i < options.length; i++) {
    if (options[i].isEnabled === false) {
      hasExplicitFalse = true;
      // Make the other option true (if there are only 2 options, make the other one true)
      trueOptionIndex = i === 0 ? 1 : 0;
      break;
    }
  }
  
  // If no explicit false was found, keep the first option as true (default behavior)
  if (!hasExplicitFalse) {
    trueOptionIndex = 0;
  }
}
```

### 2. Enhanced Logic in `mergeProfileVisibilityOptions`

The merge function also needed to ensure all required options exist when handling partial updates.

**Key Changes:**

1. **Ensure All Required Options Exist:**
```typescript
// Ensure we have all required options based on the current options structure
// This handles the case where only partial data is sent
if (currentOptions.length === 2) {
  // Profile viewing options - ensure both options exist
  const hasNameHeadline = currentOptionsMap.has("Your name headline");
  const hasPrivateCharacter = currentOptionsMap.has("Private profile character");
  
  if (!hasNameHeadline) {
    currentOptionsMap.set("Your name headline", { Name: "Your name headline", isEnabled: false });
  }
  if (!hasPrivateCharacter) {
    currentOptionsMap.set("Private profile character", { Name: "Private profile character", isEnabled: false });
  }
}
```

2. **Enhanced Logic Flow:**
```typescript
// If no option was explicitly set to true, check if any option was explicitly set to false
if (!hasExplicitTrue) {
  for (let i = 0; i < mergedOptions.length; i++) {
    if (mergedOptions[i].isEnabled === false) {
      hasExplicitFalse = true;
      // Make the other option true (if there are only 2 options, make the other one true)
      trueOptionIndex = i === 0 ? 1 : 0;
      break;
    }
  }
}

// If no explicit true or false was found, keep the first option as true (default behavior)
if (!hasExplicitTrue && !hasExplicitFalse) {
  trueOptionIndex = 0;
}
```

## Key Changes

### 1. Added `hasExplicitFalse` Flag
- Tracks whether any option was explicitly set to `false`
- Ensures proper handling of partial updates

### 2. Improved Default Behavior
- When no explicit `true` or `false` values are provided, defaults to first option as `true`
- Maintains backward compatibility

### 3. Enhanced Logic Flow
- First checks for explicit `true` values
- Then checks for explicit `false` values
- Finally applies default behavior if neither is found

## Testing

### Test Cases Covered

1. **Legacy Format - Single Option with `isEnabled: false`**
   ```json
   {
     "profileViewingOptions": [
       {
         "Name": "Your name headline",
         "isEnabled": false
       }
     ]
   }
   ```
   **Expected:** "Private profile character" should be set to `true`

2. **Legacy Format - Single Option with `isEnabled: true`**
   ```json
   {
     "profileViewingOptions": [
       {
         "Name": "Private profile character",
         "isEnabled": true
       }
     ]
   }
   ```
   **Expected:** "Your name headline" should be set to `false`

3. **New Format - Single Option with `isEnabled: false`**
   ```json
   {
     "subMenuDetails": [
       {
         "settingName": "Profile viewing options",
         "settings": [
           {
             "Name": "Your name headline",
             "isEnabled": false
           }
         ]
       }
     ]
   }
   ```
   **Expected:** "Private profile character" should be set to `true`

## Files Modified

1. **`src/Modules/user/helper/profileVisibilityValidator.ts`**
   - Enhanced `validateAndCorrectProfileVisibilityOptions` function
   - Improved logic for handling partial updates

2. **`src/Modules/user/services/userSettings.service.ts`**
   - Enhanced `mergeProfileVisibilityOptions` method
   - Added proper handling of explicit `false` values

## API Endpoints Affected

- `POST /api/v1/profile-visibility/save` (Legacy format)
- `POST /api/v1/profile-visibility/save` (New unified format)

## Backward Compatibility

✅ **Fully Backward Compatible**
- All existing functionality continues to work
- No breaking changes to API contracts
- Existing data remains valid

## Validation Rules

The fix ensures that for each profile visibility setting group:

1. **Exactly one option** must have `isEnabled: true`
2. **All other options** in the same group must have `isEnabled: false`
3. **Automatic correction** is applied when these rules are violated
4. **Partial updates** are properly handled with mutual exclusivity

## Example Usage

### Frontend Implementation

```javascript
// Send only the option you want to disable
const response = await fetch('/api/v1/profile-visibility/save', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    profileViewingOptions: [
      {
        Name: "Your name headline",
        isEnabled: false
      }
    ]
  })
});

// The system will automatically set "Private profile character" to true
const result = await response.json();
console.log(result.data); // Shows both options with correct states
```

### Expected Response

```json
{
  "success": true,
  "message": "Profile visibility settings updated successfully",
  "data": {
    "userSettings": {
      "profileVisibility": {
        "profileViewingOptions": [
          {
            "Name": "Your name headline",
            "isEnabled": false
          },
          {
            "Name": "Private profile character",
            "isEnabled": true
          }
        ]
      }
    }
  }
}
```

## Conclusion

This fix ensures that profile visibility settings maintain proper mutual exclusivity while supporting partial updates. When a user sends only one option with `isEnabled: false`, the system automatically sets the other option to `isEnabled: true`, providing a seamless user experience. 