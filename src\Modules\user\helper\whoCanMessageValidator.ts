import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { Model } from 'mongoose';
import { WhoAreYouSlug, WhoCanMessageSlugEnum } from 'src/common/constant/enum';
import {
  ConnectionInfo,
  ConnectionInfoDocument,
} from 'src/Models/connectionInfo.schema';
import {
  FollowerInfo,
  followerInfoDocument,
} from 'src/Models/followerInfo.schema';
import { People, PeopleDocument, StatusEnum } from 'src/Models/peoples.schema';

@Injectable()
export class WhoCanMessageValidatorService {
  constructor(
    @InjectModel(FollowerInfo.name)
    private followerInfoModel: Model<followerInfoDocument>,
    @InjectModel(ConnectionInfo.name)
    private connectionInfoModel: Model<ConnectionInfoDocument>,
    @InjectModel(People.name)
    private peopleModel: Model<PeopleDocument>,
  ) {}

  async validate(currentUser: any, targetUser: any) {
    const targetUserId = targetUser._id.toString();
    const currentUserId = currentUser._id.toString();

    const currentUserSignUpData = currentUser.signUpData || [];
    const targetUserSignUpData = targetUser.signUpData || [];

    const whoCanMessageData = targetUserSignUpData.filter(
      (item) => item.itemId.title === 'who_can_message' && item.isSelected,
    );

    let message = '';
    for (const item of whoCanMessageData) {
      const {
        itemId: { slug },
      } = item;

      let isValid = {
        message: '',
        status: false,
      };

      switch (slug) {
        case WhoCanMessageSlugEnum.ANYONE:
          return { message: 'Validation successful', status: true };

        case WhoCanMessageSlugEnum.CONTACT_ME_ONLY_THROUGH_REPRESENTATIVE:
          return await this.isContactThroughRep();

        case WhoCanMessageSlugEnum.FOLLOWER:
          isValid = await this.isFollower(currentUserId, targetUserId);
          break;

        case WhoCanMessageSlugEnum.ALL_UNION_MEMBER:
          isValid = await this.isSameUnion(
            currentUserSignUpData,
            targetUserSignUpData,
          );
          break;
        case WhoCanMessageSlugEnum.MEMBERS_IN_MY_UNION:
          isValid = await this.isMemberInMyUnion(currentUserId, targetUserId);
          break;

        case WhoCanMessageSlugEnum.AFFILIATE_MEMBER:
        case WhoCanMessageSlugEnum.MEMBER_AFFILIATE_ORGANIZATION_BUSINESS:
          isValid = await this.isAffiliateMember(currentUserSignUpData);
          break;

        case WhoCanMessageSlugEnum.STUDENT:
          isValid = await this.isStudent(currentUserSignUpData);
          break;

        case WhoCanMessageSlugEnum.AUDIENCE_FANS:
          isValid = await this.isAudienceFan(currentUserSignUpData);
          break;

        case WhoCanMessageSlugEnum.UNION_MEMBER:
          isValid = await this.isUnionMember(currentUserSignUpData);
          break;

        case WhoCanMessageSlugEnum.MY_CONNECTION:
          isValid = await this.isConnection(currentUserId, targetUserId);
          break;

        case WhoCanMessageSlugEnum.ONLY_EMPLOYERS_HIRERS_CAN_MESSAGE:
          isValid = await this.isEmployer(currentUser);
          break;

        case WhoCanMessageSlugEnum.NON_UNION_INDIVIDUALS:
          isValid = await this.isNonUnion(currentUserSignUpData);
          break;

        case WhoCanMessageSlugEnum.ALUMNI:
          isValid = await this.isAlumni(currentUserId, targetUserId);
          break;

        case WhoCanMessageSlugEnum.PROFESSOR_TEACHER_INSTRUCTOR_COACHE:
          isValid = await this.isProfessorOrCoach(currentUserSignUpData);
          break;

        default:
          console.warn(`Unknown slug: ${slug}`);
          isValid = {
            message: `You can not message`,
            status: false,
          };
      }

      if (isValid.status) {
        return isValid;
      } else {
        message = isValid.message;
      }
    }

    return { message: message, status: false };
  }

  private async isFollower(
    userId: string,
    targetId: string,
  ): Promise<{ message: string; status: boolean }> {
    const followerInfo = await this.followerInfoModel
      .findOne({
        followerId: new mongoose.Types.ObjectId(userId.toString()),
        followingId: new mongoose.Types.ObjectId(targetId.toString()),
        status: StatusEnum.ACCEPT,
      })
      .exec();

    if (!followerInfo) {
      return {
        message: 'You are not a follower of this user',
        status: false,
      };
    }

    return {
      message: 'You are a follower of this user',
      status: true,
    };
  }

  private async isSameUnion(
    currentUserSignUpData: any,
    targetUserSignUpData: any,
  ): Promise<{ status: boolean; message: string }> {
    const currentUserWhoAreYou = currentUserSignUpData?.find(
      (item) => item.itemId.slug === WhoAreYouSlug.UNION_1,
    );
    const targetUserWhoAreYou = targetUserSignUpData?.find(
      (item) => item.itemId.slug === WhoAreYouSlug.UNION_1,
    );

    if (!currentUserWhoAreYou || !targetUserWhoAreYou) {
      return { status: false, message: 'Only same union members can message' };
    }

    if (currentUserWhoAreYou.isSelected && targetUserWhoAreYou.isSelected) {
      return { status: true, message: 'Validation Successfull' };
    }

    return { status: false, message: 'Only same union members can message' };
  }

  private async isMemberInMyUnion(
    currentUserId: string,
    targetUserId: string,
  ): Promise<{ status: boolean; message: string }> {
    const currentUserunions = await this.peopleModel
      .find({
        childUserId: new mongoose.Types.ObjectId(currentUserId.toString()),
        status: StatusEnum.ACCEPT,
      })
      .exec();

    const targetUserUnions = await this.peopleModel
      .find({
        childUserId: new mongoose.Types.ObjectId(targetUserId.toString()),
        status: StatusEnum.ACCEPT,
      })
      .exec();

    // check any common union match
    for (const currentUnion of currentUserunions) {
      for (const targetUnion of targetUserUnions) {
        if (
          currentUnion.parentUserId.toString() ===
          targetUnion.parentUserId.toString()
        ) {
          return { status: true, message: 'Validation Successfull' };
        }
      }
    }

    return { status: false, message: 'Only members in my union can message' };
  }

  private async isAffiliateMember(
    currentUserSignUpData: any[],
  ): Promise<{ status: boolean; message: string }> {
    const whoAreYou = currentUserSignUpData.find(
      (item) => item.itemId.slug === WhoAreYouSlug.AFFILIATE_MEMBER_1,
    );

    if (!whoAreYou || !whoAreYou.isSelected) {
      return { status: false, message: 'Only affiliate members can message' };
    }

    return { status: whoAreYou.isSelected, message: 'Validation Successfull' };
  }

  private async isStudent(
    currentUserSignUpData: any[],
  ): Promise<{ status: boolean; message: string }> {
    const whoAreYou = currentUserSignUpData.find(
      (item) => item.itemId.slug === WhoAreYouSlug.STUDENT_1,
    );

    if (!whoAreYou || !whoAreYou.isSelected) {
      return { status: false, message: 'Only students can message' };
    }
    return { status: whoAreYou.isSelected, message: 'Validation Successfull' };
  }

  private async isAudienceFan(
    currentUserSignUpData: any[],
  ): Promise<{ status: boolean; message: string }> {
    const whoAreYou = currentUserSignUpData.find(
      (item) => item.itemId.slug === WhoAreYouSlug.AUDIENCE_FANS_1,
    );
    if (!whoAreYou || !whoAreYou.isSelected) {
      return { status: false, message: 'Only audience or fans can message' };
    }

    return { status: whoAreYou.isSelected, message: 'Validation Successfull' };
  }

  private async isUnionMember(
    currentUserSignUpData: any[],
  ): Promise<{ status: boolean; message: string }> {
    const whoAreYou = currentUserSignUpData.find(
      (item) => item.itemId.slug === WhoAreYouSlug.UNION_MEMBER_1,
    );

    if (!whoAreYou || !whoAreYou.isSelected) {
      return { status: false, message: 'Only union members can message' };
    }
    return { status: whoAreYou.isSelected, message: 'Validation Successfull' };
  }

  private async isConnection(
    userId: string,
    targetId: string,
  ): Promise<{ status: boolean; message: string }> {
    const connectionInfo = await this.connectionInfoModel
      .findOne({
        $or: [
          {
            userId: new mongoose.Types.ObjectId(userId.toString()),
            connectionWithId: new mongoose.Types.ObjectId(targetId.toString()),
          },
          {
            userId: new mongoose.Types.ObjectId(targetId.toString()),
            connectionWithId: new mongoose.Types.ObjectId(userId.toString()),
          },
        ],
        status: StatusEnum.ACCEPT,
      })
      .exec();

    if (!connectionInfo) {
      return {
        message: 'Only connections can message',
        status: false,
      };
    }

    return {
      message: 'Validation Successfull',
      status: true,
    };
  }

  private async isContactThroughRep(): Promise<{
    status: boolean;
    message: string;
  }> {
    return {
      message: 'Please contact my Talent Representative',
      status: false,
    };
  }

  private async isEmployer(
    targetUser: any,
  ): Promise<{ status: boolean; message: string }> {
    return targetUser.hirerEmployerVerifiedStatus === StatusEnum.ACCEPT
      ? {
          message: 'Validation Successfull',
          status: true,
        }
      : {
          message: 'Only employers or hirers can message',
          status: false,
        };
  }

  private async isNonUnion(
    currentUserSignUpData: any,
  ): Promise<{ status: boolean; message: string }> {
    const whoAreYou = currentUserSignUpData.find(
      (item) => item.itemId.slug === WhoAreYouSlug.NON_UNION_HOBBYISTS_1,
    );

    if (!whoAreYou || !whoAreYou.isSelected) {
      return {
        status: false,
        message: 'Only non-union individuals can message',
      };
    }
    return { status: whoAreYou.isSelected, message: 'Validation Successfull' };
  }

  //   private async isAffiliateOrgMember(
  //     currentUserSignUpData: any[],
  //   ): Promise<boolean> {
  //     const whoAreYou = currentUserSignUpData.find(
  //       (item) => item.itemId.slug === WhoAreYouSlug.AFFILIATE_ORGANIZATION_1,
  //     );

  //     if (!whoAreYou || !whoAreYou.isSelected) {
  //       return false;
  //     }
  //     return whoAreYou.isSelected;
  //   }

  private async isAlumni(
    userId: string,
    targetId: string,
  ): Promise<{ status: boolean; message: string }> {
    const alumniRecord = await this.peopleModel
      .findOne({
        parentUserId: new mongoose.Types.ObjectId(targetId),
        childUserId: new mongoose.Types.ObjectId(userId),
        isAlumni: true,
      })
      .exec();

    if (!alumniRecord) {
      return { status: false, message: 'Only alumni can message' };
    }

    return { status: true, message: 'Validation Successfull' };
  }

  private async isProfessorOrCoach(
    currentUserSignUpData: any[],
  ): Promise<{ status: boolean; message: string }> {
    const whoAreYou = currentUserSignUpData.find(
      (item) => item.itemId.slug === WhoAreYouSlug.PRO_TEA_INT_COACH_1,
    );

    if (!whoAreYou || !whoAreYou.isSelected) {
      return {
        status: false,
        message:
          'Only professors, teachers, instructors, or coaches can message',
      };
    }
    return { status: whoAreYou.isSelected, message: 'Validation Successfull' };
  }
}
