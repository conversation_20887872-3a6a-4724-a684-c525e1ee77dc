# Profile Visibility - Fresh Implementation

## Overview

This is a fresh implementation of profile visibility logic with a simple, unified approach for all profile visibility types.

## Simple Rule

**For ALL profile visibility types (2 to 6 subtypes):**
- **If any subtype is set to `true`** → All other subtypes become `false`
- **If any subtype is set to `false`** → All other subtypes become `true`

**Important:** User's explicit request takes priority over existing database state.

## Implementation

### Core Logic Function

```typescript
function applySimpleProfileVisibilityLogic(options: any[]) {
  if (!options || options.length === 0) return;

  let hasTrue = false;
  let hasFalse = false;
  let trueIndex = -1;
  let falseIndex = -1;

  // Check what was explicitly set
  for (let i = 0; i < options.length; i++) {
    if (options[i].isEnabled === true) {
      hasTrue = true;
      trueIndex = i;
    } else if (options[i].isEnabled === false) {
      hasFalse = true;
      falseIndex = i;
    }
  }

  // Apply the simple rule
  if (hasTrue) {
    // If any item is true, set all others to false
    options.forEach((option, index) => {
      option.isEnabled = index === trueIndex;
    });
  } else if (hasFalse) {
    // If any item is false, set all others to true
    options.forEach((option, index) => {
      option.isEnabled = index !== falseIndex;
    });
  } else {
    // If nothing was explicitly set, set first item to true and others to false
    options.forEach((option, index) => {
      option.isEnabled = index === 0;
    });
  }
}
```

## Examples

### Example 1: Setting One Subtype to True

**Frontend Request:**
```json
{
  "activeStatusOptions": [
    {
      "Name": "Everyone",
      "isEnabled": true
    }
  ]
}
```

**Result:**
```json
{
  "activeStatusOptions": [
    {
      "Name": "Everyone",
      "isEnabled": true
    },
    {
      "Name": "My Connections",
      "isEnabled": false
    },
    {
      "Name": "People I Follow",
      "isEnabled": false
    },
    {
      "Name": "No One",
      "isEnabled": false
    }
  ]
}
```

### Example 2: Setting One Subtype to False

**Frontend Request:**
```json
{
  "profileDiscoveryOptions": [
    {
      "Name": "People I Follow",
      "isEnabled": false
    }
  ]
}
```

**Result:**
```json
{
  "profileDiscoveryOptions": [
    {
      "Name": "Everyone",
      "isEnabled": true
    },
    {
      "Name": "My Connections",
      "isEnabled": true
    },
    {
      "Name": "People I Follow",
      "isEnabled": false
    },
    {
      "Name": "No One",
      "isEnabled": true
    }
  ]
}
```

**Note:** Even if the database previously had `"No One"` set to `true`, the user's explicit request for `"People I Follow"` to be `false` takes priority and makes all other options `true`.

### Example 3: Profile Viewing Options (2 subtypes)

**Frontend Request:**
```json
{
  "profileViewingOptions": [
    {
      "Name": "Private profile character",
      "isEnabled": true
    }
  ]
}
```

**Result:**
```json
{
  "profileViewingOptions": [
    {
      "Name": "Your name headline",
      "isEnabled": false
    },
    {
      "Name": "Private profile character",
      "isEnabled": true
    }
  ]
}
```

## Supported Profile Visibility Types

This logic applies to ALL profile visibility types:

1. **`profileViewingOptions`** - Profile viewing settings
2. **`profileDiscoveryOptions`** - Profile discovery settings
3. **`activeStatusOptions`** - Active status settings
4. **Any other profile visibility types** with 2-6 subtypes

## API Endpoints

The fresh logic is applied in these endpoints:

- `POST /api/v1/profile-visibility/save` (Legacy format)
- `POST /api/v1/profile-visibility/save` (New unified format)

## Frontend Implementation

### Best Practices

1. **Send Only Changed Options**: Frontend can send only the options that need to be changed
2. **System Handles the Rest**: The backend automatically applies the simple rule
3. **No Need to Send All Options**: The system will handle missing options

### Example Frontend Code

```javascript
// Send only the option you want to change
const response = await fetch('/api/v1/profile-visibility/save', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    activeStatusOptions: [
      {
        Name: "Everyone",
        isEnabled: false
      }
    ]
  })
});

// The system automatically:
// 1. Sets "My Connections", "People I Follow", "No One" to true
// 2. Returns complete data with the simple rule applied
const result = await response.json();
```

## Benefits

### For Frontend Developers
- **Simple Logic**: Easy to understand and implement
- **Consistent Behavior**: Same rule for all profile visibility types
- **Flexible Input**: Can send partial data, system handles the rest
- **User Priority**: User's explicit requests always take priority

### For Backend
- **Clean Code**: Simple, maintainable logic
- **Scalable**: Works with any number of subtypes (2-6)
- **Robust**: Handles edge cases gracefully
- **Priority Logic**: User requests override existing database state

## Error Handling

The system handles these edge cases:

1. **Multiple True Values**: Uses the first one, sets others to false
2. **Multiple False Values**: Uses the first one, sets others to true
3. **No Explicit Values**: Sets first item to true, others to false
4. **Missing Options**: Gracefully handles partial data
5. **Invalid Data**: Handles malformed input

## Conclusion

This fresh implementation provides a simple, unified approach to profile visibility settings that works consistently across all types and subtypes. 