import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { JobApplicationStatus } from 'src/common/constant/enum';

export type JobApplicationDocument = JobApplication & Document;

@Schema({ timestamps: true, versionKey: false })
export class JobApplication {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'JobPost' })
  jobId: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: string;

  @Prop({ type: Date })
  appliedDate: Date;

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  resume: string;

  @Prop({ enum: JobApplicationStatus })
  status: string;
}

export const JobApplicationSchema =
  SchemaFactory.createForClass(JobApplication);
