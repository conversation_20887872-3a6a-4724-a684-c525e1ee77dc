import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { City, Country, State } from 'country-state-city';
import mongoose, { Model, PipelineStage } from 'mongoose';
import { ParsedQs } from 'qs';
import {
  InvitationType,
  NotificationsType,
  PostCollaboratorTagPeopleEnum,
  RedirectionType,
  Title,
} from 'src/common/constant/enum';
import { AwsService } from 'src/common/services/aws.service';
import { JwtAuthService } from 'src/common/services/jwt.service';
import { MailService, templates } from 'src/common/services/mail.service';
import { OtpGeneratorService } from 'src/common/services/otpgenerator.service';
import { PasswordService } from 'src/common/services/password.service';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import { bookmarkDocument } from 'src/Models/bookmark.schema';
import { feedbackDocument } from 'src/Models/feedback.schema';
import { followerInfoDocument } from 'src/Models/followerInfo.schema';
import {
  ConnectionInfoDocument,
  StatusEnum,
} from 'src/Models/connectionInfo.schema';
import { jobListDocument } from 'src/Models/jobList.schema';
import { likeDocument } from 'src/Models/like.schema';
import {
  NotificationDocument,
  Notifications,
} from 'src/Models/notification.schema';
import { otpLogDocument } from 'src/Models/otpLog.schema';
import { MediaEnum, postDocument, PostLabelEnum } from 'src/Models/post.schema';
import { profileViewDocument } from 'src/Models/profileViews';
import { professionArr, recordDocument } from 'src/Models/record.schema';
import { recordJobDocument } from 'src/Models/recordJob.schema';
import { selfIdentifyDocument } from 'src/Models/selfIdentify.schema';
import {
  GenderEnum,
  iAmMemberEnum,
  userDocument,
} from 'src/Models/user.schema';
import { userSignupDataDocument } from 'src/Models/userSignupData.schema';
import { getRecordsData } from 'src/Modules/admin/dtos/record.dtos';
import twilio from 'twilio';
import CONSTANT, {
  DUMMY_NUMBERS,
} from '../../../common/constant/common.constant';
import {
  affliatePagesListDto,
  countryCityState,
  editProfileDto,
  connectionRequestStatus,
  GetNotificationsDto,
  GetUserListing,
  hashtagSearchData,
  publicationDto,
  SignupData,
  fieldOfStudyDto,
  socialActivitiesDto,
  offeredServicesDto,
  causesDto,
  clientRequestDto,
  connectionRequestDto,
  clientRequestAcceptRejectDto,
  UpdateSelfIdentifyLanguagesDTO,
  professionDto,
  GetUserSelectionListingDto,
  memberVerificationDto,
  subscribeDto,
  hirerVerificationDTO,
  AcceptRejectfollowDto,
  getFollowersFollowingDto,
} from '../dtos/user.dtos';
import { StreamChatService } from './streamChat.service';
import { JobPostDocument } from 'src/Models/jobPost.schema';
import { UserRepository } from 'src/Repositories/user.repository';
import { NotificationService } from 'src/common/services/notification.service';
import { clientInfoDocument } from 'src/Models/clientsInfo.schema';
import { GroupDocument } from 'src/Models/group.schema';
import { PeopleDocument } from 'src/Models/peoples.schema';
import {
  affiliateBusinessList,
  affiliateOrganizationList,
  schoolTrainingList,
  unionList,
} from 'src/fakeAccountData';
import { SubscriberDocument } from 'src/Models/subscriber.schema';
import { NOTIFICATION_MESSAGES } from 'src/Custom/helpers/message.helper';
import { WhoCanMessageValidatorService } from '../helper/whoCanMessageValidator';
import { NotificationPermissionService } from './notificationPermission.service';

@Injectable()
export class UserService {
  constructor(
    @InjectModel('User') private readonly userModel: Model<userDocument>,
    @InjectModel('SelfIdentify')
    private readonly selfIdentifyModel: Model<selfIdentifyDocument>,
    @InjectModel('OtpLog') private readonly otpLogModel: Model<otpLogDocument>,
    @InjectModel('Post') private readonly postModel: Model<postDocument>,
    @InjectModel('ProfileViews')
    private readonly profileViews: Model<profileViewDocument>,
    @InjectModel('Like') private readonly likeModel: Model<likeDocument>,
    @InjectModel('FollowerInfo')
    private readonly followerInfoModel: Model<followerInfoDocument>,
    @InjectModel('ConnectionInfo')
    private readonly connectionInfoModel: Model<ConnectionInfoDocument>,
    @InjectModel('Bookmark')
    private readonly bookmarkModel: Model<bookmarkDocument>,
    @InjectModel('Feedback')
    private readonly feedbackModel: Model<feedbackDocument>,
    @InjectModel('JobList')
    private readonly jobListModel: Model<jobListDocument>,

    @InjectModel('Group')
    private readonly groupModel: Model<GroupDocument>,

    @InjectModel('Record')
    private readonly recordModel: Model<recordDocument>,
    @InjectModel('RecordJob')
    private readonly recordJobModel: Model<recordJobDocument>,
    @InjectModel('UserSignupData')
    private readonly userSignupDataModel: Model<userSignupDataDocument>,

    @InjectModel('JobPost')
    private readonly jobPostModel: Model<JobPostDocument>,

    @InjectModel('ClientInfo')
    private readonly clientInfoModel: Model<clientInfoDocument>,

    @InjectModel('Subscriber')
    private readonly subscriberModel: Model<SubscriberDocument>,

    @InjectModel('People')
    private readonly peopleModel: Model<PeopleDocument>,

    @InjectModel(Notifications.name)
    private readonly notificationModel: Model<NotificationDocument>,
    private readonly passwordService: PasswordService,
    private readonly jwtService: JwtAuthService,
    private readonly otpGeneratorService: OtpGeneratorService,
    private readonly mailService: MailService,
    private readonly awsService: AwsService,
    private readonly streamChatService: StreamChatService,
    private readonly configService: ConfigService,
    private notificationService: NotificationService,
    private whoCanMessageValidatorService: WhoCanMessageValidatorService,
    private readonly notificationPermissionService: NotificationPermissionService,

    private readonly userRepository: UserRepository,
  ) {}

  public async signUp(signupData) {
    try {
      // if (
      //   !signupData.country &&
      //   signupData.iAmMember !==
      //     iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY
      // ) {
      //   throw new HttpException(
      //     CONSTANT.REQUIRED('Country'),
      //     HttpStatus.NOT_FOUND,
      //   );
      // }

      const isEmailExist = await this.userModel.findOne({
        email: signupData.email.toLowerCase(),
      });
      if (isEmailExist) {
        throw new HttpException(
          CONSTANT.ALREADY_EXIST('Email'),
          HttpStatus.FORBIDDEN,
        );
      }

      // const isPhoneExist = await this.userModel.findOne({
      //   phone: signupData.phone,
      // });
      // if (isPhoneExist) {
      //   throw new HttpException(
      //     CONSTANT.ALREADY_EXIST('Phone'),
      //     HttpStatus.FORBIDDEN,
      //   );
      // }

      const isUserNameExist = await this.userModel.findOne({
        userName: signupData.userName.toLowerCase(),
      });
      if (isUserNameExist) {
        throw new HttpException(
          CONSTANT.ALREADY_EXIST('UserName'),
          HttpStatus.FORBIDDEN,
        );
      }

      const hashPassword = await this.passwordService.hashPassword(
        signupData.password,
      );

      const hirerRecord = await this.userSignupDataModel.findOne({
        slug: 'employer_1',
        title: 'more_about_you',
      });

      signupData.signUpData = signupData.signUpData.map((item) => {
        if (item.itemId.toString() === hirerRecord._id.toString()) {
          return {
            itemId: item.itemId,
            isSelected: false,
          };
        }
        return item;
      });

      const data = {
        ...signupData,
        email: signupData.email.toLowerCase(),
        password: hashPassword,
        userProfileAboutYou: {
          ...(signupData.userProfileAboutYou || {}),
          website: signupData.website,
        },
        SocialMediaData: {
          ...(signupData.SocialMediaData || {}),
          website: signupData.website,
        },
      };

      delete data.website;

      const checkForFakeAccount = await this.userModel.findOne({
        businessOrganizationName: data.businessOrganizationName?.trim(),
        isFakeAccount: true,
      });

      let userData: any = null;

      if (checkForFakeAccount) {
        // convert account to real account
        userData = await this.userModel.findOneAndUpdate(
          { _id: checkForFakeAccount._id },
          {
            $set: {
              ...data,
              isFakeAccount: false,
            },
          },
          { new: true, runValidators: true },
        );
      } else {
        data.isFakeAccount = false;
        userData = await new this.userModel(data).save();
      }

      //Create user in stream for chat
      const chatObj = {
        id: `${userData._id}`,
        profile_image: userData.profileImage,
        name: signupData.userName ?? userData.businessOrganizationName,
        userName: userData.userName ?? userData.businessOrganizationName,
        businessOrganizationName: userData.businessOrganizationName
          ? userData.businessOrganizationName
          : null,
      };
      await this.streamChatService.createOrUpdateUserForChat(chatObj);

      userData = userData.toObject();
      // Generating JWT token
      const payload = { userName: userData.userName, id: userData._id };
      const token = await this.jwtService.generateJwtToken(payload);
      userData.token = token;
      delete userData.password;
      delete userData.isEmailVerified;
      delete userData.__v;
      delete userData.signUpData;
      delete userData.publications;
      delete userData.affiliatePages;
      delete userData.fieldOfStudy;
      delete userData.socialActivities;
      delete userData.offeredServices;
      delete userData.causes;
      delete userData.contactInfo;

      return successResponse(userData, CONSTANT.SIGNUP, HttpStatus.CREATED);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async signIn(signinData) {
    try {
      // let user: any = await this.userModel
      //   .findOne({ email: signinData.email })
      //   .select('-isEmailVerified -__v');

      const userArr: any = await this.userModel.aggregate([
        {
          $match: {
            isFakeAccount: false,
            email: signinData.email.toLowerCase(),
          },
        },
        {
          $unwind: {
            path: '$aboutYou.schoolTrainingFacility',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: '$aboutYou.schoolTrainingFacility.iAm',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'aboutYou.schoolTrainingFacility.iAm.itemId',
            foreignField: '_id',
            as: 'aboutYouDetails',
          },
        },
        {
          $unwind: {
            path: '$aboutYouDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $set: {
            'aboutYou.schoolTrainingFacility.iAm.itemId': {
              _id: '$aboutYouDetails._id',
              title: '$aboutYouDetails.title',
              slug: '$aboutYouDetails.slug',
              itemText: '$aboutYouDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: {
              itemText: '$aboutYou.schoolTrainingFacility.itemText',
              idNumber: '$aboutYou.schoolTrainingFacility.idNumber',
              memberIdCard: '$aboutYou.schoolTrainingFacility.memberIdCard',
              memberDoc: '$aboutYou.schoolTrainingFacility.memberDoc',
              userId: '$_id',
              iAmMember: '$iAmMember',
              businessOrganizationName: '$businessOrganizationName',
              firstName: '$firstName',
              lastName: '$lastName',
              userName: '$userName',
              city: '$city',
              state: '$state',
              country: '$country',
              email: '$email',
              password: '$password',
              isEmailVerified: '$isEmailVerified',
              isNotificationOn: '$isNotificationOn',
              isEmailOn: '$isEmailOn',
              countryCode: '$countryCode',
              phone: '$phone',
              socialMedia: '$socialMedia',
              contactInfo: '$contactInfo',
              signUpData: '$signUpData',
              profileImage: '$profileImage',
              followers: '$followers',
              accountVerified: '$accountVerified',
              following: '$following',
              professions: '$professions',
              isFakeAccount: '$isFakeAccount',
              isFunding: '$isFunding',
              hirerEmployerVerifiedStatus: '$hirerEmployerVerifiedStatus',
              isMembershipVerified: '$isMembershipVerified',
              createdAt: '$createdAt',
              updatedAt: '$updatedAt',
            },
            iAm: {
              $push: '$aboutYou.schoolTrainingFacility.iAm',
            },
            aboutYou: {
              $first: '$aboutYou',
            },
          },
        },
        {
          $group: {
            _id: '$_id.userId',
            iAmMember: {
              $first: '$_id.iAmMember',
            },
            businessOrganizationName: {
              $first: '$_id.businessOrganizationName',
            },
            firstName: {
              $first: '$_id.firstName',
            },
            lastName: {
              $first: '$_id.lastName',
            },
            userName: {
              $first: '$_id.userName',
            },
            city: {
              $first: '$_id.city',
            },
            state: {
              $first: '$_id.state',
            },
            country: {
              $first: '$_id.country',
            },
            email: {
              $first: '$_id.email',
            },
            password: {
              $first: '$_id.password',
            },
            isEmailVerified: {
              $first: '$_id.isEmailVerified',
            },
            isNotificationOn: {
              $first: '$_id.isNotificationOn',
            },
            isEmailOn: {
              $first: '$_id.isEmailOn',
            },
            countryCode: {
              $first: '$_id.countryCode',
            },
            phone: {
              $first: '$_id.phone',
            },
            socialMedia: {
              $first: '$_id.socialMedia',
            },
            contactInfo: {
              $first: '$_id.contactInfo',
            },
            signUpData: {
              $first: '$_id.signUpData',
            },
            profileImage: {
              $first: '$_id.profileImage',
            },
            followers: {
              $first: '$_id.followers',
            },
            following: {
              $first: '$_id.following',
            },
            accountVerified: {
              $first: '$_id.accountVerified',
            },
            hirerEmployerVerifiedStatus: {
              $first: '$_id.hirerEmployerVerifiedStatus',
            },
            isMembershipVerified: {
              $first: '$_id.isMembershipVerified',
            },
            professions: {
              $first: '$_id.professions',
            },
            isFakeAccount: {
              $first: '$_id.isFakeAccount',
            },
            isFunding: {
              $first: '$_id.isFunding',
            },
            createdAt: {
              $first: '$_id.createdAt',
            },
            updatedAt: {
              $first: '$_id.updatedAt',
            },
            schoolTrainingFacility: {
              $push: {
                itemText: '$_id.itemText',
                idNumber: '$_id.idNumber',
                memberIdCard: '$_id.memberIdCard',
                memberDoc: '$_id.memberDoc',
                iAm: '$iAm',
              },
            },
            aboutYou: {
              $first: '$aboutYou',
            },
          },
        },
        {
          $set: {
            'aboutYou.schoolTrainingFacility': '$schoolTrainingFacility',
          },
        },
        {
          $unset: 'schoolTrainingFacility',
        },
        {
          $unwind: {
            path: '$signUpData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'signUpData.itemId',
            foreignField: '_id',
            as: 'signUpDetails',
          },
        },
        {
          $unwind: {
            path: '$signUpDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'signUpData.itemId': {
              _id: '$signUpDetails._id',
              title: '$signUpDetails.title',
              slug: '$signUpDetails.slug',
              itemText: '$signUpDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: {
              _id: '$_id',
              iAmMember: '$iAmMember',
              firstName: '$firstName',
              lastName: '$lastName',
              userName: '$userName',
              businessOrganizationName: '$businessOrganizationName',
              city: '$city',
              state: '$state',
              country: '$country',
              email: '$email',
              password: '$password',
              isEmailVerified: '$isEmailVerified',
              isNotificationOn: '$isNotificationOn',
              isEmailOn: '$isEmailOn',
              countryCode: '$countryCode',
              phone: '$phone',
              socialMedia: '$socialMedia',
              contactInfo: '$contactInfo',
              accountVerified: '$accountVerified',
              isMembershipVerified: '$isMembershipVerified',
              hirerEmployerVerifiedStatus: '$hirerEmployerVerifiedStatus',
              profileImage: '$profileImage',
              followers: '$followers',
              following: '$following',
              professions: '$professions',
              isFakeAccount: '$isFakeAccount',
              isFunding: '$isFunding',
              aboutYou: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ['$aboutYou.schoolTrainingFacility', null] },
                      { $eq: ['$aboutYou.schoolTrainingFacility', {}] },
                      {
                        $eq: [
                          '$aboutYou.schoolTrainingFacility.iAm.itemId',
                          null,
                        ],
                      },
                      {
                        $eq: [
                          '$aboutYou.schoolTrainingFacility.iAm.itemId',
                          {},
                        ],
                      },
                    ],
                  },
                  then: null,
                  else: '$aboutYou',
                },
              },
            },
            signUpData: {
              $push: {
                isSelected: '$signUpData.isSelected',
                itemId: '$signUpData.itemId',
              },
            },
          },
        },
        {
          $project: {
            _id: '$_id._id',
            iAmMember: '$_id.iAmMember',
            firstName: '$_id.firstName',
            lastName: '$_id.lastName',
            userName: '$_id.userName',
            businessOrganizationName: '$_id.businessOrganizationName',
            city: '$_id.city',
            state: '$_id.state',
            country: '$_id.country',
            email: '$_id.email',
            password: '$_id.password',
            isEmailVerified: '$_id.isEmailVerified',
            isNotificationOn: '$_id.isNotificationOn',
            isEmailOn: '$_id.isEmailOn',
            countryCode: '$_id.countryCode',
            phone: '$_id.phone',
            socialMedia: '$_id.socialMedia',
            contactInfo: '$_id.contactInfo',
            aboutYou: '$_id.aboutYou',
            accountVerified: '$_id.accountVerified',
            profileImage: '$_id.profileImage',
            followers: '$_id.followers',
            following: '$_id.following',
            professions: '$_id.professions',
            isFakeAccount: '$_id.isFakeAccount',
            isMembershipVerified: '$_id.isMembershipVerified',
            hirerEmployerVerifiedStatus: '$_id.hirerEmployerVerifiedStatus',
            isFunding: '$_id.isFunding',
            whoAreYou: {
              $filter: {
                input: '$signUpData',
                as: 'item',
                cond: {
                  $eq: ['$$item.itemId.title', 'who_are_you'],
                },
              },
            },
            moreAboutYou: {
              $filter: {
                input: '$signUpData',
                as: 'item',
                cond: {
                  $eq: ['$$item.itemId.title', 'more_about_you'],
                },
              },
            },
            openToPublic: {
              $filter: {
                input: '$signUpData',
                as: 'item',
                cond: {
                  $eq: ['$$item.itemId.title', 'profile_open_to'],
                },
              },
            },
            whoCanMessage: {
              $filter: {
                input: '$signUpData',
                as: 'item',
                cond: {
                  $eq: ['$$item.itemId.title', 'who_can_message'],
                },
              },
            },
          },
        },
        {
          $addFields: {
            'aboutYou.schoolTrainingFacility': {
              $filter: {
                input: '$aboutYou.schoolTrainingFacility',
                as: 'facility',
                cond: {
                  $gt: [
                    {
                      $size: {
                        $filter: {
                          input: '$$facility.iAm',
                          as: 'iAmItem',
                          cond: {
                            $ne: ['$$iAmItem.itemId', {}],
                          },
                        },
                      },
                    },
                    0,
                  ],
                },
              },
            },
          },
        },
      ]);

      const user = userArr[0];

      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const isPasswordMatched = await this.passwordService.verifyPassword(
        signinData.password,
        user.password,
      );
      if (!isPasswordMatched) {
        throw new HttpException(
          CONSTANT.INVALID_CREDENTIAL,
          HttpStatus.FORBIDDEN,
        );
      }

      const payload = { userName: user.userName, id: user._id };
      const token = await this.jwtService.generateJwtToken(payload);

      // user = user.toObject();
      user.token = token;
      delete user.password;

      return successResponse(user, CONSTANT.SIGNIN, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async userNameAvailability(userNameData) {
    try {
      // Regular expression to validate the username format
      const regex = /^[\p{L}0-9_.-]{3,15}$/u; // \p{L} matches any kind of letter from any language
      const isValid = regex.test(userNameData.userName);
      if (!isValid) {
        throw new HttpException(
          'Username must contain letters, numbers (0-9), underscores (_), hyphens (-), dots (.) and length between 3 to 15 characters.',
          HttpStatus.FORBIDDEN,
        );
      }
      // Normalize the input username to lowercase for case-insensitive comparison
      const inputUserName = userNameData.userName?.toLowerCase(); // Added normalization to lowercase

      // Fetch all users and compare usernames case-insensitively
      const allUser: any = await this.userModel.find({
        isFakeAccount: false,
      });

      const unameFound = allUser.filter(
        (i) => i.userName?.toLowerCase() === inputUserName, // Added toLowerCase() for case-insensitive comparison
      );

      if (unameFound.length !== 0) {
        throw new HttpException(
          CONSTANT.USERNAME_ALREADY_EXIST,
          HttpStatus.FORBIDDEN,
        );
      }

      return successResponse(null, CONSTANT.USERNAME_AVAILABLE, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async profileView(req, profileViewdata) {
    try {
      const { user: loggedInUser } = req;

      const isAlreadyViewed: any = await this.profileViews.findOne({
        userId: loggedInUser._id,
        viewerId: new mongoose.Types.ObjectId(profileViewdata?.viewerId),
      });
      if (!isAlreadyViewed) {
        await this.profileViews.create({
          viewerId: profileViewdata?.viewerId,
          userId: loggedInUser._id,
        });

        await this.userModel.updateOne(
          { _id: loggedInUser._id },
          { $inc: { 'analytics.viewCount': 1 } },
        );

        await this.userModel.updateOne(
          { _id: profileViewdata?.viewerId },
          { $inc: { 'analytics.viewByMeCount': 1 } },
        );
      }
      return successResponse(null, CONSTANT.PROFILE_VIEWED, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async sendOtp(sendOtpData) {
    try {
      let otp = await this.otpGeneratorService.generateOtp();

      const currentDate = new Date();
      const otpExpires = new Date(currentDate.getTime() + 5 * 60000).getTime();

      if (sendOtpData.type === 'mobile') {
        const ACCOUNT_SID = this.configService.get<string>('ACCOUNT_SID');
        const AUTH_TOKEN = this.configService.get<string>('AUTH_TOKEN');
        const client = twilio(ACCOUNT_SID, AUTH_TOKEN);
        const twilioServiceId: any =
          this.configService.get<string>('SERVICE_ID');

        if (DUMMY_NUMBERS.includes(sendOtpData.phone)) {
          otp = '123456';
        } else {
          const response = await client.verify
            .services(twilioServiceId)
            .verifications.create({
              to: `${sendOtpData.countryCode}${sendOtpData.phone}`,
              channel: 'sms',
            });

          // Send OTP using twilio
          if (!response) {
            throw new HttpException(
              CONSTANT.ERROR('Sending Otp'),
              HttpStatus.BAD_REQUEST,
            );
          }
        }

        const phoneExist: any = await this.otpLogModel.findOne({
          phone: sendOtpData.phone,
        });

        if (!phoneExist) {
          await this.otpLogModel.create({
            phone: sendOtpData.phone,
            isMatch: false,
            createdAt: currentDate,
          });
        } else {
          await this.otpLogModel.findOneAndUpdate(
            { phone: sendOtpData.phone },
            {
              isMatch: false,
              createdAt: currentDate,
            },
            {
              new: true,
              runValidators: true,
            },
          );
        }
      }

      if (sendOtpData.type === 'email') {
        const emailExist: any = await this.otpLogModel.findOne({
          email: sendOtpData.email.toLowerCase(),
        });

        if (!emailExist && sendOtpData.type === 'email') {
          const otpData: any = new this.otpLogModel({
            otp: otp,
            email: sendOtpData.email.toLowerCase(),
            isMatch: false,
            otpExpires: otpExpires,
            createdAt: currentDate,
            deletedAt: null,
          });
          await otpData.save();
        } else {
          await this.otpLogModel.findOneAndUpdate(
            { email: sendOtpData.email.toLowerCase() },
            {
              otp: otp,
              otpExpires: otpExpires,
              isMatch: false,
              createdAt: currentDate,
            },
            {
              new: true,
              runValidators: true,
            },
          );
        }
      }

      if (sendOtpData.type === 'email') {
        const template = templates.sendOtp({
          otp,
        });
        await this.mailService.SendMail(
          sendOtpData.email,
          'Verification mail',
          template,
        );
      }

      if (sendOtpData.type === 'forgot') {
        const user: any = await this.userModel.findOne({
          email: sendOtpData.email.toLowerCase(),
          isFakeAccount: false,
        });
        if (!user) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('User'),
            HttpStatus.NOT_FOUND,
          );
        }

        const emailExist: any = await this.otpLogModel.findOne({
          email: sendOtpData.email.toLowerCase(),
        });

        if (!emailExist) {
          const otpData: any = new this.otpLogModel({
            otp: otp,
            email: sendOtpData.email.toLowerCase(),
            isMatch: false,
            otpExpires: otpExpires,
            createdAt: currentDate,
            deletedAt: null,
          });
          await otpData.save();
        } else {
          await this.otpLogModel.findOneAndUpdate(
            { email: sendOtpData.email.toLowerCase() },
            {
              otp: otp,
              otpExpires: otpExpires,
              isMatch: false,
              createdAt: currentDate,
            },
            {
              new: true,
              runValidators: true,
            },
          );
        }
        const template = templates.forgotPassword({
          otp: otp,
          userName: user.userName,
        });
        await this.mailService.SendMail(
          sendOtpData.email,
          'Forgot Password',
          template,
        );
      }
      return successResponse(null, CONSTANT.SENT_MESSAGE('Otp'), HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async verifyOtp(verifyOtpData) {
    try {
      const currentDate = new Date();

      if (verifyOtpData.type === 'mobile') {
        const ACCOUNT_SID = this.configService.get<string>('ACCOUNT_SID');
        const AUTH_TOKEN = this.configService.get<string>('AUTH_TOKEN');
        const client = twilio(ACCOUNT_SID, AUTH_TOKEN);
        const twilioServiceId: any =
          this.configService.get<string>('SERVICE_ID');

        if (DUMMY_NUMBERS.includes(verifyOtpData.phone)) {
          verifyOtpData.otp = '123456';
        } else {
          // verify OTP using twilio
          const response = await client.verify.v2
            .services(twilioServiceId)
            .verificationChecks.create({
              to: `${verifyOtpData.countryCode}${verifyOtpData.phone}`,
              code: verifyOtpData.otp,
            });
          if (!response) {
            throw new HttpException(
              CONSTANT.ERROR('Verifying Otp'),
              HttpStatus.BAD_REQUEST,
            );
          }
        }

        // update isMatch: true in otpLog model
        await this.otpLogModel.findOneAndUpdate(
          { phone: verifyOtpData.phone },
          {
            $set: { isMatch: true },
          },
        );
      }

      if (verifyOtpData.type === 'email' || verifyOtpData.type === 'forgot') {
        const userOtp: any = await this.otpLogModel.findOne({
          email: verifyOtpData.email.toLowerCase(),
          otp: verifyOtpData.otp,
          isMatch: false,
        });

        if (!userOtp) {
          throw new HttpException(
            CONSTANT.OTP_MISMATCH,
            HttpStatus.BAD_REQUEST,
          );
        }

        if (currentDate > userOtp.otpExpires) {
          throw new HttpException(CONSTANT.OTP_EXPIRED, HttpStatus.BAD_REQUEST);
        }

        // update isMatch: true in otpLog model
        await this.otpLogModel.findOneAndUpdate(
          { email: verifyOtpData.email.toLowerCase() },
          {
            $set: { isMatch: true },
          },
        );

        if (verifyOtpData.type === 'email') {
          // update isEmailVerified: true in user model
          await this.userModel.findOneAndUpdate(
            { email: verifyOtpData.email.toLowerCase() },
            {
              $set: { isEmailVerified: true },
            },
          );
        }
      }
      return successResponse(null, CONSTANT.OTP_VERIFIED, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async resetPassword(resetPasswordData) {
    try {
      const user: any = await this.userModel.findOne({
        email: resetPasswordData.email.toLowerCase(),
        isFakeAccount: false,
      });
      if (!user) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.BAD_REQUEST,
        );
      }

      const hashPassword = await this.passwordService.hashPassword(
        resetPasswordData.password,
      );

      user.password = hashPassword;
      await user.save();

      return successResponse(null, CONSTANT.RESET_PASSWORD, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async countryStateCity(countryCityStateData) {
    try {
      let data, message;

      if (countryCityStateData.type === countryCityState.COUNTRY) {
        const countries = Country.getAllCountries();

        //purpose of plusAddPC fun. : some countries don't have +(plus) at starting of phonecode
        const plusAddPC = async (pc) => {
          if (!pc.phonecode.startsWith('+')) {
            const phone_code = (pc.phonecode = '+' + pc.phonecode);
            return phone_code;
          }
        };
        const countriesUpdate = countries.filter(plusAddPC);

        data = await countriesUpdate.map(({ name, isoCode, phonecode }) => ({
          name,
          isoCode,
          phonecode,
        })); //returns specific fields
        message = 'Countries';
      }

      if (countryCityStateData.type === countryCityState.STATE) {
        if (!countryCityStateData.isoCode) {
          throw new HttpException(
            CONSTANT.REQUIRED('Country isoCode'),
            HttpStatus.NOT_FOUND,
          );
        }

        const states = State.getStatesOfCountry(countryCityStateData.isoCode);

        data = await states.map(({ name, isoCode, countryCode }) => ({
          name,
          isoCode,
          countryCode,
        }));
        message = 'States';
      }

      if (countryCityStateData.type === countryCityState.CITY) {
        if (!countryCityStateData.countryCode) {
          throw new HttpException(
            CONSTANT.REQUIRED('CountryCode'),
            HttpStatus.NOT_FOUND,
          );
        }
        if (!countryCityStateData.isoCode) {
          throw new HttpException(
            CONSTANT.REQUIRED('State isoCode'),
            HttpStatus.NOT_FOUND,
          );
        }

        const cities = City.getCitiesOfState(
          countryCityStateData.countryCode,
          countryCityStateData.isoCode,
        );

        data = await cities.map(({ name, countryCode, stateCode }) => ({
          name,
          countryCode,
          stateCode,
        }));
        message = 'Cities';
      }

      return successResponse(
        data,
        CONSTANT.FETCHED_SUCCESSFULLY(message),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getSignupData(body: SignupData) {
    try {
      const { slug } = body;

      const data = await this.userSignupDataModel
        .find({
          parentSlug: { $in: slug },
        })
        .sort({ _id: 1 });

      return successResponse(
        data,
        CONSTANT.FETCHED_SUCCESSFULLY('SignupData'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getOneUser(req, id: string) {
    try {
      const loggedInUserId = req.user._id;
      const targetUserId = new mongoose.Types.ObjectId(id);

      const profile = await this.userRepository.findProfileById(
        id,
        loggedInUserId,
      );
      if (profile.length === 0) {
        return successResponse(
          null,
          CONSTANT.FETCHED_SUCCESSFULLY('User'),
          HttpStatus.OK,
        );
      }

      const response = profile[0];

      // Track profile view if not viewing own profile
      if (targetUserId.toString() !== loggedInUserId.toString()) {
        await this.handleProfileView(targetUserId, loggedInUserId);
      }

      const [
        jobPosts,
        selfIdentify,
        connectionRequestStatus,
        isFollowing,
        isSubscribed,
        clientInfo,
        member,
        latestGroup,
        latestPodcast,
      ] = await Promise.all([
        this.getLatestJobPost(id),
        this.getSelfIdentifySettingOfUser({ user: { _id: targetUserId } }).then(
          (res) => res.data,
        ),
        this.getConnectionStatus(loggedInUserId, targetUserId),
        this.getIsFollowing(loggedInUserId, targetUserId),
        this.getIsSubscribed(loggedInUserId, targetUserId),
        this.getLatestClientInfo(targetUserId),
        this.getPeoples({ query: { page: 1, perPage: 1 } }, id, true).then(
          (res) => res.data[0] ?? null,
        ),
        this.groupModel
          .findOne({ createdBy: targetUserId })
          .select('name description coverPhoto privacy createdAt sharableLink')
          .sort({ createdAt: -1 }),
        this.getLatestPodcast(id),
      ]);

      response.jobPosts = jobPosts;
      response.selfIdentify = selfIdentify;
      response.connectionRequestStatus = connectionRequestStatus;
      response.isFollowing = isFollowing;
      response.isSubscribed = isSubscribed;
      response.clientInfo = clientInfo;
      response.isUserLoggedIn =
        loggedInUserId._id?.toString() === targetUserId?.toString();
      response.member = member;
      response.group = latestGroup;
      response.podcast = latestPodcast;
      if (Object.keys(response.projects).length <= 1) {
        response.projects = null;
      }

      return successResponse(
        response,
        CONSTANT.FETCHED_SUCCESSFULLY('User'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  private async handleProfileView(userId, viewerId) {
    const alreadyViewed = await this.profileViews.findOne({
      userId: userId,
      viewerId: viewerId,
    });

    const user = await this.userModel.findById(userId);

    if (!alreadyViewed && user.isFakeAccount === false) {
      await this.profileViews.create({ viewerId: viewerId, userId: userId });
      await this.userModel.updateOne(
        { _id: userId },
        {
          $inc: { 'analytics.viewCount': 1 },
          'analytics.visibility': 'public',
        },
      );

      await this.userModel.updateOne(
        { _id: viewerId },
        {
          $inc: { 'analytics.viewByMeCount': 1 },
        },
      );
    }
  }

  private async getLatestJobPost(userId) {
    return this.jobPostModel
      .findOne({
        userId: new mongoose.Types.ObjectId(userId),
        isDeleted: false,
      })
      .populate([
        {
          path: 'userId',
          match: { isFakeAccount: false },
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        },
        {
          path: 'applications',
          options: { sort: { createdAt: -1 }, limit: 1 },
          populate: [
            {
              path: 'userId',
              match: { isFakeAccount: false },
              select:
                'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
            },
            {
              path: 'jobId',
              select: '-applications',
            },
          ],
        },
      ])
      .sort({ createdAt: -1 });
  }

  private async getConnectionStatus(userId, targetId) {
    const connection = await this.connectionInfoModel.findOne({
      $expr: {
        $or: [
          {
            $and: [
              { $eq: ['$userId', userId] },
              { $eq: ['$connectionWithId', targetId] },
            ],
          },
          {
            $and: [
              { $eq: ['$userId', targetId] },
              { $eq: ['$connectionWithId', userId] },
            ],
          },
        ],
      },
    });
    return connection?.status ?? null;
  }

  private async getIsFollowing(userId, targetId) {
    const follow = await this.followerInfoModel.findOne({
      $expr: {
        $and: [
          { $eq: ['$followerId', userId] },
          { $eq: ['$followingId', targetId] },
        ],
      },
    });
    // also sent status
    return follow?.status ?? null;
  }

  private async getIsSubscribed(userId, targetId) {
    const subscriber = await this.subscriberModel.findOne({
      $expr: {
        $and: [
          { $eq: ['$subscriberId', userId] },
          { $eq: ['$userId', targetId] },
        ],
      },
    });
    return !!subscriber;
  }

  private async getLatestClientInfo(userId) {
    const clients = await this.clientInfoModel.aggregate([
      {
        $match: {
          userId,
          status: StatusEnum.ACCEPT,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'clientId',
          foreignField: '_id',
          as: 'clientId',
          pipeline: [
            { $match: { isFakeAccount: false } },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                userName: 1,
                profileImage: 1,
                businessOrganizationName: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      { $unwind: { path: '$clientId', preserveNullAndEmptyArrays: true } },
      { $project: { _id: 1, clientId: 1, createdAt: 1, updatedAt: 1 } },
      { $sort: { createdAt: -1 } },
      { $limit: 1 },
    ]);
    return clients[0] ?? null;
  }

  private async getLatestPodcast(userId) {
    const podcast = await this.postModel.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          postLabel: PostLabelEnum.POD_CAST,
          $or: [{ repostBy: { $exists: false } }, { repostBy: null }],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'userId',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$userId',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'taggedPeople',
          foreignField: '_id',
          as: 'taggedPeople',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'reactions.userId',
          foreignField: '_id',
          as: 'reactions',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'repostBy',
          foreignField: '_id',
          as: 'repostBy',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$repostBy',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          collaborators: {
            $filter: {
              input: '$collaborators',
              as: 'collab',
              cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
            },
          },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'collaborators.id',
          foreignField: '_id',
          as: 'collaborators',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'fundraisers',
          foreignField: '_id',
          as: 'fundraisers',
          pipeline: [
            {
              $match: {
                isFakeAccount: false,
              },
            },
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                businessOrganizationName: 1,
                userName: 1,
                profileImage: 1,
                followers: 1,
                following: 1,
                connections: 1,
                accountVerified: 1,
                iAmMember: 1,
                professions: 1,
                hirerEmployerVerifiedStatus: 1,
                isMembershipVerified: 1,
                isFakeAccount: 1,
              },
            },
          ],
        },
      },
      {
        $project: {
          post: 0,
          group: 0,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $limit: 1,
      },
    ]);

    return podcast[0] ?? null;
  }

  public async getAllPublications(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['description'];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );
      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const publications = await this.userModel.findById(userId).select({
        publications: 1,
        _id: 0,
      });

      const totalCount = publications.publications.length;

      if (publications?.publications?.length > 0) {
        publications.publications = publications.publications.slice(
          skipData,
          skipData + limitData,
        );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: publications.publications.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        publications.publications,
        CONSTANT.FETCHED_SUCCESSFULLY('Publications'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllProfessions(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );
      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const professions = await this.userModel.findById(userId).select({
        professions: 1,
        _id: 0,
      });

      const totalCount = professions.professions.length;

      if (professions?.professions?.length > 0) {
        professions.professions = professions.professions.slice(
          skipData,
          skipData + limitData,
        );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: professions.professions.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        professions.professions,
        CONSTANT.FETCHED_SUCCESSFULLY('Professions'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getSelfIdentifyLanguagesOfUser(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );
      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const langauges = await this.selfIdentifyModel
        .findOne({
          userId: new mongoose.Types.ObjectId(userId),
        })
        .select({
          languagesSpokenSigned: 1,
          _id: 0,
        });

      if (!langauges) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Languages'),
          HttpStatus.NOT_FOUND,
        );
      }

      const totalCount = langauges.languagesSpokenSigned.length;

      if (langauges?.languagesSpokenSigned?.length > 0) {
        langauges.languagesSpokenSigned = langauges.languagesSpokenSigned.slice(
          skipData,
          skipData + limitData,
        );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: langauges.languagesSpokenSigned.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        langauges.languagesSpokenSigned,
        CONSTANT.FETCHED_SUCCESSFULLY('Languages'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async updateSelfIdentifyLanguages(
    req,
    updateSelfIdentifyLanguagesData: UpdateSelfIdentifyLanguagesDTO,
  ) {
    try {
      const { user: loggedInUser } = req;

      const isUserExist = await this.userModel.findById(loggedInUser._id);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const existingRecord = await this.selfIdentifyModel.findOne({
        userId: loggedInUser._id,
      });

      if (existingRecord) {
        await this.selfIdentifyModel.updateOne(
          { userId: loggedInUser._id },
          {
            $set: {
              languagesSpokenSigned:
                updateSelfIdentifyLanguagesData.languagesSpokenSigned,
            },
          },
        );
      } else {
        await this.selfIdentifyModel.create({
          userId: loggedInUser._id,
          languagesSpokenSigned:
            updateSelfIdentifyLanguagesData.languagesSpokenSigned,
        });
      }

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Languages'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllAffiliatePages(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }
      const affiliatePages = await this.userModel.findById(userId).select({
        affiliatePages: 1,
        _id: 0,
      });

      const totalCount = affiliatePages.affiliatePages.length;

      if (affiliatePages?.affiliatePages?.length > 0) {
        affiliatePages.affiliatePages = affiliatePages.affiliatePages.slice(
          skipData,
          skipData + limitData,
        );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: affiliatePages.affiliatePages.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        affiliatePages.affiliatePages,
        CONSTANT.FETCHED_SUCCESSFULLY('Affiliate Pages'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async verifyAffiliatePage(req, affiliatePageId: string) {
    try {
      const { user: loggedInUser } = req;
      const isUserExist = await this.userModel.findById(loggedInUser._id);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      await this.userModel.updateOne(
        {
          _id: loggedInUser._id,
          'affiliatePages._id': new mongoose.Types.ObjectId(affiliatePageId),
        },
        {
          $set: {
            'affiliatePages.$.isVerified': true,
          },
        },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Affiliate Page'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllSocialActivities(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const socialActivities = await this.userModel.findById(userId).select({
        socialActivities: 1,
        _id: 0,
      });

      const totalCount = socialActivities.socialActivities.length;

      if (socialActivities?.socialActivities?.length > 0) {
        socialActivities.socialActivities =
          socialActivities.socialActivities.slice(
            skipData,
            skipData + limitData,
          );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: socialActivities.socialActivities.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        socialActivities.socialActivities,
        CONSTANT.FETCHED_SUCCESSFULLY('Social Activity'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllOfferedServices(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const offeredServices = await this.userModel.findById(userId).select({
        offeredServices: 1,
        _id: 0,
      });

      const totalCount = offeredServices.offeredServices.length;

      if (offeredServices?.offeredServices?.length > 0) {
        offeredServices.offeredServices = offeredServices.offeredServices.slice(
          skipData,
          skipData + limitData,
        );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: offeredServices.offeredServices.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        offeredServices.offeredServices,
        CONSTANT.FETCHED_SUCCESSFULLY('Offered services'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllCauses(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const isUserExist = await this.userModel.findById(userId);

      if (!isUserExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const causes = await this.userModel.findById(userId).select({
        causes: 1,
        _id: 0,
      });

      const totalCount = causes.causes.length;

      if (causes?.causes?.length > 0) {
        causes.causes = causes.causes.slice(skipData, skipData + limitData);
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: causes.causes.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        causes.causes,
        CONSTANT.FETCHED_SUCCESSFULLY('Causes'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllFieldOfStudy(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = [];

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          sortParam,
          paginationParam,
        );

      const fieldOfStudy = await this.userModel.findById(userId).select({
        fieldOfStudy: 1,
        _id: 0,
      });

      const totalCount = fieldOfStudy.fieldOfStudy.length;

      if (fieldOfStudy?.fieldOfStudy?.length > 0) {
        fieldOfStudy.fieldOfStudy = fieldOfStudy.fieldOfStudy.slice(
          skipData,
          skipData + limitData,
        );
      }

      const paginationObj = {
        totalResults: totalCount,
        currentResults: fieldOfStudy.fieldOfStudy.length,
        totalPages: Math.ceil(totalCount / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        fieldOfStudy.fieldOfStudy,
        CONSTANT.FETCHED_SUCCESSFULLY('field Of Study'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getRecords(recordsData) {
    try {
      let records: any;
      let mergedArray: string[] = [];
      //use if type is profession,degree,passportCountries
      let mergeArray: { self_identify: string; isAddTxt: boolean }[] = [];

      //Define the fields for each type
      const fieldMap: Record<string, string[]> = {
        [getRecordsData.UNION]: ['americanUnions', 'globalUnions'],
        [getRecordsData.AFFILIATE_ORGANIZATION]: [
          'americanAffiliateOrganization',
          'globalAffiliateOrganization',
          'nonProfitPhilanthropicInstitution',
        ],
        [getRecordsData.AFFILIATE_BUSINESSES]: [
          'americanCateringCrafties',
          'artGalleries',
          'cateringCraftServicesGlobally',
          'museums',
          'talentRepsAgenciesManagementCompanies',
          'globalTalentRepsAgenciesManagementCompanies',
          'talentRepsAgenciesManagementCompaniesForSpeakers',
          'entertainmentLawFirms',
          'globalComedyClubs',
          'audioProductionRecordingStudios',
          'americanComedyClubs',
          'globalTheaters',
          'americanTheaters',
          'casting',
          'globalRecordingStudios',
          'lifeCoachAgencies',
          'animalActorsAgenciesCompanies',
          'publications',
          'investmentEntities',
          'productionAcquisitionDistributionCompanies',
        ],
        [getRecordsData.SCHOOL_TRAINING_FACILITIES]: [
          'schoolsTrainingInArtsEntertainment',
        ],
        [getRecordsData.VISUAL_ART]: ['visualArt'],
        [getRecordsData.PERFORMANING_ART]: ['performingArt'],
        [getRecordsData.DANCE]: ['dance'],
        [getRecordsData.ACTING]: ['acting'],
        [getRecordsData.MUSIC]: ['music'],
        [getRecordsData.FILM_MEDIA]: ['filmMedia'],
        [getRecordsData.DESIGN]: ['design'],
        [getRecordsData.LITERARY_ART]: ['literaryArt'],
        [getRecordsData.CRAFTS]: ['crafts'],
        [getRecordsData.APPLIED_ART]: ['appliedArt'],
        [getRecordsData.OTHER]: ['other'],
        [getRecordsData.NON_PROFIT_PHILANTHROPIC_INSTITUTION]: [
          'nonProfitPhilanthropicInstitution',
        ],
        [getRecordsData.FEEDBACK_CATEGORIES]: ['feedbackCategories'],
        [getRecordsData.PROFESSION]: ['profession'],
        [getRecordsData.PROJECTTYPEGENRE]: ['projectTypeGenre'],
        [getRecordsData.DEGREE]: ['degree'],
        [getRecordsData.PASSPORTCOUNTRIES]: ['passportCountries'],
        [getRecordsData.PRODUCTIONCOMPANIES]: ['productionCompanies'],
        [getRecordsData.INTERESTS]: ['interests'],
        [getRecordsData.ETHNICITY]: ['ethnicity'],
        [getRecordsData.ORGANIZATION_TYPE]: ['organizationType'],
        [getRecordsData.COMPANY_SIZE]: ['companySize'],
        [getRecordsData.SPECIALIZATION]: ['specialization'],
        [getRecordsData.INDUSTRY]: ['industry'],
        [getRecordsData.SEXUAL_ORIENTATION]: ['sexualOrientation'],
        [getRecordsData.NATIONALITY]: ['nationality'],
        [getRecordsData.SELFIOTHER]: ['selfIother'],
        [getRecordsData.SKILLS]: ['skills'],
        [getRecordsData.DISABILITY]: ['disability'],
        [getRecordsData.LANGUAGESSPOKENSIGNED]: ['languagesSpokenSigned'],
        [getRecordsData.PROFICIENCYLEVEL]: ['proficiencyLevel'],
        [getRecordsData.AGE]: ['age'],
        [getRecordsData.SELFIGENDER]: ['selfIgender'],
        [getRecordsData.CURRENCY]: ['currency'],
        [getRecordsData.FIELD_OF_STUDY]: ['fieldOfStudy'],
        [getRecordsData.PRONOUNS]: ['pronouns'],
        [getRecordsData.CAUSES]: ['causes'],
        [getRecordsData.PROFESSIONS]: ['professions'],
        [getRecordsData.POSITIONS]: ['positions'],
        [getRecordsData.FANDEGREE]: ['fanDegree'],
      };

      // Define special handling configurations for certain types
      const specialConfig: Record<string, { addTxt: string[] }> = {
        [getRecordsData.PROFESSION]: { addTxt: professionArr },
        [getRecordsData.PROJECTTYPEGENRE]: { addTxt: ['Other'] },
        // [getRecordsData.DEGREE]: { addTxt: ['Other'] },
        [getRecordsData.PASSPORTCOUNTRIES]: { addTxt: ['Other'] },
        // [getRecordsData.OTHER]: { addTxt: demographicsArr },
      };

      //Use dynamic field selection based on recordsData.type
      if (recordsData.type && fieldMap[recordsData.type]) {
        records = await this.recordModel
          .findOne()
          .select(fieldMap[recordsData.type].join(' ') + ' -_id');

        //Merge arrays into a single array
        mergedArray = await Promise.all(
          fieldMap[recordsData.type]
            .map((field) => records?.[field] || [])
            .flat(),
        );

        // Check if type has special handling configuration
        if (specialConfig[recordsData.type]) {
          const { addTxt } = specialConfig[recordsData.type];

          // Transform the array to an array of objects
          mergeArray = await Promise.all(
            mergedArray.map((item) => ({
              self_identify: item,
              isAddTxt: addTxt.includes(item),
            })),
          );
        }
      }

      if (Array.isArray(recordsData.listOfType)) {
        const types = [];

        recordsData.listOfType.forEach((type) => {
          if (fieldMap[type]) {
            types.push(type);
          }
        });

        const fields = types
          .map((t) => fieldMap[t].map((p) => p.split(',').join(' ')).join(' '))
          .join(' ');

        records = await this.recordModel.findOne().select(fields + ' -_id');

        mergedArray = types
          .map((type) => fieldMap[type].map((field) => records?.[field] || []))
          .flat()
          .flat();

        // check if type has special handling configuration
        types.forEach((type) => {
          if (specialConfig[type]) {
            const { addTxt } = specialConfig[type];

            // Transform the array to an array of objects
            mergeArray = mergedArray.map((item) => ({
              self_identify: item,
              isAddTxt: addTxt.includes(item),
            }));
          }
        });
      }

      const fieldJobListMap: Record<string, string[]> = {
        [getRecordsData.ROLETYPE]: ['roleType'],
        [getRecordsData.PROJECTTYPE]: ['projectType'],
        [getRecordsData.COMMERCIAL_BRANDED_CONTENT]: [
          'commercialBrandedContent',
        ],
        [getRecordsData.CONCERT]: ['concert'],
        [getRecordsData.FASHION]: ['fashion'],
        [getRecordsData.LITERATURE]: ['literature'],
        [getRecordsData.MOVIE_FILM]: ['movieFilm'],
        [getRecordsData.MUSEUM]: ['museum'],
        [getRecordsData.OPERA]: ['opera'],
        [getRecordsData.HIPHOP]: ['hipHop'],
        [getRecordsData.RAP]: ['rap'],
        [getRecordsData.COUNTRY_MUSIC]: ['countryMusic'],
        [getRecordsData.OTHER_DIGITAL_MEDIA]: ['otherDigitalMedia'],
        [getRecordsData.RELIGIOUS_SPIRITUAL_MUSIC]: ['religiousSpiritualMusic'],
        [getRecordsData.STAND_UP_COMEDY]: ['standUpComedy'],
        [getRecordsData.TV_SHOW_SERIES]: ['tVShowSeries'],
        [getRecordsData.THEATER]: ['theater'],
        [getRecordsData.USER_GENERATED_CONTENT_UGC]: [
          'userGeneratedContentUGC',
        ],
        [getRecordsData.TYPE_OF_ORGANIZATION_YOU_WORKING_WITH_PROJECT]: [
          'typeOfOrganizationYouAreWorkingWithForThisProject',
        ],
        [getRecordsData.ADDITIONAL]: ['additional'],
      };

      //Use dynamic field selection based on recordsData.type
      if (recordsData.type && fieldJobListMap[recordsData.type]) {
        records = await this.recordJobModel
          .findOne()
          .select(fieldJobListMap[recordsData.type].join(' ') + ' -_id');

        //Merge arrays into a single array
        mergedArray = await Promise.all(
          fieldJobListMap[recordsData.type]
            .map((field) => records?.[field] || [])
            .flat(),
        );

        // Check if type has special handling configuration
        if (specialConfig[recordsData.type]) {
          const { addTxt } = specialConfig[recordsData.type];

          // Transform the array to an array of objects
          mergeArray = await Promise.all(
            mergedArray.map((item) => ({
              self_identify: item,
              isAddTxt: addTxt.includes(item),
            })),
          );
        }
      }

      if (recordsData.search) {
        const searchArr = (recordsData.search as string).split(' ');

        const regexSearch: RegExp[] = searchArr.map(
          (ele) => new RegExp(ele, 'i'),
        );

        //Use the regexSearch array to filter the mergedArray
        if (regexSearch.length > 0) {
          mergedArray = mergedArray.filter((item) =>
            regexSearch.some((regex) => regex.test(item)),
          );
        }
      }

      return successResponse(
        specialConfig[recordsData.type] ? mergeArray : mergedArray,
        CONSTANT.FETCHED_SUCCESSFULLY(
          recordsData.type ? recordsData.type : 'Records ',
        ),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async privacySetting(req, privacySettingData) {
    try {
      const { user: loggedInUser } = req;

      const privacy: any = await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        privacySettingData,
        {
          new: true,
          runValidators: true,
        },
      );

      // Extract only the updated fields
      const updatedFields = {
        profileOpen: privacy.profileOpen,
        contactYou: privacy.contactYou,
        collaborationOptions: privacy.collaborationOptions,
        notify: privacy.notify,
        isOnline: privacy.isOnline,
      };

      return successResponse(
        { ...updatedFields },
        CONSTANT.UPDATED_SUCCESSFULLY('Privacy setting'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async visibilitySetting(req, visibilitySettingData) {
    try {
      const { user: loggedInUser } = req;

      const visibility: any = await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        visibilitySettingData,
        {
          new: true,
          runValidators: true,
        },
      );

      // Extract only the updated fields
      const updatedFields = {
        collaboration: visibility.collaboration,
      };

      return successResponse(
        { ...updatedFields },
        CONSTANT.UPDATED_SUCCESSFULLY('Visibility setting'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async socialMediaSetting(req, socialMediaSettingData) {
    try {
      const { user: loggedInUser } = req;

      const user = await this.userModel.findById(loggedInUser._id);

      // Check if contactInfo exists
      if (user.contactInfo) {
        user.contactInfo.push(...socialMediaSettingData.contactInfo);
      } else {
        user.contactInfo = socialMediaSettingData.contactInfo;
      }

      const socialMedia: any = await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          socialMedia: socialMediaSettingData.socialMedia,
          contactInfo: user.contactInfo,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      // Extract only the updated fields
      const updatedFields = {
        socialMedia: socialMedia.socialMedia,
        contactInfo: socialMedia.contactInfo,
      };

      return successResponse(
        { ...updatedFields },
        CONSTANT.UPDATED_SUCCESSFULLY('Social-media setting'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async genderSetting(req, genderData) {
    try {
      const { user: loggedInUser } = req;

      if (
        genderData.gender === GenderEnum.PREFER_SELF_DESCRIBE &&
        !genderData.customGenderValue
      ) {
        throw new HttpException(
          CONSTANT.REQUIRED('Custom gender value'),
          HttpStatus.NOT_FOUND,
        );
      }

      const gender_data: any = await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        genderData,
        {
          new: true,
          runValidators: true,
        },
      );

      // Extract only the updated fields
      const updatedFields = {
        gender: gender_data.gender,
        customGenderValue: gender_data.customGenderValue,
      };

      return successResponse(
        { ...updatedFields },
        CONSTANT.UPDATED_SUCCESSFULLY('Gender'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async fileUpload(file, req, fileUploadData) {
    try {
      const { user: loggedInUser } = req;

      let location, thumbnailUrl, folderPath, responseMessage, mediaType;

      if (
        file &&
        (fileUploadData.uploadType === 'PROFILE' ||
          fileUploadData.uploadType === 'MEMBERID' ||
          fileUploadData.uploadType === 'MEMBERDOC' ||
          fileUploadData.uploadType === 'GROUP_COVER' ||
          fileUploadData.uploadType === 'JOB_RESUME' ||
          fileUploadData.uploadType === 'LOGO' ||
          fileUploadData.uploadType === 'WEB')
      ) {
        if (fileUploadData.uploadType === 'PROFILE')
          folderPath = `Pepli/users/${loggedInUser._id}/profile/`;
        if (fileUploadData.uploadType === 'MEMBERID')
          folderPath = `Pepli/users/${loggedInUser._id}/memberid/`;
        if (fileUploadData.uploadType === 'MEMBERDOC')
          folderPath = `Pepli/users/${loggedInUser._id}/memberdoc/`;
        if (fileUploadData.uploadType === 'GROUP_COVER')
          folderPath = `Pepli/users/${loggedInUser._id}/group_cover/`;
        if (fileUploadData.uploadType === 'JOB_RESUME')
          folderPath = `Pepli/users/${loggedInUser._id}/job_resume/`;
        if (fileUploadData.uploadType === 'LOGO')
          folderPath = `Pepli/users/logo/`;
        if (fileUploadData.uploadType === 'WEB')
          folderPath = `Pepli/users/web/`;

        responseMessage = CONSTANT.IMAGE_UPLOAD_ERROR;

        mediaType = MediaEnum.IMAGE;

        location = await this.awsService.s3Upload(file, folderPath);
        if (!location) {
          throw new HttpException(
            responseMessage,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }

        return successResponse(
          {
            mediaType,
            url: location[0],
            thumbUrl: null,
          },
          CONSTANT.FILE_UPLOADED,
          HttpStatus.OK,
        );
      } else if (
        file &&
        (fileUploadData.uploadType === 'POST' ||
          fileUploadData.uploadType === 'GROUP_POST' ||
          fileUploadData.uploadType === 'STORY' ||
          fileUploadData.uploadType === 'PORTFOLIO' ||
          fileUploadData.uploadType === 'PROJECT' ||
          fileUploadData.uploadType === 'PROFILE_PROJECT' ||
          fileUploadData.uploadType === 'HONOR_AND_AWARD')
      ) {
        const mediaResponses = [];

        for (const files of file) {
          if (fileUploadData.uploadType === 'POST')
            folderPath = `Pepli/users/${loggedInUser._id}/posts/`;
          if (fileUploadData.uploadType === 'GROUP_POST')
            folderPath = `Pepli/users/${loggedInUser._id}/group/posts/`;
          if (fileUploadData.uploadType === 'STORY')
            folderPath = `Pepli/users/${loggedInUser._id}/stories/`;
          if (fileUploadData.uploadType === 'PORTFOLIO')
            folderPath = `Pepli/users/${loggedInUser._id}/portfolio/`;
          if (
            fileUploadData.uploadType === 'PROJECT' ||
            fileUploadData.uploadType === 'PROFILE_PROJECT'
          )
            folderPath = `Pepli/users/${loggedInUser._id}/project/`;

          if (fileUploadData.uploadType === 'HONOR_AND_AWARD')
            folderPath = `Pepli/users/${loggedInUser._id}/honor_and_award/`;

          responseMessage = CONSTANT.FILE_UPLOAD_ERROR;

          mediaType = files.originalname
            .toLowerCase()
            .match(/\.(mp4|mkv|mpeg|mov|webm)$/)
            ? MediaEnum.VIDEO
            : MediaEnum.IMAGE;

          location = await this.awsService.s3Upload(files, folderPath);

          if (!location) {
            throw new HttpException(
              responseMessage,
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }

          if (mediaType === MediaEnum.VIDEO) {
            if (fileUploadData.uploadType === 'POST')
              folderPath = `Pepli/users/${loggedInUser._id}/posts/thumbnail`;
            if (fileUploadData.uploadType === 'GROUP_POST')
              folderPath = `Pepli/users/${loggedInUser._id}/group/posts/thumbnail`;
            if (fileUploadData.uploadType === 'STORY')
              folderPath = `Pepli/users/${loggedInUser._id}/stories/thumbnail`;
            if (fileUploadData.uploadType === 'PORTFOLIO')
              folderPath = `Pepli/users/${loggedInUser._id}/portfolio/thumbnail`;
            if (
              fileUploadData.uploadType === 'PROJECT' ||
              fileUploadData.uploadType === 'PROFILE_PROJECT'
            )
              folderPath = `Pepli/users/${loggedInUser._id}/project/thumbnail`;

            thumbnailUrl = await this.awsService.uploadThumbnail(
              location,
              folderPath,
            );
            if (!thumbnailUrl) {
              throw new HttpException(
                CONSTANT.THUMBNAIL_ERROR,
                HttpStatus.FORBIDDEN,
              );
            }
          }
          // For video files, set thumbUrl to 'thumb exist'
          const thumbUrl = mediaType === MediaEnum.VIDEO ? thumbnailUrl : null;

          mediaResponses.push({
            mediaType,
            url: location,
            thumbUrl,
          });
        }

        return successResponse(
          mediaResponses,
          CONSTANT.FILE_UPLOADED,
          HttpStatus.OK,
        );
      } else {
        throw new HttpException(
          CONSTANT.FILE_TYPE_NOT_MATCHED,
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      console.log(error);
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getProfile(req) {
    try {
      const { user: loggedInUser } = req;
      const userId = new mongoose.Types.ObjectId(loggedInUser._id);

      const [profile, jobPosts] = await Promise.all([
        this.userRepository.findProfileById(
          userId.toString(),
          userId.toString(),
        ),
        this.getLatestJobPost(userId),
      ]);

      const response = profile?.[0];

      if (!response) {
        return successResponse(
          null,
          CONSTANT.FETCHED_SUCCESSFULLY('Profile info'),
          HttpStatus.OK,
        );
      }

      // Attach jobPosts
      response.jobPosts = jobPosts;

      // Nullify empty projects
      if (Object.keys(response.projects || {}).length <= 1) {
        response.projects = null;
      }

      // Attach selfIdentify
      response.selfIdentify = (
        await this.getSelfIdentifySettingOfUser(req)
      ).data;

      // Default flags
      response.connectionRequestStatus = null;
      response.isFollowing = null;
      response.isSubscribed = false;

      // Clients
      const client = await this.getLatestClientInfo(userId);
      response.clientInfo = client || null;

      // Members
      const members = await this.getPeoples(
        { query: { page: 1, perPage: 1 } },
        userId.toString(),
        true,
      );
      if (members.data.length > 0) {
        response.member = members.data[0];
      }

      // Group
      response.group = await this.getLatestGroup(userId);

      // Podcast
      response.podcast = await this.getLatestPodcast(userId);

      return successResponse(
        response,
        CONSTANT.FETCHED_SUCCESSFULLY('Profile info'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getLatestGroup(userId: any) {
    return this.groupModel
      .findOne({ createdBy: userId })
      .select('name description coverPhoto privacy createdAt sharableLink')
      .sort({ createdAt: -1 });
  }

  public async profileSetting(req, profileData) {
    try {
      const { user: loggedInUser } = req;

      const profile: any = await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        profileData,
        {
          new: true,
          runValidators: true,
        },
      );

      const mediaType = 'PROFILE';
      // Deleting file from s3 bucket
      if (profileData.profileImage && loggedInUser.profileImage) {
        const folderName = `Pepli/users/${loggedInUser._id}/profile/`;
        await this.awsService.s3Delete(
          loggedInUser.profileImage,
          folderName,
          mediaType,
        );
      }

      // Extract only the updated fields
      const updatedFields = {
        firstName: profile.firstName,
        lastName: profile.lastName,
        businessOrganizationName: profile.businessOrganizationName,
        profileImage: profile.profileImage,
        bio: profile.bio,
        gender: profile.gender,
        customGenderValue: profile.customGenderValue,
        education: profile.education,
        degree: profile.degree,
        profession: profile.profession,
        availabilityForWork: profile.availabilityForWork,
        passportCountries: profile.passportCountries,
        jobType: profile.jobType,
        interests: profile.interests,
      };

      //update user profileImage in stream
      const chatObj = {
        id: `${profile._id}`,
        profile_image: profile.profileImage,
        name:
          profile.firstName && profile.lastName
            ? `${profile.firstName} ${profile.lastName}`
            : profile.businessOrganizationName,
        userName: profile.userName ?? profile.businessOrganizationName,
        businessOrganizationName: profile.businessOrganizationName,
      };
      await this.streamChatService.createOrUpdateUserForChat(chatObj);

      return successResponse(
        { ...updatedFields },
        CONSTANT.UPDATED_SUCCESSFULLY('Profile info'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdatePublication(req, publicationData: publicationDto) {
    try {
      const { user: loggedInUser } = req;
      const { _id: id, description, website } = publicationData;

      const user = await this.userModel.findById(loggedInUser._id);
      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      let publication;

      if (id) {
        publication = await this.userModel.findOneAndUpdate(
          {
            _id: loggedInUser._id,
            'publications._id': new mongoose.Types.ObjectId(id),
          },
          {
            $set: {
              'publications.$[elem].description': description,
              'publications.$[elem].website': website,
            },
          },
          {
            new: true,
            runValidators: true,
            arrayFilters: [{ 'elem._id': new mongoose.Types.ObjectId(id) }],
          },
        );
      }

      if (!publication) {
        publication = await this.userModel.findByIdAndUpdate(
          loggedInUser._id,
          {
            $push: {
              publications: {
                _id: new mongoose.Types.ObjectId(),
                description,
                website,
              },
            },
          },
          { new: true, runValidators: true },
        );
      }

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Publication info'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdateProfessions(req, data: professionDto) {
    try {
      const { user: loggedInUser } = req;

      const { professions } = data;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            professions: professions,
          },
        },
        { new: true, runValidators: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('profession'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdateAffiliatePages(req, pagesData: affliatePagesListDto) {
    try {
      const { user: loggedInUser } = req;

      const { affiliatePages } = pagesData;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            affiliatePages: affiliatePages,
          },
        },
        { new: true, runValidators: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('affiliate pages'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdateSocialActivities(req, data: socialActivitiesDto) {
    try {
      const { user: loggedInUser } = req;

      const { socialActivities } = data;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            socialActivities: socialActivities,
          },
        },
        { new: true, runValidators: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('social activities'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdateOfferedServices(req, data: offeredServicesDto) {
    try {
      const { user: loggedInUser } = req;

      const { offeredServices } = data;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            offeredServices: offeredServices,
          },
        },
        { new: true, runValidators: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('offered services'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdateCauses(req, data: causesDto) {
    try {
      const { user: loggedInUser } = req;

      const { causes } = data;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            causes: causes,
          },
        },
        { new: true, runValidators: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Causes'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addOrUpdatefieldOfStudy(req, data: fieldOfStudyDto) {
    try {
      const { user: loggedInUser } = req;

      const { fieldOfStudy } = data;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            fieldOfStudy: fieldOfStudy,
          },
        },
        { new: true, runValidators: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('field of study'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeAffiliatePage(req, name: string) {
    try {
      const { user: loggedInUser } = req;

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            affiliatePages: name,
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Affiliate page'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeSocialActivity(req, name: string) {
    try {
      const { user: loggedInUser } = req;

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            socialActivities: name,
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('social activity'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeFieldOfStudy(req, name: string) {
    try {
      const { user: loggedInUser } = req;

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            fieldOfStudy: name,
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('field Of study'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeOfferedService(req, name: string) {
    try {
      const { user: loggedInUser } = req;

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            offeredServices: name,
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Offered Service'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeCause(req, name: string) {
    try {
      const { user: loggedInUser } = req;

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            causes: name,
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Cause'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removeProfessions(req, name: string) {
    try {
      const { user: loggedInUser } = req;

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            professions: name,
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Profession'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async updateAnalyticsVisibility(
    req,
    data: { visibility: 'public' | 'private' },
  ) {
    try {
      const { user: loggedInUser } = req;

      const { visibility } = data;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        {
          $set: {
            'analytics.visibility': visibility,
          },
        },
        {
          new: true,
          runValidators: true,
        },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Analytics visibility'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async removePublication(req, publicationId: string) {
    try {
      const { user: loggedInUser } = req;

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new NotFoundException(CONSTANT.NOT_FOUND_MESSAGE('User'));
      }

      await this.userModel.findOneAndUpdate(
        {
          _id: loggedInUser._id,
        },
        {
          $pull: {
            publications: { _id: new mongoose.Types.ObjectId(publicationId) },
          },
        },
        { new: true },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Publication info'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async selfIdentifySetting(req, selfIdentifyData) {
    try {
      const { user: loggedInUser } = req;

      const userExist = await this.selfIdentifyModel.findOne({
        userId: loggedInUser._id,
      });
      let selfIdentify: any;
      if (!userExist) {
        const data = {
          ...selfIdentifyData,
          userId: loggedInUser._id,
        };
        selfIdentify = await new this.selfIdentifyModel(data).save();
      }

      if (userExist) {
        selfIdentify = await this.selfIdentifyModel.findOneAndUpdate(
          { userId: loggedInUser._id },
          {
            birthDate: selfIdentifyData.birthDate
              ? selfIdentifyData.birthDate
              : null,
            genderPronoun: selfIdentifyData.genderPronoun
              ? selfIdentifyData.genderPronoun
              : null,

            ...selfIdentifyData,
          },
          {
            new: true,
            runValidators: true,
          },
        );
      }

      // Extract only the updated fields
      const updatedFields = {
        ethnicity: selfIdentify.ethnicity,
        ethnicityVisibleOnProfile: selfIdentify.ethnicityVisibleOnProfile,
        ethnicitySearchable: selfIdentify.ethnicitySearchable,
        nationality: selfIdentify.nationality,
        nationalityVisibleOnProfile: selfIdentify.nationalityVisibleOnProfile,
        nationalitySearchable: selfIdentify.nationalitySearchable,
        disability: selfIdentify.disability,
        disabilityVisibleOnProfile: selfIdentify.disabilityVisibleOnProfile,
        disabilitySearchable: selfIdentify.disabilitySearchable,
        customDisability: selfIdentify.customDisability,
        languagesSpokenSigned: selfIdentify.languagesSpokenSigned,
        birthDate: selfIdentify.birthDate,
        ageRange: selfIdentify.ageRange,
        showYearOnYourBirthday: selfIdentify.showYearOnYourBirthday,
        skills: selfIdentify.skills,
        birthDateVisibleOnProfile: selfIdentify.birthDateVisibleOnProfile,
        birthDateSearchable: selfIdentify.birthDateSearchable,
        selfIgender: selfIdentify.selfIgender,
        selfIgenderVisibleOnProfile: selfIdentify.selfIgenderVisibleOnProfile,
        selfIgenderSearchable: selfIdentify.selfIgenderSearchable,
        customSelfIgender: selfIdentify.customSelfIgender,
        other: selfIdentify.other,
        otherVisibleOnProfile: selfIdentify.otherVisibleOnProfile,
        otherSearchable: selfIdentify.otherSearchable,
      };

      if (
        Array.isArray(selfIdentifyData.skills) &&
        selfIdentifyData.skills.length > 0
      ) {
        const updatedSkills = await this.userRepository.updateSkills(
          loggedInUser._id,
          selfIdentifyData.skills,
        );
        selfIdentifyData.skills = Array.isArray(updatedSkills)
          ? updatedSkills
          : [];
      }

      return successResponse(
        { ...updatedFields },
        CONSTANT.UPDATED_SUCCESSFULLY('Self-Identify info'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getSelfIdentifySettingOfUser(req) {
    try {
      const { user: loggedInUser } = req;

      const selfIdentify = await this.selfIdentifyModel
        .findOne({
          userId: loggedInUser._id,
        })
        .populate({
          path: 'userId',
          match: { isFakeAccount: false },
          select:
            '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified userProfileAboutYou.skills',
        });

      if (selfIdentify) {
        if (selfIdentify.userId?.userProfileAboutYou?.skills) {
          selfIdentify.skills =
            selfIdentify.userId.userProfileAboutYou.skills ?? [];
        }
      }

      const obj = selfIdentify;
      delete obj?.userId.userProfileAboutYou;

      return successResponse(
        obj,
        CONSTANT.FETCHED_SUCCESSFULLY('Self-Identify info'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async homeData(req, queryParam) {
    try {
      const { user: loggedInUser } = req;
      const { sortBy, sort, page, perPage } = queryParam;

      const sortParam = { sortBy, sort };
      const paginationParam = { page, perPage };

      // Create Search, Filter, Sort and Pagination
      const { sortObj, skipData, limitData } = createSearchFilterSortPagination(
        null,
        null,
        null,
        sortParam,
        paginationParam,
      );

      // Aggregation pipeline array
      const pipelineArr: any = [
        ...(loggedInUser.iAmMember === iAmMemberEnum.AUDIENCE_MEMBER_FAN
          ? [
              {
                $match: {
                  postLabel: { $ne: PostLabelEnum.COLLAB },
                },
              },
            ]
          : []),
        {
          $match: {
            $expr: {
              $cond: [
                {
                  $and: [
                    { $eq: [{ $type: '$repostBy' }, 'objectId'] },
                    { $eq: ['$userId', loggedInUser._id] },
                  ],
                },
                {
                  $not: {
                    $and: [
                      { $ne: ['$repostBy', loggedInUser._id] },
                      { $eq: ['$userId', loggedInUser._id] },
                    ],
                  },
                },
                true,
              ],
            },
          },
        },
        {
          $addFields: {
            collaborators: {
              $filter: {
                input: '$collaborators',
                as: 'collab',
                cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'collaborators.id',
            foreignField: '_id',
            as: 'collaborators',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'fundraisers',
            foreignField: '_id',
            as: 'fundraisers',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  businessOrganizationName: 1,
                  userName: 1,
                  profileImage: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'taggedPeople',
            foreignField: '_id',
            as: 'taggedPeople',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'connectioninfos',
            let: {
              userId: '$userId',
              loggedInUserId: loggedInUser._id,
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $or: [
                      {
                        $and: [
                          {
                            $eq: ['$$userId', '$userId'],
                          },
                          {
                            $eq: ['$connectionWithId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$status', connectionRequestStatus.ACCEPT],
                          },
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: ['$userId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$status', connectionRequestStatus.ACCEPT],
                          },
                          {
                            $eq: ['$$userId', '$connectionWithId'],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
            as: 'connectionInfo',
          },
        },
        {
          $unwind: {
            path: '$connectionInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'followerinfos',
            let: {
              userId: '$userId',
              loggedInUserId: loggedInUser._id,
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $or: [
                      {
                        $and: [
                          {
                            $eq: ['$followerId', '$$loggedInUserId'],
                          },
                          {
                            $eq: ['$followingId', '$$userId'],
                          },

                          {
                            $eq: ['$status', StatusEnum.ACCEPT],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
            as: 'followerInfo',
          },
        },
        {
          $unwind: {
            path: '$followerInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $lookup: {
                  from: 'usersignupdatas',
                  let: {
                    signupItems: '$signUpData',
                  },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $and: [
                            {
                              $in: [
                                '$_id',
                                {
                                  $map: {
                                    input: {
                                      $filter: {
                                        input: '$$signupItems',
                                        as: 'item',
                                        cond: {
                                          $eq: ['$$item.isSelected', true],
                                        },
                                      },
                                    },
                                    as: 'filtered',
                                    in: '$$filtered.itemId',
                                  },
                                },
                              ],
                            },
                            { $eq: ['$title', 'profile_open_to'] },
                            { $eq: ['$slug', 'private_1'] },
                            { $eq: ['$itemText', 'Private'] },
                          ],
                        },
                      },
                    },
                  ],
                  as: 'privateProfileMatches',
                },
              },
              {
                $addFields: {
                  isPrivateAccount: {
                    $cond: [
                      {
                        $and: [
                          { $gt: [{ $size: '$privateProfileMatches' }, 0] }, // any match found
                          { $ne: ['$_id', loggedInUser._id] },
                        ],
                      },
                      true,
                      false,
                    ],
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  isPrivateAccount: 1,
                  privateProfileMatches: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$userId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'repostBy',
            foreignField: '_id',
            as: 'repostBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$repostBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'bookmarks',
            localField: '_id',
            foreignField: 'postId',
            as: 'bookmarks',
          },
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'group',
            pipeline: [
              {
                $project: {
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$group',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: {
            group: { $exists: false },
          },
        },
        // Prioritize posts based on userId.iAmMember
        // {
        //   $addFields: {
        //     priority: {
        //       $cond: {
        //         if: {
        //           $eq: [
        //             '$userId.iAmMember',
        //             iAmMemberEnum.UNION_AFFILIATE_MEMBER,
        //           ],
        //         },
        //         then: 1,
        //         else: 2,
        //       },
        //     },
        //   },
        // },
        {
          $addFields: {
            isLike: {
              $cond: {
                if: {
                  $in: [
                    mongoose.Types.ObjectId.createFromHexString(
                      loggedInUser._id.toString(),
                    ),
                    '$reactions.userId',
                  ],
                },
                then: true,
                else: false,
              },
            },
            isBookmark: {
              $cond: {
                if: {
                  $in: [
                    mongoose.Types.ObjectId.createFromHexString(
                      loggedInUser._id.toString(),
                    ),
                    '$bookmarks.userId',
                  ],
                },
                then: true,
                else: false,
              },
            },
            currentUserReaction: {
              $let: {
                vars: {
                  userReaction: {
                    $filter: {
                      input: '$reactions',
                      as: 'reaction',
                      cond: {
                        $eq: [
                          '$$reaction.userId',
                          mongoose.Types.ObjectId.createFromHexString(
                            loggedInUser._id.toString(),
                          ),
                        ],
                      },
                    },
                  },
                },
                in: {
                  $cond: {
                    if: {
                      $gt: [
                        {
                          $size: '$$userReaction',
                        },
                        0,
                      ],
                    },
                    then: {
                      $arrayElemAt: ['$$userReaction.type', 0],
                    },
                    else: null,
                  },
                },
              },
            },
            // priority: {
            //   $cond: {
            //     if: {
            //       $eq: ['$userId._id', loggedInUser._id],
            //     },
            //     then: 1,
            //     else: {
            //       $cond: {
            //         if: {
            //           $gt: [{ $ifNull: ['$connectionInfo', null] }, null],
            //         },
            //         then: 2,
            //         else: {
            //           $cond: {
            //             if: {
            //               $gt: [{ $ifNull: ['$followerInfo', null] }, null],
            //             },
            //             then: 3,
            //             else: 4,
            //           },
            //         },
            //       },
            //     },
            //   },
            // },
            // priority: {
            //   $cond: {
            //     if: { $eq: ['$userId._id', loggedInUser._id] },
            //     then: 1, // Author always can see

            //     else: {
            //       $cond: {
            //         // If whoCanComment missing or empty → fallback original priority logic
            //         if: {
            //           $or: [
            //             { $not: ['$whoCanComment'] },
            //             { $eq: [{ $size: '$whoCanComment' }, 0] },
            //           ],
            //         },
            //         then: {
            //           $cond: {
            //             if: {
            //               $gt: [{ $ifNull: ['$connectionInfo', null] }, null],
            //             },
            //             then: 2, // connection
            //             else: {
            //               $cond: {
            //                 if: {
            //                   $gt: [{ $ifNull: ['$followerInfo', null] }, null],
            //                 },
            //                 then: 3, // follower
            //                 else: 4, // public / fans / others
            //               },
            //             },
            //           },
            //         },

            //         else: {
            //           // whoCanComment is present → check hierarchy fans > followers > myConnections
            //           $cond: {
            //             if: { $in: ['fans', '$whoCanComment'] },
            //             then: {
            //               $cond: {
            //                 if: {
            //                   $gt: [
            //                     { $ifNull: ['$connectionInfo', null] },
            //                     null,
            //                   ],
            //                 },
            //                 then: 2, // connection can see
            //                 else: {
            //                   $cond: {
            //                     if: {
            //                       $gt: [
            //                         { $ifNull: ['$followerInfo', null] },
            //                         null,
            //                       ],
            //                     },
            //                     then: 3, // follower can see
            //                     else: 4, // fans/public can see
            //                   },
            //                 },
            //               },
            //             },

            //             else: {
            //               $cond: {
            //                 if: { $in: ['followers', '$whoCanComment'] },
            //                 then: {
            //                   $cond: {
            //                     if: {
            //                       $gt: [
            //                         { $ifNull: ['$connectionInfo', null] },
            //                         null,
            //                       ],
            //                     },
            //                     then: 2, // connection can see
            //                     else: {
            //                       $cond: {
            //                         if: {
            //                           $gt: [
            //                             { $ifNull: ['$followerInfo', null] },
            //                             null,
            //                           ],
            //                         },
            //                         then: 3, // follower can see
            //                         else: 5, // no access
            //                       },
            //                     },
            //                   },
            //                 },

            //                 else: {
            //                   // Only myConnections present
            //                   $cond: {
            //                     if: {
            //                       $gt: [
            //                         { $ifNull: ['$connectionInfo', null] },
            //                         null,
            //                       ],
            //                     },
            //                     then: 2, // connection can see
            //                     else: 5, // no access
            //                   },
            //                 },
            //               },
            //             },
            //           },
            //         },
            //       },
            //     },
            //   },
            // },
            priority: {
              $cond: {
                if: { $eq: ['$userId._id', loggedInUser._id] },
                then: 1, // author
                else: {
                  $cond: {
                    if: {
                      $and: [
                        { $in: ['myConnections', '$whoCanComment'] },
                        { $gt: [{ $ifNull: ['$connectionInfo', null] }, null] },
                      ],
                    },
                    then: 2, // connections
                    else: {
                      $cond: {
                        if: {
                          $and: [
                            { $in: ['followers', '$whoCanComment'] },
                            {
                              $gt: [{ $ifNull: ['$followerInfo', null] }, null],
                            },
                          ],
                        },
                        then: 3, // followers
                        else: {
                          $cond: {
                            if: {
                              $and: [
                                { $in: ['fans', '$whoCanComment'] },
                                {
                                  $eq: [
                                    loggedInUser.iAmMember,
                                    iAmMemberEnum.AUDIENCE_MEMBER_FAN,
                                  ],
                                },
                                {
                                  $eq: ['$userId.isPrivateAccount', false],
                                },
                              ],
                            },
                            then: 4, // fans
                            else: {
                              $cond: {
                                if: {
                                  $and: [
                                    {
                                      $or: [
                                        { $not: ['$whoCanComment'] },
                                        {
                                          $eq: [{ $size: '$whoCanComment' }, 0],
                                        },
                                        { $in: ['public', '$whoCanComment'] },
                                      ],
                                    },
                                    {
                                      $eq: ['$userId.isPrivateAccount', false],
                                    },
                                  ],
                                },
                                then: 5, // public
                                else: 6, // fallback no access
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        {
          $match: {
            $expr: {
              $or: [
                { $eq: ['$priority', 1] }, // author always sees
                {
                  $and: [
                    { $eq: ['$priority', 2] },
                    { $in: ['myConnections', '$whoCanComment'] },
                  ],
                },
                {
                  $and: [
                    { $eq: ['$priority', 3] },
                    { $in: ['followers', '$whoCanComment'] },
                  ],
                },
                {
                  $and: [
                    { $eq: ['$priority', 4] },
                    { $in: ['fans', '$whoCanComment'] },
                  ],
                },
                {
                  $and: [
                    { $eq: ['$priority', 5] },
                    { $eq: ['$userId.isPrivateAccount', false] },
                  ],
                },
              ],
            },
          },
        },

        {
          $unset: 'reactions',
        },
      ];

      // Apply the priority sort first, then conditional sorting if sortObj has values
      if (Object.keys(sortObj).length) {
        pipelineArr.push({ $sort: { ...sortObj, priority: 1 } });
      } else {
        pipelineArr.push({ $sort: { ...sortObj, priority: 1 } });
      }

      if (skipData || skipData === 0) pipelineArr.push({ $skip: skipData });
      if (limitData || limitData === 0) pipelineArr.push({ $limit: limitData });

      // Get posts data query
      const postData = await this.postModel.aggregate(pipelineArr, {
        collation: {
          locale: 'en_US',
          numericOrdering: true,
        },
      });

      let postCount;

      if (loggedInUser.iAmMember === iAmMemberEnum.AUDIENCE_MEMBER_FAN) {
        postCount = await this.postModel.countDocuments({
          postLabel: { $ne: PostLabelEnum.COLLAB },
          group: { $exists: false },
        });
      } else {
        postCount = await this.postModel.countDocuments({
          group: { $exists: false },
        }); //posts without group
      }

      // Pagination parameters
      const totalResults = postCount;
      const currentResults = postData?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        postData,
        CONSTANT.FETCHED_SUCCESSFULLY('Home data'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async skipScreen() {
    try {
      return successResponse(null, CONSTANT.SKIP, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async editProfile(req, editProfileData: editProfileDto) {
    try {
      const { user: loggedInUser } = req;

      if (typeof editProfileData.isNotificationOn !== 'undefined') {
        loggedInUser.isNotificationOn = editProfileData.isNotificationOn;
      }
      if (typeof editProfileData.isEmailOn !== 'undefined') {
        loggedInUser.isEmailOn = editProfileData.isEmailOn;
      }

      let existingSignUpData = loggedInUser.signUpData || [];

      // Create a map of existing itemId to easily update them
      const itemMap: any = new Map(
        existingSignUpData.map((item) => [
          item.itemId.toString(),
          { itemId: item.itemId.toString(), isSelected: item.isSelected },
        ]),
      );

      delete editProfileData.hirerEmployerVerifiedStatus;

      // Loop through the incoming newSignUpData and update/add accordingly
      editProfileData.signUpData &&
        editProfileData.signUpData.forEach((newItem) => {
          if (itemMap.has(newItem.itemId)) {
            // Update the existing item
            if (itemMap.get(newItem.itemId).isSelected !== newItem.isSelected) {
              existingSignUpData = existingSignUpData.map((item) => {
                if (item.itemId.toString() === newItem.itemId) {
                  return {
                    itemId: item.itemId,
                    isSelected: newItem.isSelected,
                  };
                }
                return item;
              });
            }
          } else {
            // Add new item to the map
            existingSignUpData.push(newItem);
          }
        });

      // Update the user's signUpData
      if (editProfileData.signUpData) {
        editProfileData.signUpData = existingSignUpData;
      }

      // Handle contactInfo
      let existingContactInfo = loggedInUser.contactInfo || [];

      const publicAccountRecord = await this.userSignupDataModel.findOne({
        title: 'profile_open_to',
        slug: 'public_1',
      });

      const isChangeToPublic =
        Array.isArray(editProfileData?.signUpData) &&
        editProfileData?.signUpData?.find(
          (item) =>
            item?.itemId?.toString() === publicAccountRecord?._id?.toString() &&
            item.isSelected,
        );

      const hirerRecord = await this.userSignupDataModel.findOne({
        slug: 'employer_1',
        title: 'more_about_you',
      });

      if (Array.isArray(editProfileData?.signUpData)) {
        editProfileData.signUpData = editProfileData.signUpData.map((item) => {
          if (item.itemId.toString() === hirerRecord._id.toString()) {
            const oldValue = loggedInUser.signUpData.find(
              (item) => item?.itemId?.toString() === hirerRecord._id.toString(),
            );

            if (oldValue.isSelected && !item.isSelected) {
              editProfileData.hirerEmployerVerifiedStatus = null;
            }

            return {
              itemId: item.itemId,
              isSelected:
                !oldValue.isSelected && item.isSelected
                  ? false
                  : item.isSelected,
            };
          }
          return item;
        });
      }

      if (isChangeToPublic) {
        const checkPendingRequest = await this?.followerInfoModel?.find({
          followingId: loggedInUser._id,
          status: StatusEnum.PENDING,
        });

        const followerIds = checkPendingRequest.map((req) => req.followerId);

        // accept all follow requests
        await this.followerInfoModel.updateMany(
          {
            followingId: loggedInUser._id,
          },
          {
            status: StatusEnum.ACCEPT,
          },
        );

        await this.userModel.findByIdAndUpdate(loggedInUser._id, {
          $set: {
            followers: loggedInUser.followers + checkPendingRequest.length,
          },
        });

        await this.userModel.updateMany(
          { _id: { $in: followerIds } },
          { $inc: { following: 1 } },
        );
        // delete sent notifications
        await this.notificationModel.deleteMany({
          notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
          redirectionType: RedirectionType.USER,
          receiver: loggedInUser._id,
        });
      }
      // Check if `contactInfo` exists in the payload
      if (
        editProfileData.contactInfo &&
        Array.isArray(editProfileData.contactInfo)
      ) {
        // Create a map of existing _id values
        const payloadIds = editProfileData.contactInfo
          .filter((newContactInfo) => newContactInfo._id)
          .map((newContactInfo) => newContactInfo._id.toString());

        // Filter out contactInfo entries that are not in the incoming payload
        existingContactInfo = existingContactInfo.filter((contact) =>
          payloadIds.includes(contact._id.toString()),
        );

        editProfileData.contactInfo.forEach((newContactInfo) => {
          if (newContactInfo._id) {
            // If `_id` exists, update the existing record
            existingContactInfo = existingContactInfo.map((contact) => {
              if (contact._id.toString() === newContactInfo._id) {
                return {
                  ...contact,
                  ...newContactInfo, // Update the contact info
                };
              }
              return contact;
            });
          } else {
            // If `_id` is null, remove it and add a new record
            const { _id, ...newContactWithoutId } = newContactInfo;
            existingContactInfo.push(newContactWithoutId); // Add new contact info
          }
        });

        // Update the contactInfo in the editProfileData object
        editProfileData.contactInfo = existingContactInfo;
      }

      // Handle socialMedia if present
      if (editProfileData.socialMedia) {
        loggedInUser.socialMedia = {
          ...loggedInUser.socialMedia,
          ...editProfileData.socialMedia,
        };
      }

      // Update User Profile About
      if (editProfileData.userProfileAboutYou) {
        loggedInUser.userProfileAboutYou = {
          ...loggedInUser.userProfileAboutYou,
          ...editProfileData.userProfileAboutYou,
        };
        editProfileData['userProfileAboutYou'] = {
          ...loggedInUser.userProfileAboutYou,
          ...editProfileData.userProfileAboutYou,
        };
      }

      const data = {
        ...editProfileData,
      };

      if ('accountVerified' in data && loggedInUser.accountVerified === true) {
        delete data.accountVerified;
      }

      const userData = await this.userModel.findByIdAndUpdate(
        loggedInUser._id,
        data,
      );

      if (
        Array.isArray(editProfileData.parentUserIds) &&
        editProfileData.parentUserIds.length > 0
      ) {
        editProfileData.parentUserIds.forEach(async (parentUserId) => {
          const isExist = await this.peopleModel.findOne({
            childUserId: loggedInUser._id,
            parentUserId: new mongoose.Types.ObjectId(parentUserId),
            isAlumni: false,
            isStudent: false,
          });

          if (!isExist) {
            const data = {
              childUserId: loggedInUser._id,
              parentUserId: new mongoose.Types.ObjectId(parentUserId),
            };
            await new this.peopleModel(data).save();
          } else if (isExist.status === StatusEnum.REJECT) {
            await this.peopleModel.findByIdAndUpdate(
              isExist._id,
              {
                status: StatusEnum.PENDING,
              },
              {
                new: true,
                runValidators: true,
              },
            );
          }
        });
      }

      //update user profileImage in stream
      const chatObj = {
        id: `${userData._id}`,
        profile_image: userData.profileImage,
        name:
          userData.firstName && userData.lastName
            ? `${userData.firstName} ${userData.lastName}`
            : userData.businessOrganizationName,
        userName: userData.userName ?? userData.businessOrganizationName,
        businessOrganizationName: userData.businessOrganizationName,
      };

      await this.streamChatService.createOrUpdateUserForChat(chatObj);

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Profile'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async sendMemberVerificationRequest(payload: memberVerificationDto) {
    try {
      const isExist = await this.peopleModel.findById(payload.id);

      if (!isExist) {
        throw new NotFoundException(
          CONSTANT.NOT_FOUND_MESSAGE('membership request'),
        );
      }

      if (isExist.status === StatusEnum.ACCEPT) {
        throw new HttpException(
          { message: `Membership request already ${isExist.status}ed` },
          HttpStatus.BAD_REQUEST,
        );
      }

      await isExist.updateOne({
        status: StatusEnum.PENDING,
        document: payload.document,
        idNumber: payload.idNumber,
        email: payload.email,
      });

      // // notification
      const params = {
        title: NOTIFICATION_MESSAGES.MEMBERSHIP_REQUEST_SENT,
        notificationMessage: '',
        notificationType: NotificationsType.MEMBER,
        redirectionType: RedirectionType.USER,
        receiver: isExist.parentUserId,
        sender: isExist.childUserId,
        postId: null,
        connectionId: null,
        memberId: isExist._id,
        action: InvitationType.INITIAL,
      };

      this.notificationService.sendToTopic(null, params);

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Membership request'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async followUnfollow(req, followData) {
    try {
      const { user: follower } = req;

      if (follower._id === followData.followingId) {
        throw new HttpException(
          { message: 'Same Following Id' },
          HttpStatus.FORBIDDEN,
        );
      }

      // Find the user to be followed using aggregation to also lookup signupdata and determine public/private
      const followingUserArr = await this.userModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(
              followData.followingId?.toString(),
            ),
          },
        },
        {
          $unwind: {
            path: '$signUpData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'signUpData.itemId',
            foreignField: '_id',
            as: 'signUpDetails',
          },
        },
        {
          $unwind: {
            path: '$signUpDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'signUpData.itemId': {
              _id: '$signUpDetails._id',
              title: '$signUpDetails.title',
              slug: '$signUpDetails.slug',
              itemText: '$signUpDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: '$_id',
            doc: { $first: '$$ROOT' },
            signUpData: { $push: '$signUpData' },
          },
        },
        {
          $addFields: {
            'doc.signUpData': '$signUpData',
          },
        },
        {
          $replaceRoot: { newRoot: '$doc' },
        },
      ]);

      const followingUser = followingUserArr[0];

      if (!followingUser) {
        throw new HttpException(
          { message: 'User not found' },
          HttpStatus.NOT_FOUND,
        );
      }

      // Check if already following
      const followingExist = await this.followerInfoModel.findOne({
        followerId: follower._id,
        followingId: new mongoose.Types.ObjectId(
          String(followData.followingId),
        ),
      });

      // Determine if the account is private
      let isPrivateAccount = false;
      if (followingUser.signUpData && Array.isArray(followingUser.signUpData)) {
        const isPublicAccount = followingUser.signUpData.find(
          (item: any) =>
            item.isSelected &&
            item.itemId.title === 'profile_open_to' &&
            item.itemId.slug === 'public_1',
        );

        if (!isPublicAccount) {
          isPrivateAccount = true;
        }
      }

      if (!followingExist) {
        // If private, create a pending follow request
        if (isPrivateAccount) {
          const data = {
            followerId: follower._id,
            followingId: followData.followingId,
            status: StatusEnum.PENDING,
          };
          await new this.followerInfoModel(data).save();

          // Send notification for follow request
          const params = {
            title: NOTIFICATION_MESSAGES.FOLLOW_REQUEST_SENT,
            notificationMessage: '',
            notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
            redirectionType: RedirectionType.USER,
            receiver: followData.followingId,
            sender: follower._id,
            postId: null,
            action: InvitationType.INITIAL,
          };

          this.notificationService.sendToTopic(null, params);

          return successResponse(
            null,
            CONSTANT.FOLLOW_REQUEST_SENT,
            HttpStatus.OK,
          );
        } else {
          // Public account: directly follow (status: ACCEPT)
          const data = {
            followerId: follower._id,
            followingId: followData.followingId,
            status: StatusEnum.ACCEPT,
          };
          const followUser = await new this.followerInfoModel(data).save();

          const followerUserCount = await this.userModel.findOne({
            _id: followUser.followingId,
            isFakeAccount: false,
          });
          if (followerUserCount) {
            followerUserCount.followers += 1;
            await followerUserCount.save();
          }

          const followingUserCount = await this.userModel.findOne({
            _id: followUser.followerId,
            isFakeAccount: false,
          });
          if (followingUserCount) {
            followingUserCount.following += 1;
            await followingUserCount.save();
          }

          const params = {
            title: NOTIFICATION_MESSAGES.FOLLOW_YOU,
            notificationMessage: '',
            notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
            redirectionType: RedirectionType.USER,
            receiver: followUser.followingId,
            sender: followUser.followerId,
            postId: null,
          };

          this.notificationService.sendToTopic(null, params);

          return successResponse(null, CONSTANT.FOLLOW_USER, HttpStatus.OK);
        }
      }

      // If already following or request exists, unfollow or cancel request
      await this.followerInfoModel.deleteOne({
        _id: followingExist.id,
      });

      // delete sent notifications
      await this.notificationModel.deleteMany({
        notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
        redirectionType: RedirectionType.USER,
        receiver: followingExist.followingId,
        sender: followingExist.followerId,
      });

      if (followingExist.status === StatusEnum.ACCEPT) {
        const followerUserCount = await this.userModel.findOne({
          _id: followingExist.followingId,
          isFakeAccount: false,
        });
        if (followerUserCount) {
          followerUserCount.followers -= 1;
          await followerUserCount.save();
        }

        const followingUserCount = await this.userModel.findOne({
          _id: followingExist.followerId,
          isFakeAccount: false,
        });
        if (followingUserCount) {
          followingUserCount.following -= 1;
          await followingUserCount.save();
        }
      }

      return successResponse(
        null,
        isPrivateAccount
          ? CONSTANT.FOLLOW_REQUEST_CANCELLED
          : CONSTANT.UNFOLLOW_USER,
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async acceptRejectFollowRequest(req, data: AcceptRejectfollowDto) {
    try {
      const { user: loggedInUser } = req;
      const { followerId, status } = data;

      if (!['accept', 'reject'].includes(status)) {
        throw new HttpException(
          { message: 'Invalid status' },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Find the pending follow request
      const followRequest = await this.followerInfoModel.findOne({
        followerId: new mongoose.Types.ObjectId(followerId),
        followingId: loggedInUser._id,
        status: StatusEnum.PENDING,
      });

      if (!followRequest) {
        throw new HttpException(
          { message: 'Follow request not found' },
          HttpStatus.NOT_FOUND,
        );
      }

      if (status === StatusEnum.ACCEPT) {
        // Update status to ACCEPT
        followRequest.status = StatusEnum.ACCEPT;
        await followRequest.save();

        // Increment counts
        const followerUser = await this.userModel.findOne({
          _id: followRequest.followerId,
          isFakeAccount: false,
        });
        if (followerUser) {
          followerUser.following += 1;
          await followerUser.save();
        }

        const followingUser = await this.userModel.findOne({
          _id: followRequest.followingId,
          isFakeAccount: false,
        });
        if (followingUser) {
          followingUser.followers += 1;
          await followingUser.save();
        }

        // delete sent notifications
        await this.notificationModel.deleteMany({
          notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
          redirectionType: RedirectionType.USER,
          receiver: followRequest.followingId,
          sender: followRequest.followerId,
        });

        // Send notification for accepted follow
        const params = {
          title: NOTIFICATION_MESSAGES.ACCEPTED_FOLLOWING,
          notificationMessage: '',
          notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
          redirectionType: RedirectionType.USER,
          receiver: followRequest.followerId,
          sender: followRequest.followingId,
          postId: null,
        };
        this.notificationService.sendToTopic(null, params);

        return successResponse(
          null,
          CONSTANT.ACCEPT_REQUEST('follow'),
          HttpStatus.OK,
        );
      } else {
        await this.followerInfoModel.deleteOne({ _id: followRequest._id });

        // delete sent notifications
        await this.notificationModel.deleteMany({
          notificationType: PostCollaboratorTagPeopleEnum.FOLLOWING,
          redirectionType: RedirectionType.USER,
          receiver: loggedInUser._id,
          sender: followRequest.followerId,
        });

        return successResponse(
          null,
          CONSTANT.REJECT_REQUEST('follow'),
          HttpStatus.OK,
        );
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async sentConnectionRequest(
    req,
    connectionRequestData: connectionRequestDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      if (
        loggedInUser._id.toString() === connectionRequestData.connectionWithId
      ) {
        throw new HttpException({ message: 'Same User' }, HttpStatus.FORBIDDEN);
      }

      const userExist = await this.userModel.findById(
        connectionRequestData.connectionWithId,
      );

      if (!userExist) {
        throw new HttpException(
          { message: CONSTANT.NOT_FOUND_MESSAGE('User') },
          HttpStatus.NOT_FOUND,
        );
      }

      const connectionExist = await this.connectionInfoModel.findOne({
        $or: [
          {
            userId: loggedInUser._id,
            connectionWithId: new mongoose.Types.ObjectId(
              connectionRequestData.connectionWithId,
            ),
          },
          {
            userId: new mongoose.Types.ObjectId(
              connectionRequestData.connectionWithId,
            ),
            connectionWithId: loggedInUser._id,
          },
        ],
      });

      if (!connectionExist) {
        const data = {
          userId: loggedInUser._id,
          connectionWithId: new mongoose.Types.ObjectId(
            connectionRequestData.connectionWithId,
          ),
          status: StatusEnum.PENDING,
        };
        const connections = await new this.connectionInfoModel(data).save();

        const followingExist = await this.followerInfoModel.findOne({
          followerId: connections.userId,
          followingId: connections.connectionWithId,
          status: StatusEnum.ACCEPT,
        });

        if (!followingExist) {
          await this.followUnfollow(req, {
            followingId: connectionRequestData.connectionWithId,
          });
        }
        const params = {
          title: NOTIFICATION_MESSAGES.CONNECTION_REQUEST_SENT,
          notificationMessage: '',
          notificationType: PostCollaboratorTagPeopleEnum.CONNECTION,
          redirectionType: RedirectionType.USER,
          receiver: connections.connectionWithId,
          sender: connections.userId,
          postId: null,
          connectionId: connections._id,
          action: InvitationType.INITIAL,
        };

        this.notificationService.sendToTopic(null, params);
      } else {
        if (connectionExist.status === StatusEnum.ACCEPT) {
          await this.userModel.findByIdAndUpdate(connectionExist.userId, [
            {
              $set: {
                connections: {
                  $max: [{ $subtract: ['$connections', 1] }, 0],
                },
              },
            },
          ]);

          await this.userModel.findByIdAndUpdate(
            connectionExist.connectionWithId,
            [
              {
                $set: {
                  connections: {
                    $max: [{ $subtract: ['$connections', 1] }, 0],
                  },
                },
              },
            ],
          );
        }

        await this.notificationModel.deleteMany({
          connectionId: connectionExist._id,
        });

        await this.connectionInfoModel.deleteOne({ _id: connectionExist._id });

        return successResponse(
          null,
          CONSTANT.REMOVE('connection'),
          HttpStatus.OK,
        );
      }

      return successResponse(
        null,
        CONSTANT.SENT_MESSAGE('Connection request'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async sendClientrequest(req, clientRequestData: clientRequestDto) {
    try {
      const { user: loggedInUser } = req;

      if (loggedInUser._id.toString() === clientRequestData.clientId) {
        throw new HttpException({ message: 'Same User' }, HttpStatus.FORBIDDEN);
      }

      const userExist = await this.userModel.findById(
        clientRequestData.clientId,
      );

      if (!userExist) {
        throw new HttpException(
          { message: CONSTANT.NOT_FOUND_MESSAGE('User') },
          HttpStatus.NOT_FOUND,
        );
      }

      const clientExist = await this.clientInfoModel.findOne({
        userId: loggedInUser._id,
        clientId: new mongoose.Types.ObjectId(clientRequestData.clientId),
      });

      if (!clientExist) {
        const data = {
          userId: loggedInUser._id,
          clientId: new mongoose.Types.ObjectId(clientRequestData.clientId),
        };

        const clients = await new this.clientInfoModel(data).save();

        const params = {
          title: NOTIFICATION_MESSAGES.ADD_CLIENT_NOTIFICATION,
          notificationMessage: '',
          notificationType: NotificationsType.CLIENT,
          redirectionType: RedirectionType.USER,
          receiver: clients.clientId,
          sender: clients.userId,
          postId: null,
          clientId: clients._id,
          action: InvitationType.INITIAL,
        };

        this.notificationService.sendToTopic(null, params);

        const params2 = {
          title: NOTIFICATION_MESSAGES.PENDING_CLIENT_REQUEST_NOTIFICATION,
          notificationMessage: '',
          notificationType: NotificationsType.CLIENT,
          redirectionType: RedirectionType.USER,
          receiver: clients.userId,
          sender: clients.clientId,
        };

        this.notificationService.sendToTopic(null, params2);
      }

      if (clientExist && clientExist.status === StatusEnum.REJECT) {
        await this.clientInfoModel.findByIdAndUpdate(
          clientExist._id,
          {
            status: StatusEnum.PENDING,
          },
          {
            new: true,
            runValidators: true,
          },
        );
      } else if (
        clientExist &&
        (clientExist.status === StatusEnum.ACCEPT ||
          clientExist.status === StatusEnum.PENDING)
      ) {
        await this.notificationModel.deleteMany({
          clientId: clientExist._id,
        });

        await this.clientInfoModel.deleteOne({ _id: clientExist._id });

        return successResponse(
          null,
          CONSTANT.REMOVE('Client Request '),
          HttpStatus.OK,
        );
      }

      return successResponse(
        null,
        CONSTANT.SENT_MESSAGE('Client request'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getConnectionRequests(req) {
    try {
      const { user: loggedInUser } = req;

      const queryData: ParsedQs = req.query;
      let page = Number(queryData.page);
      let perPage = Number(queryData.perPage);

      if (!page) page = 1;
      if (!perPage) perPage = 10;

      const connectionRequests: any = await this.connectionInfoModel
        .find({
          userId: loggedInUser._id,
          status: StatusEnum.PENDING,
        })
        .select('-updatedAt -__v')
        .populate({
          path: 'userId',
          match: { isFakeAccount: false },
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        })
        .populate({
          path: 'connectionWithId',
          match: { isFakeAccount: false },
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        })
        .sort({ createdAt: -1 })
        .skip((page - 1) * perPage)
        .limit(perPage);

      const connectionRequests_data = await Promise.all(
        connectionRequests.map(async (i) => {
          const connectionRequestsData = { ...i }._doc;
          return connectionRequestsData;
        }),
      );

      const connectionRequestsCount: any = await this.connectionInfoModel
        .find({
          userId: loggedInUser._id,
          status: StatusEnum.PENDING,
        })
        .countDocuments();

      const totalResults: any = connectionRequestsCount;
      const currentResults = connectionRequests.length;
      let totalPages = Math.ceil(totalResults / perPage);
      totalPages = totalPages == 0 ? 1 : totalPages;
      const currentPage = page;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        connectionRequests_data,
        CONSTANT.FETCHED_SUCCESSFULLY('Connection requests'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getConnectionslist(req, user_Id) {
    try {
      const type = req.query.type;

      const queryData: ParsedQs = req.query;
      let page = Number(queryData.page);
      let perPage = Number(queryData.perPage);

      if (!page) page = 1;
      if (!perPage) perPage = 10;

      if (type === 'FOLLOWER' || type === 'FOLLOWING') {
        return this.getFollowersFollowingList(req, {
          userId: user_Id,
          type: type,
        });
      } else if (type === 'CONNECTION') {
        const userObjectId = new mongoose.Types.ObjectId(user_Id);

        const matchObj = {
          $or: [
            { connectionWithId: userObjectId, status: StatusEnum.ACCEPT },
            { userId: userObjectId, status: StatusEnum.ACCEPT },
          ],
        };

        const connections = await this.connectionInfoModel.aggregate([
          {
            $match: matchObj,
          },
          {
            $lookup: {
              from: 'users',
              localField: 'connectionWithId',
              foreignField: '_id',
              as: 'connectionWithUser',
              pipeline: [
                {
                  $match: {
                    isFakeAccount: false,
                  },
                },
                {
                  $project: {
                    _id: 1,
                    firstName: 1,
                    lastName: 1,
                    userName: 1,
                    profileImage: 1,
                    businessOrganizationName: 1,
                    followers: 1,
                    following: 1,
                    connections: 1,
                    accountVerified: 1,
                    iAmMember: 1,
                    professions: 1,
                  },
                },
              ],
            },
          },
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: '_id',
              as: 'user',
              pipeline: [
                {
                  $match: {
                    isFakeAccount: false,
                  },
                },
                {
                  $project: {
                    _id: 1,
                    firstName: 1,
                    lastName: 1,
                    userName: 1,
                    profileImage: 1,
                    businessOrganizationName: 1,
                    followers: 1,
                    following: 1,
                    connections: 1,
                    accountVerified: 1,
                    iAmMember: 1,
                    professions: 1,
                  },
                },
              ],
            },
          },
          {
            $addFields: {
              connectionWithUser: { $arrayElemAt: ['$connectionWithUser', 0] },
              user: { $arrayElemAt: ['$user', 0] },
            },
          },
          {
            $project: {
              _id: 1,
              createdAt: 1,
              connectedUser: {
                $cond: {
                  if: { $eq: ['$userId', userObjectId] },
                  then: '$connectionWithUser',
                  else: '$user',
                },
              },
            },
          },
          {
            $sort: { createdAt: -1 },
          },
          {
            $skip: (page - 1) * perPage,
          },
          {
            $limit: perPage,
          },
        ]);

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const connectionsCount: any = await this.connectionInfoModel
          .find(matchObj)
          .countDocuments();

        const totalResults: any = connectionsCount;
        const currentResults = connections.length;
        let totalPages = Math.ceil(totalResults / perPage);
        totalPages = totalPages == 0 ? 1 : totalPages;
        const currentPage = page;

        const paginationObj = {
          totalResults,
          currentResults,
          totalPages,
          currentPage,
        };

        return successResponse(
          connections.map((c) => c.connectedUser),
          CONSTANT.FETCHED_SUCCESSFULLY('Connection list'),
          HttpStatus.OK,
          paginationObj,
        );
      } else if (type === 'SUBSCRIBER') {
        return this.getSubscribers(req, user_Id);
      } else if (type === 'VIEWER' || type === 'VIEWBYME') {
        return this.getViewerOrViewByMe(req, user_Id, type);
      } else {
        throw new HttpException(
          { message: 'Invalid type' },
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async connectionRequestAcceptReject(
    req,
    conectionRequestAcceptRejectData,
  ) {
    try {
      const { user: loggedInUser } = req;

      const requestExist = await this.connectionInfoModel.findOne({
        _id: new mongoose.Types.ObjectId(
          conectionRequestAcceptRejectData.requestId,
        ),
        connectionWithId: loggedInUser._id,
      });

      if (!requestExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Connection request'),
          HttpStatus.NOT_FOUND,
        );
      }

      if (
        requestExist.status === connectionRequestStatus.ACCEPT &&
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.ACCEPT
      ) {
        return successResponse(
          null,
          CONSTANT.ALREADY_ACCEPTED('connection'),
          HttpStatus.OK,
        );
      }

      if (
        requestExist.status === connectionRequestStatus.REJECT &&
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.REJECT
      ) {
        return successResponse(
          null,
          CONSTANT.ALREADY_REJECTED('connection'),
          HttpStatus.OK,
        );
      }

      if (
        requestExist.status === connectionRequestStatus.CANCEL &&
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.CANCEL
      ) {
        return successResponse(null, CONSTANT.ALREADY_CANCELLED, HttpStatus.OK);
      }

      if (
        requestExist.status === connectionRequestStatus.REMOVE &&
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.REMOVE
      ) {
        return successResponse(null, CONSTANT.ALREADY_REMOVE, HttpStatus.OK);
      }

      if (
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.ACCEPT ||
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.REJECT
      ) {
        await this.connectionInfoModel.findByIdAndUpdate(
          requestExist._id,
          {
            status: conectionRequestAcceptRejectData.status,
          },
          {
            new: true,
            runValidators: true,
          },
        );

        let action = 'rejected';

        let notificationAction = InvitationType.REJECT;

        if (
          conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.ACCEPT
        ) {
          action = 'accepted';

          notificationAction = InvitationType.ACCEPT;

          await this.userModel.findByIdAndUpdate(requestExist.userId, {
            $inc: { connections: 1 },
          });
          await this.userModel.findByIdAndUpdate(
            requestExist.connectionWithId,
            {
              $inc: { connections: 1 },
            },
          );
        } else {
          await this.connectionInfoModel.deleteOne(requestExist._id);

          // decrement connection count if rejected
          await this.userModel.findByIdAndUpdate(requestExist.userId, {
            $inc: { connections: -1 },
          });
          await this.userModel.findByIdAndUpdate(
            requestExist.connectionWithId,
            {
              $inc: { connections: -1 },
            },
          );
        }

        await this.notificationModel.deleteOne({
          _id: new mongoose.Types.ObjectId(
            conectionRequestAcceptRejectData.notificationId,
          ),
        });

        const params = {
          title: NOTIFICATION_MESSAGES.CONNECTION_REQUEST_STATUS(action),
          notificationMessage: '',
          notificationType: NotificationsType.CONNECTION,
          redirectionType: RedirectionType.USER,
          receiver: requestExist.userId,
          sender: loggedInUser._id,
          postId: null,
          connectionId: null,
          action: notificationAction,
        };

        this.notificationService.sendToTopic(null, params);

        const params2 = {
          title: NOTIFICATION_MESSAGES.CONNECTION_REQUEST_STATUS(action),
          notificationMessage: '',
          notificationType: NotificationsType.CONNECTION,
          redirectionType: RedirectionType.USER,
          receiver: loggedInUser._id,
          sender: requestExist.userId,
          postId: null,
          connectionId: null,
          action: notificationAction,
        };

        this.notificationService.sendToTopic(null, params2);

        return successResponse(
          null,
          conectionRequestAcceptRejectData.status ===
            connectionRequestStatus.ACCEPT
            ? CONSTANT.ACCEPT_REQUEST('connection')
            : CONSTANT.REJECT_REQUEST('connection'),
          HttpStatus.OK,
        );
      }

      if (
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.CANCEL ||
        conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.REMOVE
      ) {
        if (
          conectionRequestAcceptRejectData.status ===
          connectionRequestStatus.REMOVE
        ) {
          await this.userModel.findByIdAndUpdate(requestExist.userId, {
            $inc: { connections: -1 },
          });
          await this.userModel.findByIdAndUpdate(
            requestExist.connectionWithId,
            {
              $inc: { connections: -1 },
            },
          );
        }

        await this.connectionInfoModel.deleteOne(requestExist._id);

        return successResponse(
          null,
          conectionRequestAcceptRejectData.status ===
            connectionRequestStatus.CANCEL
            ? CONSTANT.CANCEL_REQUEST
            : CONSTANT.REMOVE('connection'),
          HttpStatus.OK,
        );
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
  public async clientRequestAcceptReject(
    req,
    clientRequestAcceptRejectData: clientRequestAcceptRejectDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      const requestExist = await this.clientInfoModel.findOne({
        _id: new mongoose.Types.ObjectId(
          clientRequestAcceptRejectData.requestId,
        ),
      });

      if (!requestExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Client request'),
          HttpStatus.NOT_FOUND,
        );
      }

      // Remove client functionality
      if (clientRequestAcceptRejectData.status === StatusEnum.REMOVE) {
        await this.clientInfoModel.deleteOne({ _id: requestExist._id });

        await this.notificationModel.deleteMany({
          notificationType: NotificationsType.CLIENT,
          redirectionType: RedirectionType.USER,
          $or: [
            { receiver: requestExist.userId, sender: requestExist.clientId },
            { receiver: requestExist.clientId, sender: requestExist.userId },
          ],
        });

        return successResponse(null, CONSTANT.REMOVE('client'), HttpStatus.OK);
      }

      if (
        requestExist.status === StatusEnum.ACCEPT &&
        clientRequestAcceptRejectData.status === StatusEnum.ACCEPT
      ) {
        return successResponse(null, CONSTANT.ALREADY_ACCEPTED, HttpStatus.OK);
      }

      if (
        requestExist.status === StatusEnum.REJECT &&
        clientRequestAcceptRejectData.status === StatusEnum.REJECT
      ) {
        return successResponse(null, CONSTANT.ALREADY_REJECTED, HttpStatus.OK);
      }

      if (
        clientRequestAcceptRejectData.status === StatusEnum.ACCEPT ||
        clientRequestAcceptRejectData.status === StatusEnum.REJECT
      ) {
        await this.clientInfoModel.findByIdAndUpdate(
          requestExist._id,
          {
            status: clientRequestAcceptRejectData.status,
          },
          {
            new: true,
            runValidators: true,
          },
        );

        const action =
          clientRequestAcceptRejectData.status === StatusEnum.ACCEPT
            ? 'accepted'
            : 'rejected';

        const notificationAction =
          clientRequestAcceptRejectData.status === StatusEnum.ACCEPT
            ? InvitationType.ACCEPT
            : InvitationType.REJECT;

        await this.notificationModel.deleteMany({
          notificationType: NotificationsType.CLIENT,
          redirectionType: RedirectionType.USER,
          receiver: requestExist.userId,
          sender: requestExist.clientId,
        });

        await this.notificationModel.deleteOne({
          _id: new mongoose.Types.ObjectId(
            clientRequestAcceptRejectData.notificationId,
          ),
        });

        const params = {
          title: NOTIFICATION_MESSAGES.CLIENT_REQUEST_STATUS(
            '{{reciver}}',
            action,
          ),
          notificationMessage: '',
          notificationType: NotificationsType.CLIENT,
          redirectionType: RedirectionType.USER,
          receiver: requestExist.userId,
          sender: loggedInUser._id,
          postId: null,
          clientId: null,
          action: notificationAction,
        };

        this.notificationService.sendToTopic(null, params);

        const params2 = {
          title: NOTIFICATION_MESSAGES.CLIENT_REQUEST_STATUS(
            '{{sender}}',
            action,
          ),
          notificationMessage: '',
          notificationType: NotificationsType.CLIENT,
          redirectionType: RedirectionType.USER,
          receiver: loggedInUser._id,
          sender: requestExist.userId,
          postId: null,
          clientId: null,
          action: notificationAction,
        };

        this.notificationService.sendToTopic(null, params2);

        return successResponse(
          null,
          clientRequestAcceptRejectData.status === StatusEnum.ACCEPT
            ? CONSTANT.ACCEPT_REQUEST('client')
            : CONSTANT.REJECT_REQUEST('client'),
          HttpStatus.OK,
        );
      } else {
        return successResponse(
          null,
          CONSTANT.INVALID(' Status'),
          HttpStatus.OK,
        );
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async memberRequestAcceptReject(
    req,
    memberRequestData: clientRequestAcceptRejectDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      const requestExist = await this.peopleModel.findOne({
        _id: new mongoose.Types.ObjectId(memberRequestData.requestId),
      });

      if (!requestExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('membership request'),
          HttpStatus.NOT_FOUND,
        );
      }

      if (memberRequestData.status === StatusEnum.REMOVE) {
        await this.peopleModel.deleteOne({ _id: requestExist._id });

        if (
          requestExist.childUserId?.toString() === loggedInUser._id?.toString()
        ) {
          const countNumOfMembership = await this.peopleModel.find({
            childUserId: loggedInUser._id,
            status: StatusEnum.ACCEPT,
          });
          if (countNumOfMembership.length === 0) {
            await this.userModel.findByIdAndUpdate(loggedInUser._id, {
              isMembershipVerified: false,
            });
          }
        } else if (
          requestExist.parentUserId?.toString() === loggedInUser._id?.toString()
        ) {
          const countNumOfMembership = await this.peopleModel.find({
            childUserId: requestExist.childUserId,
            status: StatusEnum.ACCEPT,
          });
          if (countNumOfMembership.length === 0) {
            await this.userModel.findByIdAndUpdate(requestExist.childUserId, {
              isMembershipVerified: false,
            });
          }
        }
        await this.notificationModel.deleteMany({
          memberId: requestExist._id,
        });
        return successResponse(null, CONSTANT.REMOVE('member'), HttpStatus.OK);
      }

      if (
        requestExist.status === StatusEnum.ACCEPT &&
        memberRequestData.status === StatusEnum.ACCEPT
      ) {
        return successResponse(
          null,
          CONSTANT.ALREADY_ACCEPTED('membership'),
          HttpStatus.OK,
        );
      }

      if (
        requestExist.status === StatusEnum.REJECT &&
        memberRequestData.status === StatusEnum.REJECT
      ) {
        return successResponse(
          null,
          CONSTANT.ALREADY_REJECTED('membership'),
          HttpStatus.OK,
        );
      }

      if (
        memberRequestData.status === StatusEnum.ACCEPT ||
        memberRequestData.status === StatusEnum.REJECT
      ) {
        await this.peopleModel.findByIdAndUpdate(
          requestExist._id,
          {
            status: memberRequestData.status,
          },
          {
            new: true,
            runValidators: true,
          },
        );

        if (StatusEnum.ACCEPT === memberRequestData.status) {
          await this.userModel.findByIdAndUpdate(requestExist.childUserId, {
            $set: {
              isMembershipVerified: true,
            },
          });
        }

        const action =
          memberRequestData.status === StatusEnum.ACCEPT
            ? 'accepted'
            : 'rejected';

        const notificationAction =
          memberRequestData.status === StatusEnum.ACCEPT
            ? InvitationType.ACCEPT
            : InvitationType.REJECT;

        await this.notificationModel.deleteOne({
          _id: new mongoose.Types.ObjectId(memberRequestData.notificationId),
        });

        const params = {
          title: NOTIFICATION_MESSAGES.MEMBERSHIP_REQUEST_STATUS(action),
          notificationMessage: '',
          notificationType: NotificationsType.MEMBER,
          redirectionType: RedirectionType.USER,
          receiver: requestExist.childUserId,
          sender: loggedInUser._id,
          postId: null,
          clientId: null,
          action: notificationAction,
        };

        this.notificationService.sendToTopic(null, params);

        const params2 = {
          title: NOTIFICATION_MESSAGES.MEMBERSHIP_REQUEST_STATUS(action),
          notificationMessage: '',
          notificationType: NotificationsType.MEMBER,
          redirectionType: RedirectionType.USER,
          receiver: loggedInUser._id,
          sender: requestExist.childUserId,
          postId: null,
          clientId: null,
          action: notificationAction,
        };

        this.notificationService.sendToTopic(null, params2);

        return successResponse(
          null,
          memberRequestData.status === StatusEnum.ACCEPT
            ? CONSTANT.ACCEPT_REQUEST('membership')
            : CONSTANT.REJECT_REQUEST('membership'),
          HttpStatus.OK,
        );
      } else {
        return successResponse(null, CONSTANT.INVALID('Status'), HttpStatus.OK);
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getFollowersFollowingList(
    req,
    followData: getFollowersFollowingDto,
  ) {
    try {
      const { user: loggedInUser } = req;

      const getUser = await this.userModel.findOne({
        _id: new mongoose.Types.ObjectId(followData.userId),
        isFakeAccount: false,
      });
      if (!getUser) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const queryData: ParsedQs = req.query;
      let page = Number(queryData.page);
      let perPage = Number(queryData.perPage);
      const isForStorySharing = req?.query?.isForStorySharing;

      if (!page) page = 1;
      if (!perPage) perPage = 10;

      let result,
        totalResults,
        currentResults,
        totalPages,
        currentPage,
        responseMessage;

      const isFollow: any = await this.followerInfoModel.find({
        followerId: loggedInUser._id,
        status: StatusEnum.ACCEPT,
      });

      if (followData.type === 'FOLLOWING') {
        const data: any = await this.followerInfoModel
          .find({
            followerId: new mongoose.Types.ObjectId(followData.userId),
            status: StatusEnum.ACCEPT,
          })
          .select('followingId -_id')
          .populate(
            'followingId',
            '_id firstName lastName userName businessOrganizationName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
          )
          .sort({ createdAt: -1 })
          // .skip((page - 1) * perPage)
          // .limit(perPage)
          .lean();

        let followingData = await Promise.all(
          data?.map(async (i) => {
            const datas: any = { ...i.followingId, isFollowing: null };

            await Promise.all(
              isFollow.map(async (f) => {
                if (f.followingId.equals(i.followingId._id)) {
                  datas.isFollowing = StatusEnum.ACCEPT;
                }
              }),
            );
            return datas;
          }),
        );

        // Apply search filter if search query is provided
        if (queryData.search) {
          const searchArr = (queryData.search as string).split(' ');
          const regexSearch = searchArr.map((ele) => new RegExp(ele, 'i'));

          followingData = followingData?.filter((follower: any) =>
            ['firstName', 'lastName', 'userName'].some((key: string) =>
              regexSearch.some((regex: any) => regex.test(follower[key])),
            ),
          );
        }

        // let followingCount = await this.followerInfoModel.countDocuments({
        //   followerId: new mongoose.Types.ObjectId(followData.userId),
        // });

        // result = followingData;
        totalResults = followingData?.length;
        // currentResults = result.length;
        totalPages = Math.max(Math.ceil(totalResults / perPage), 1);
        totalPages = totalPages == 0 ? 1 : totalPages;
        currentPage = Math.min(Math.max(1, page), totalPages);

        // Apply pagination
        const startIndex = (currentPage - 1) * perPage;
        const endIndex = Math.min(startIndex + perPage, totalResults);
        const paginatedData = followingData?.slice(startIndex, endIndex);

        result = paginatedData;
        currentResults = paginatedData.length;
        responseMessage = CONSTANT.FETCHED_SUCCESSFULLY('Following');
      } else if (followData.type === 'FOLLOWER') {
        const data: any = await this.followerInfoModel
          .find({
            followingId: new mongoose.Types.ObjectId(followData.userId),
            status: StatusEnum.ACCEPT,
          })
          .select('followerId -_id')
          .populate(
            'followerId',
            '_id firstName lastName userName businessOrganizationName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
          )
          .sort({ createdAt: -1 })
          // .skip((page - 1) * perPage)
          // .limit(perPage)
          .lean();

        let followersData: any = await Promise.all(
          data?.map(async (i) => {
            const datas: any = { ...i.followerId, isFollowing: null };

            await Promise.all(
              isFollow.map(async (f) => {
                if (f.followingId.equals(i.followerId._id)) {
                  datas.isFollowing = StatusEnum.ACCEPT;
                }
              }),
            );
            return datas;
          }),
        );

        // Apply search filter if search query is provided
        if (queryData.search) {
          const searchArr = (queryData.search as string).split(' ');
          const regexSearch = searchArr.map((ele) => new RegExp(ele, 'i'));

          followersData = followersData.filter((follower: any) =>
            ['firstName', 'lastName', 'userName'].some((key: string) =>
              regexSearch.some((regex: any) => regex.test(follower[key])),
            ),
          );
        }

        if (isForStorySharing === 'true' || isForStorySharing === true) {
          // Pre-fetch current user with signUpData (only once)
          const currentUserAgg = await this.userModel.aggregate([
            {
              $match: {
                _id: new mongoose.Types.ObjectId(loggedInUser._id.toString()),
              },
            },
            {
              $unwind: {
                path: '$signUpData',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'usersignupdatas',
                localField: 'signUpData.itemId',
                foreignField: '_id',
                as: 'signUpDetails',
              },
            },
            {
              $unwind: {
                path: '$signUpDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                'signUpData.itemId': {
                  _id: '$signUpDetails._id',
                  title: '$signUpDetails.title',
                  slug: '$signUpDetails.slug',
                  itemText: '$signUpDetails.itemText',
                },
              },
            },
            {
              $group: {
                _id: '$_id',
                user: { $first: '$$ROOT' },
                signUpData: { $push: '$signUpData' },
              },
            },
            {
              $addFields: {
                'user.signUpData': '$signUpData',
              },
            },
            {
              $replaceRoot: {
                newRoot: '$user',
              },
            },
          ]);
          const currentUser = currentUserAgg[0];

          // Get all follower userIds
          const followerIds = followersData
            .map((user) => user?._id?.toString())
            .filter(Boolean);

          // Fetch all target users in one query
          const targetUsersAgg = await this.userModel.aggregate([
            {
              $match: {
                _id: {
                  $in: followerIds.map((id) => new mongoose.Types.ObjectId(id)),
                },
              },
            },
            {
              $unwind: {
                path: '$signUpData',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'usersignupdatas',
                localField: 'signUpData.itemId',
                foreignField: '_id',
                as: 'signUpDetails',
              },
            },
            {
              $unwind: {
                path: '$signUpDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $addFields: {
                'signUpData.itemId': {
                  _id: '$signUpDetails._id',
                  title: '$signUpDetails.title',
                  slug: '$signUpDetails.slug',
                  itemText: '$signUpDetails.itemText',
                },
              },
            },
            {
              $group: {
                _id: '$_id',
                user: { $first: '$$ROOT' },
                signUpData: { $push: '$signUpData' },
              },
            },
            {
              $addFields: {
                'user.signUpData': '$signUpData',
              },
            },
            {
              $replaceRoot: {
                newRoot: '$user',
              },
            },
          ]);
          // Map for quick lookup
          const targetUserMap = new Map(
            targetUsersAgg.map((u) => [u._id.toString(), u]),
          );

          // Validate in parallel, filter only those who pass
          const validatedFollowers = await Promise.all(
            followersData.map(async (user) => {
              const targetUser = targetUserMap.get(user?._id?.toString());
              if (!targetUser) return null;
              const value = await this.whoCanMessageValidatorService.validate(
                currentUser,
                targetUser,
              );
              return value.status ? user : null;
            }),
          );
          followersData = validatedFollowers.filter((user) => user !== null);
        }
        // let followersCount = await this.followerInfoModel.countDocuments({
        //   followingId: new mongoose.Types.ObjectId(followData.userId),
        // });

        // result = followersData;
        totalResults = followersData?.length;
        // currentResults = result.length;
        totalPages = Math.max(Math.ceil(totalResults / perPage), 1);
        totalPages = totalPages == 0 ? 1 : totalPages;
        currentPage = Math.min(Math.max(1, page), totalPages);

        // Apply pagination
        const startIndex = (currentPage - 1) * perPage;
        const endIndex = Math.min(startIndex + perPage, totalResults);
        const paginatedData = followersData.slice(startIndex, endIndex);

        result = paginatedData;
        currentResults = paginatedData.length;
        responseMessage = CONSTANT.FETCHED_SUCCESSFULLY('Followers');
      } else {
        throw new HttpException(
          { message: 'Invalid type' },
          HttpStatus.BAD_REQUEST,
        );
      }

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        result,
        responseMessage,
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getSubscribers(req, id) {
    try {
      const { search = '', page, perPage } = req.query;

      const sortParam = { sortBy: 'createdAt', sort: '-1' };
      const paginationParam = { page, perPage };

      // Create Search, Sort and Pagination
      const { searchObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          [
            'subscriberId.userName',
            'subscriberId.firstName',
            'subscriberId.lastName',
            'subscriberId.businessOrganizationName',
          ],
          null,
          sortParam,
          paginationParam,
        );

      const getUser = await this.userModel.findOne({
        _id: new mongoose.Types.ObjectId(id),
        isFakeAccount: false,
      });

      if (!getUser) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      const subscribers = await this.subscriberModel.aggregate([
        { $match: { userId: new mongoose.Types.ObjectId(id), ...searchObj } },
        {
          $lookup: {
            from: 'users',
            localField: 'subscriberId',
            foreignField: '_id',
            as: 'subscriberId',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        { $unwind: '$subscriberId' },
        {
          $project: {
            _id: '$subscriberId._id',
            firstName: '$subscriberId.firstName',
            lastName: '$subscriberId.lastName',
            userName: '$subscriberId.userName',
            profileImage: '$subscriberId.profileImage',
            businessOrganizationName: '$subscriberId.businessOrganizationName',
            followers: '$subscriberId.followers',
            following: '$subscriberId.following',
            connections: '$subscriberId.connections',
            accountVerified: '$subscriberId.accountVerified',
            iAmMember: '$subscriberId.iAmMember',
            professions: '$subscriberId.professions',
          },
        },
        { $sort: sortObj },
        { $skip: skipData },
        { $limit: limitData },
      ]);

      const totalResults = await this.subscriberModel.countDocuments({
        userId: new mongoose.Types.ObjectId(id),
        ...searchObj,
      });

      const currentResults = subscribers.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        subscribers,
        CONSTANT.FETCHED_SUCCESSFULLY('Subscribers'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getViewerOrViewByMe(req, id, type) {
    try {
      const { search = '', page, perPage } = req.query;

      const sortParam = { sortBy: 'createdAt', sort: '-1' };
      const paginationParam = { page, perPage };

      const searchFrom = type === 'VIEWER' ? 'viewerId' : 'userId';

      // Create Search, Sort and Pagination
      const { searchObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          [
            `${searchFrom}.userName`,
            `${searchFrom}.firstName`,
            `${searchFrom}.lastName`,
            `${searchFrom}.businessOrganizationName`,
          ],
          null,
          sortParam,
          paginationParam,
        );

      const getUser = await this.userModel.findOne({
        _id: new mongoose.Types.ObjectId(id),
        isFakeAccount: false,
      });

      if (!getUser) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.NOT_FOUND,
        );
      }

      if (type === 'VIEWER') {
        const users = await this.profileViews.aggregate([
          { $match: { userId: new mongoose.Types.ObjectId(id), ...searchObj } },
          {
            $lookup: {
              from: 'users',
              localField: 'viewerId',
              foreignField: '_id',
              as: 'viewerId',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    firstName: 1,
                    lastName: 1,
                    userName: 1,
                    profileImage: 1,
                    businessOrganizationName: 1,
                    followers: 1,
                    following: 1,
                    connections: 1,
                    accountVerified: 1,
                    iAmMember: 1,
                    professions: 1,
                  },
                },
              ],
            },
          },
          { $unwind: '$viewerId' },
          {
            $project: {
              _id: '$viewerId._id',
              firstName: '$viewerId.firstName',
              lastName: '$viewerId.lastName',
              userName: '$viewerId.userName',
              profileImage: '$viewerId.profileImage',
              businessOrganizationName: '$viewerId.businessOrganizationName',
              followers: '$viewerId.followers',
              following: '$viewerId.following',
              connections: '$viewerId.connections',
              accountVerified: '$viewerId.accountVerified',
              iAmMember: '$viewerId.iAmMember',
              professions: '$viewerId.professions',
            },
          },
          { $sort: sortObj },
          { $skip: skipData },
          { $limit: limitData },
        ]);

        const totalResults = await this.profileViews.countDocuments({
          userId: new mongoose.Types.ObjectId(id),
          ...searchObj,
        });

        const currentResults = users.length;
        const totalPages = Math.ceil(totalResults / limitData);
        const currentPage = Number(page) || 1;

        const paginationObj = {
          totalResults,
          currentResults,
          totalPages,
          currentPage,
        };

        return successResponse(
          users,
          CONSTANT.FETCHED_SUCCESSFULLY('Viewers'),
          HttpStatus.OK,
          paginationObj,
        );
      } else if (type === 'VIEWBYME') {
        const users = await this.profileViews.aggregate([
          {
            $match: { viewerId: new mongoose.Types.ObjectId(id), ...searchObj },
          },
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: '_id',
              as: 'userId',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    firstName: 1,
                    lastName: 1,
                    userName: 1,
                    profileImage: 1,
                    businessOrganizationName: 1,
                    followers: 1,
                    following: 1,
                    connections: 1,
                    accountVerified: 1,
                    iAmMember: 1,
                    professions: 1,
                  },
                },
              ],
            },
          },
          { $unwind: '$userId' },
          {
            $project: {
              _id: '$userId._id',
              firstName: '$userId.firstName',
              lastName: '$userId.lastName',
              userName: '$userId.userName',
              profileImage: '$userId.profileImage',
              businessOrganizationName: '$userId.businessOrganizationName',
              followers: '$userId.followers',
              following: '$userId.following',
              connections: '$userId.connections',
              accountVerified: '$userId.accountVerified',
              iAmMember: '$userId.iAmMember',
              professions: '$userId.professions',
            },
          },
          { $sort: sortObj },
          { $skip: skipData },
          { $limit: limitData },
        ]);

        const totalResults = await this.profileViews.countDocuments({
          viewerId: new mongoose.Types.ObjectId(id),
          ...searchObj,
        });

        const currentResults = users.length;
        const totalPages = Math.ceil(totalResults / limitData);
        const currentPage = Number(page) || 1;

        const paginationObj = {
          totalResults,
          currentResults,
          totalPages,
          currentPage,
        };

        return successResponse(
          users,
          CONSTANT.FETCHED_SUCCESSFULLY('View by me'),
          HttpStatus.OK,
          paginationObj,
        );
      } else {
        throw new HttpException(
          { message: 'Invalid type' },
          HttpStatus.BAD_REQUEST,
        );
      }
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async logout(req) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { user: loggedInUser } = req;

      // await this.DeviceToken.deleteOne({
      //   deviceToken: logoutData.deviceToken,
      //   userId: loggedInUser._id,
      // });

      return successResponse(null, CONSTANT.LOGOUT, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async changePassword(req, changePasswordData) {
    try {
      const { user: loggedInUser } = req;

      const userExist: any = await this.userModel.findOne(loggedInUser._id);
      if (!userExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('User'),
          HttpStatus.BAD_REQUEST,
        );
      }

      const isPasswordMatched = await this.passwordService.verifyPassword(
        changePasswordData.oldPassword,
        userExist.password,
      );
      if (!isPasswordMatched) {
        throw new HttpException(
          CONSTANT.INCORRECT_OLD_PASSWORD,
          HttpStatus.BAD_REQUEST,
        );
      }

      if (changePasswordData.oldPassword === changePasswordData.newPassword) {
        throw new HttpException(
          CONSTANT.OLD_NEW_PASSWORD_CANT_SAME,
          HttpStatus.BAD_REQUEST,
        );
      }

      const regex =
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*-{}?._&])[A-Za-z\d@#$!%*-{}?._&]{8,30}$/;
      const isValid = regex.test(changePasswordData.newPassword);
      if (!isValid) {
        throw new HttpException('Password is weak', HttpStatus.FORBIDDEN);
      }

      const hashPassword = await this.passwordService.hashPassword(
        changePasswordData.newPassword,
      );

      await this.userModel.findByIdAndUpdate(
        userExist._id,
        {
          password: hashPassword,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      return successResponse(
        null,
        CONSTANT.CHANGE_SUCCESSFULLY('Password'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async feedback(req, feedbackData) {
    try {
      const { user: loggedInUser } = req;
      const data = { ...feedbackData, userId: loggedInUser._id };
      const feedback: any = await new this.feedbackModel(data).save();

      await this.mailService.SendMail(
        process.env.CONTACT_US_EMAIL,
        feedbackData.title,
        templates.feedbackTemplate({
          user_name: loggedInUser.firstName + ' ' + loggedInUser.lastName,
          category: feedbackData.category,
          subject: feedbackData.title,
          description: feedbackData.description,
        }),
      );

      return successResponse(
        feedback,
        CONSTANT.ADDED_SUCCESSFULLY('Feedback'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addJobPost(req, jobData) {
    try {
      const { user: loggedInUser } = req;

      const data = {
        ...jobData,
        userId: loggedInUser._id,
      };

      const job: any = await new this.jobListModel(data).save();
      return successResponse(
        job,
        CONSTANT.ADDED_SUCCESSFULLY('Job post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async hashtagSearch(req, searchData) {
    try {
      if (!searchData.page) searchData.page = 1;
      if (!searchData.perPage) searchData.perPage = 10;
      const skipData = (searchData.page - 1) * searchData.perPage;

      let searchArr,
        result,
        totalResults,
        currentResults,
        totalPages,
        currentPage,
        responseMessage;

      const regexSearch: any = [];
      let searchObj: any = {};
      if (searchData.search) {
        searchArr = (searchData.search as string).split(' ');
        searchArr.forEach((ele) => {
          regexSearch.push(new RegExp(ele, 'i'));
        });
      }

      if (searchData.type === hashtagSearchData.USER) {
        searchObj.$or = [
          { firstName: { $in: regexSearch } },
          { lastName: { $in: regexSearch } },
          { userName: { $in: regexSearch } },
        ];

        result = await this.userModel
          .find({
            ...searchObj,
          })
          .select('firstName lastName userName profileImage')
          .sort({ createdAt: -1 })
          .skip(skipData)
          .limit(searchData.perPage);

        const usersCount = await this.userModel
          .find({
            ...searchObj,
          })
          .countDocuments();

        totalResults = usersCount;
        currentResults = result.length;
        totalPages = Math.ceil(totalResults / searchData.perPage);
        totalPages = totalPages == 0 ? 1 : totalPages;
        currentPage = searchData.page;
        responseMessage = CONSTANT.FETCHED_SUCCESSFULLY('Users');
      }

      if (searchData.type === hashtagSearchData.POST) {
        searchObj = { caption: { $in: regexSearch } };

        result = await this.postModel
          .find({
            ...searchObj,
          })
          .select('-updatedAt -__v')
          .sort({ createdAt: -1 })
          .skip(skipData)
          .limit(searchData.perPage);

        const postCount: any = await this.postModel
          .find({ ...searchObj })
          .countDocuments();

        totalResults = postCount;
        currentResults = result.length;
        totalPages = Math.ceil(totalResults / searchData.perPage);
        totalPages = totalPages == 0 ? 1 : totalPages;
        currentPage = searchData.page;
        responseMessage = CONSTANT.FETCHED_SUCCESSFULLY('Posts');
      }

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        result,
        responseMessage,
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getUserListing(queryParam: GetUserListing) {
    const { search = '', page, perPage } = queryParam;

    const sortParam = { sortBy: 'userName', sort: '1' };
    const paginationParam = { page, perPage };

    // Create Search, Sort and Pagination
    const { searchObj, sortObj, skipData, limitData } =
      createSearchFilterSortPagination(
        search,
        ['userName'],
        null,
        sortParam,
        paginationParam,
      );

    try {
      const userList = await this.userModel.aggregate([
        { $match: { ...searchObj, isFakeAccount: false } },
        {
          $project: {
            _id: 1,
            firstName: 1,
            lastName: 1,
            userName: 1,
            profileImage: 1,
            businessOrganizationName: 1,
            followers: 1,
            following: 1,
            connections: 1,
            accountVerified: 1,
            iAmMember: 1,
            professions: 1,
          },
        },
        { $sort: sortObj },
        { $skip: skipData },
        { $limit: limitData },
      ]);

      const userCount = await this.userModel.aggregate([
        { $match: { ...searchObj, isFakeAccount: false } },
      ]);

      // Pagination parameters
      const totalResults = userCount.length || 0;
      const currentResults = userList.length || 0;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };
      console.log({ userList });

      return successResponse(
        userList || [],
        CONSTANT.FETCHED_SUCCESSFULLY(`user list`),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getUserSignupData(req: any) {
    try {
      const { user: loggedInUser } = req;

      const data = await this.userModel.aggregate([
        {
          $match: {
            _id: mongoose.Types.ObjectId.createFromHexString(
              loggedInUser._id.toString(),
            ),
            isFakeAccount: false,
          },
        },
        {
          $unwind: {
            path: '$signUpData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'usersignupdatas',
            localField: 'signUpData.itemId',
            foreignField: '_id',
            as: 'signUpDetails',
          },
        },
        {
          $unwind: {
            path: '$signUpDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'signUpData.itemId': {
              _id: '$signUpDetails._id',
              title: '$signUpDetails.title',
              slug: '$signUpDetails.slug',
              itemText: '$signUpDetails.itemText',
            },
          },
        },
        {
          $group: {
            _id: {
              userName: '$userName',
            },
            signUpData: {
              $push: {
                isSelected: '$signUpData.isSelected',
                itemId: '$signUpData.itemId',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            userName: '$_id.userName',
            signUpData: 1,
          },
        },
      ]);

      const slugArr = [];

      data[0].signUpData.filter((item) => {
        if (item.isSelected && item.itemId.title === Title.WHO_ARE_YOU) {
          return slugArr.push(item.itemId.slug);
        }
      });

      let optionsData = [];
      optionsData = await this.userSignupDataModel
        .find({
          parentSlug: { $in: slugArr },
        })
        .sort({ _id: 1 });

      return successResponse(
        optionsData,
        CONSTANT.FETCHED_SUCCESSFULLY('SignupData'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async getUsersNotifications(req: any, queryParam: GetNotificationsDto) {
    try {
      const { user: loggedInUser } = req;
      let { page, perPage } = queryParam;

      page = Number(page);
      perPage = Number(perPage);

      if (!page) page = 1;
      if (!perPage) perPage = 10;

      const skipData = (page - 1) * perPage;
      const limitData = perPage;

      const notificationCount = await this.notificationModel.countDocuments({
        receiver: loggedInUser._id,
      });

      const unreadNotificationCount =
        await this.notificationModel.countDocuments({
          receiver: loggedInUser._id,
          isRead: false,
        });

      const notifications = await this.notificationModel.aggregate([
        {
          $addFields: {
            'postDetails.collaborators': '$postDetails.collaboratorDetails',
            'postDetails.taggedPeople': '$postDetails.taggedPeopleDetails',
          },
        },
        {
          $project: {
            'postDetails.collaboratorDetails': 0,
            'postDetails.taggedPeopleDetails': 0,
          },
        },
        {
          $match: {
            receiver: new mongoose.Types.ObjectId(loggedInUser._id),
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'sender',
            foreignField: '_id',
            as: 'senderData',
            pipeline: [
              // {
              //   $match: {
              //     isFakeAccount: false,
              //   },
              // },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'receiver',
            foreignField: '_id',
            as: 'receiverData',
            pipeline: [
              // {
              //   $match: {
              //     isFakeAccount: false,
              //   },
              // },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'groups',
            localField: 'group',
            foreignField: '_id',
            as: 'groupDetails',
          },
        },
        {
          $lookup: {
            from: 'posts',
            localField: 'postId',
            foreignField: '_id',
            as: 'postDetails',
            pipeline: [
              {
                $lookup: {
                  from: 'users',
                  localField: 'fundraisers',
                  foreignField: '_id',
                  as: 'fundraisers',
                  pipeline: [
                    {
                      $match: {
                        isFakeAccount: false,
                      },
                    },
                    {
                      $project: {
                        _id: 1,
                        firstName: 1,
                        lastName: 1,
                        businessOrganizationName: 1,
                        userName: 1,
                        profileImage: 1,
                        followers: 1,
                        following: 1,
                        connections: 1,
                        accountVerified: 1,
                        iAmMember: 1,
                        professions: 1,
                      },
                    },
                  ],
                },
              },
              {
                $project: {
                  group: 0,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'stories',
            localField: 'storyId',
            foreignField: '_id',
            as: 'storyDetails',
          },
        },
        {
          $unwind: {
            path: '$senderData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: '$receiverData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: '$groupDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: '$postDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: '$storyDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'postDetails.collaborators': {
              $filter: {
                input: '$postDetails.collaborators',
                as: 'collab',
                cond: { $eq: ['$$collab.status', StatusEnum.ACCEPT] },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'postDetails.collaborators.id',
            foreignField: '_id',
            as: 'postDetails.collaborators',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'postDetails.taggedPeople',
            foreignField: '_id',
            as: 'postDetails.taggedPeople',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            'postDetails.reactions': {
              $ifNull: ['$postDetails.reactions', []],
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            let: {
              reactionIds: '$postDetails.reactions.userId',
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $in: ['$_id', '$$reactionIds'],
                  },
                },
              },
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
            as: 'reactionsUserDetail',
          },
        },
        {
          $addFields: {
            'postDetails.reactions': {
              $map: {
                input: '$postDetails.reactions',
                as: 'reaction',
                in: {
                  $mergeObjects: [
                    '$$reaction',
                    {
                      userId: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$reactionsUserDetail',
                              as: 'user',
                              cond: {
                                $eq: ['$$user._id', '$$reaction.userId'], // Match userId in reaction with user _id
                              },
                            },
                          },
                          0, // Get the first matched user
                        ],
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'postDetails.userId',
            foreignField: '_id',
            as: 'postDetails.userId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $project: {
            reactionsUserDetail: 0,
          },
        },
        {
          $unwind: {
            path: '$postDetails.userId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'postDetails.repostBy',
            foreignField: '_id',
            as: 'postDetails.repostBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$postDetails.repostBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'peoples',
            localField: 'memberId',
            foreignField: '_id',
            as: 'memberDetails',
          },
        },
        {
          $unwind: {
            path: '$memberDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'postDetails.collaborators': {
              $cond: {
                if: {
                  $eq: [
                    {
                      $size: '$postDetails.collaborators',
                    },
                    0,
                  ],
                },
                then: '$$REMOVE',
                else: '$postDetails.collaborators',
              },
            },
            'postDetails.taggedPeople': {
              $cond: {
                if: {
                  $eq: [
                    {
                      $size: '$postDetails.taggedPeople',
                    },
                    0,
                  ],
                },
                then: '$$REMOVE',
                else: '$postDetails.taggedPeople',
              },
            },
            'postDetails.reactions': {
              $cond: {
                if: {
                  $eq: [
                    {
                      $size: '$postDetails.reactions',
                    },
                    0,
                  ],
                },
                then: '$$REMOVE',
                else: '$postDetails.reactions',
              },
            },
          },
        },
        {
          $addFields: {
            postDetails: {
              $cond: {
                if: {
                  $eq: [
                    {
                      $size: {
                        $objectToArray: '$postDetails',
                      },
                    },
                    0,
                  ],
                },
                then: null,
                else: '$postDetails',
              },
            },
            storyDetails: '$storyDetails',
          },
        },
        {
          $addFields: {
            notificationData: {
              sender: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ['$senderData', null] }, // Case 1: senderData is null
                      {
                        $eq: [{ $objectToArray: '$senderData' }, []],
                      }, // Case 2: senderData is an empty object
                      { $not: ['$senderData'] }, // Case 3: senderData field is missing
                    ],
                  },
                  then: null, // Set to null if senderData is null, empty, or missing
                  else: {
                    userName: '$senderData.userName',
                    _id: '$senderData._id',
                    firstName: '$senderData.firstName',
                    lastName: '$senderData.lastName',
                    email: '$senderData.email',
                    profileImage: '$senderData.profileImage',
                    businessOrganizationName:
                      '$senderData.businessOrganizationName',
                  },
                },
              },
              receiver: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ['$receiverData', null] },
                      { $eq: [{ $objectToArray: '$receiverData' }, []] },
                      { $not: ['$receiverData'] },
                    ],
                  },
                  then: null,
                  else: {
                    userName: '$receiverData.userName',
                    _id: '$receiverData._id',
                    firstName: '$receiverData.firstName',
                    lastName: '$receiverData.lastName',
                    email: '$receiverData.email',
                    profileImage: '$receiverData.profileImage',
                    businessOrganizationName:
                      '$receiverData.businessOrganizationName',
                  },
                },
              },
              notificationType: '$notificationType',
              redirectionType: '$redirectionType',
              memberDetails: {
                $cond: {
                  if: {
                    $ifNull: ['$memberDetails', false],
                  },
                  then: '$memberDetails',
                  else: null,
                },
              },
              groupDetails: {
                $cond: {
                  if: {
                    $ifNull: ['$group', false],
                  },
                  then: '$groupDetails',
                  else: null,
                },
              },
              postDetails: {
                $cond: {
                  if: {
                    $ifNull: ['$postId', false],
                  },
                  then: '$postDetails',
                  else: null,
                },
              },
              storyDetails: {
                $cond: {
                  if: {
                    $ifNull: ['$storyId', false],
                  },
                  then: '$storyDetails',
                  else: null,
                },
              },
              action: '$action',
              connectionId: '$connectionId',
              clientId: '$clientId',
              memberId: '$memberId',
              isGroupMember: '$isGroupMember',
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'notificationData.groupDetails.createdBy',
            foreignField: '_id',
            as: 'notificationData.groupDetails.createdBy',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$notificationData.groupDetails.createdBy',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'notificationData.groupDetails': {
              $cond: {
                if: {
                  $eq: [{}, '$notificationData.groupDetails'],
                },
                // check if groupDetails is an empty object
                then: null,
                // set it to null if it's an empty object
                else: '$notificationData.groupDetails', // otherwise keep the existing value
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            status: 1,
            notificationMessage: 1,
            isRead: 1,
            notificationData: 1,
            // action:1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
      ]);
      // Pagination parameters
      const totalResults = notificationCount;
      const currentResults = notifications?.length;
      const totalPages = Math.ceil(totalResults / perPage);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
        unreadNotificationCount,
      };

      return successResponse(
        notifications,
        CONSTANT.SUCCESSFULL('Get notifications'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async markNotificationAsRead(req: any) {
    try {
      const { user: loggedInUser } = req;

      await this.notificationModel.updateMany(
        {
          receiver: loggedInUser._id,
        },
        {
          isRead: true,
        },
        {
          new: true,
          runValidators: true,
        },
      );

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Notification'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async getClients(req: any, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['name'];

      let matchObj = {
        userId: new mongoose.Types.ObjectId(userId),
        status: StatusEnum.ACCEPT,
      };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const clients = await this.clientInfoModel.aggregate([
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'clientId',
            foreignField: '_id',
            as: 'clientId',
            pipeline: [
              {
                $match: {
                  isFakeAccount: false,
                },
              },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$clientId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 1,
            clientId: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
        {
          $sort: sortObj,
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
      ]);

      const totalResults = await this.clientInfoModel.countDocuments(matchObj);
      const currentResults = clients.length;
      const totalPages = Math.ceil(totalResults / perPage);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        clients,
        CONSTANT.FETCHED_SUCCESSFULLY('Clients'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async getPeoples(req: any, userId: string, showAll = false) {
    try {
      const {
        search,
        sortBy,
        sort,
        page,
        perPage,
        isAlumni = false,
        isStudent = false,
      } = req.query;

      const searchFields = ['name'];

      if (req.query?.showAll === 'true' || req.query?.showAll === true) {
        showAll = true;
      }

      let matchObj = {
        $or: [
          {
            parentUserId: new mongoose.Types.ObjectId(userId),
          },
          {
            childUserId: new mongoose.Types.ObjectId(userId),
          },
        ],
        ...(!showAll && { status: StatusEnum.ACCEPT }),
        ...(isAlumni === 'true' ? { isAlumni: true } : { isAlumni: false }),
        ...(isStudent === 'true' ? { isStudent: true } : { isStudent: false }),
      };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const clients = await this.peopleModel.aggregate([
        {
          $match: matchObj,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'childUserId',
            foreignField: '_id',
            as: 'childUserId',
            pipeline: [
              // {
              //   $match: {
              //     isFakeAccount: false,
              //   },
              // },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },

        {
          $unwind: {
            path: '$childUserId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'parentUserId',
            foreignField: '_id',
            as: 'parentUserId',
            pipeline: [
              // {
              //   $match: {
              //     isFakeAccount: false,
              //   },
              // },
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  userName: 1,
                  profileImage: 1,
                  businessOrganizationName: 1,
                  followers: 1,
                  following: 1,
                  connections: 1,
                  accountVerified: 1,
                  iAmMember: 1,
                  professions: 1,
                  hirerEmployerVerifiedStatus: 1,
                  isMembershipVerified: 1,
                  isFakeAccount: 1,
                },
              },
            ],
          },
        },

        {
          $unwind: {
            path: '$parentUserId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            member: {
              $cond: {
                if: {
                  $eq: [
                    '$parentUserId._id',
                    new mongoose.Types.ObjectId(userId),
                  ],
                },
                then: '$childUserId',
                else: '$parentUserId',
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            status: 1,
            member: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
        {
          $sort: sortObj,
        },
        {
          $skip: skipData,
        },
        {
          $limit: limitData,
        },
      ]);

      const totalResults = await this.peopleModel.countDocuments(matchObj);
      const currentResults = clients.length;
      const totalPages = Math.ceil(totalResults / perPage);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        clients,
        CONSTANT.FETCHED_SUCCESSFULLY('Peoples'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async showClientToggleStatus(req: any) {
    try {
      const { user: loggedInUser } = req;

      await this.userRepository.findByIdAndUpdate(loggedInUser._id, {
        showClients: !loggedInUser.showClients,
      });

      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Client Show Status'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  async createFakeAccounts() {
    try {
      const accountsData = [
        ...unionList,
        ...affiliateBusinessList,
        ...affiliateOrganizationList,
        ...schoolTrainingList,
      ];

      const iAmMember =
        iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY;

      const businessNames = accountsData.map((a) => a.businessOrganizationName);
      const accountTypes = [...new Set(accountsData.map((a) => a.accountType))];

      // Preload all relevant account types
      const accountTypeDocs = await this.userSignupDataModel
        .find({ itemText: { $in: accountTypes } })
        .lean();

      const accountTypeMap = Object.fromEntries(
        accountTypeDocs.map((doc) => [doc.itemText, doc]),
      );

      // Pre-check existing users
      const existingUsers = await this.userModel
        .find({
          businessOrganizationName: { $in: businessNames },
          iAmMember,
        })
        .lean();

      const existingNamesSet = new Set(
        existingUsers.map((user) => user.businessOrganizationName),
      );

      const createOps = accountsData.map(async (accountData) => {
        const { businessOrganizationName, accountType } = accountData;

        if (existingNamesSet.has(businessOrganizationName)) {
          return {
            businessOrganizationName,
            status: 'skipped',
            reason: 'User already exists',
          };
        }

        const accountDoc = accountTypeMap[accountType];
        if (!accountDoc) {
          return {
            businessOrganizationName,
            status: 'failed',
            reason: 'Invalid account type',
          };
        }

        const signupData = [
          {
            isSelected: true,
            itemId: accountDoc._id,
          },
        ];

        const data = {
          businessOrganizationName,
          iAmMember,
          signUpData: signupData,
          isFakeAccount: true,
        };

        try {
          const user = await new this.userModel(data).save({
            validateBeforeSave: false,
          });

          return {
            businessOrganizationName,
            status: 'created',
            userId: user._id,
          };
        } catch (err) {
          return {
            businessOrganizationName,
            status: 'failed',
            reason: err?.message || 'Unknown error',
          };
        }
      });

      const results = await Promise.all(createOps);

      return successResponse(
        results,
        CONSTANT.ADDED_SUCCESSFULLY('Fake Accounts'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Internal Server Error',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async unionAffiliateOrganizationBusinessSchoolsTrainingFacilityList(
    req,
    bodyData: GetUserSelectionListingDto,
  ) {
    try {
      // eslint-disable-next-line prefer-const
      let { page, perPage, search } = req.query;

      const loggedInUser = req.user;

      const sortParam = { sortBy: 'businessOrganizationName', sort: 1 };
      const paginationParam = { page, perPage };

      const {
        listOfType,
        isFakeAccount,
        isFunding,
        include = false,
      } = bodyData;

      if (!page) {
        page = 1;
      }

      if (!perPage) {
        perPage = 10;
      }

      const whoAreYouIds = (
        await this.userSignupDataModel
          .find({
            itemText: {
              $in: listOfType,
            },
          })
          .distinct('_id')
      ).map((u) => u._id);

      // Create Search, Sort and Pagination
      const { searchObj, sortObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          ['businessOrganizationName', 'userName'],
          null,
          sortParam,
          paginationParam,
        );

      const pipeline: PipelineStage[] = [
        {
          $match: {
            ...searchObj,
            iAmMember:
              iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY,
            ...(isFakeAccount === true
              ? { isFakeAccount: true }
              : isFakeAccount === false
              ? { isFakeAccount: false }
              : {}),
            ...(isFunding === true
              ? { isFunding: true }
              : isFunding === false
              ? { isFunding: false }
              : {}),
            signUpData: {
              $elemMatch: {
                itemId: { $in: whoAreYouIds },
                isSelected: true,
              },
            },
          },
        },
      ];

      if (loggedInUser && !isFunding && include === false) {
        pipeline.push(
          ...[
            {
              $lookup: {
                from: 'peoples',
                let: { id: '$_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          {
                            $or: [
                              {
                                $and: [
                                  { $eq: ['$parentUserId', '$$id'] },
                                  { $eq: ['$childUserId', loggedInUser._id] },
                                  {
                                    $not: {
                                      $or: [
                                        { $eq: ['$isAlumni', true] },
                                        { $eq: ['$isStudent', true] },
                                      ],
                                    },
                                  },
                                ],
                              },
                              {
                                $and: [
                                  { $eq: ['$childUserId', loggedInUser._id] },
                                  { $eq: ['$parentUserId', '$$id'] },
                                  {
                                    $not: {
                                      $or: [
                                        { $eq: ['$isAlumni', true] },
                                        { $eq: ['$isStudent', true] },
                                      ],
                                    },
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  },

                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      userName: 1,
                      profileImage: 1,
                      businessOrganizationName: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,
                    },
                  },
                ],
                as: 'peoples',
              },
            },
            {
              $unwind: {
                path: '$peoples',
                preserveNullAndEmptyArrays: true,
              },
            },
          ],
        );
      }

      pipeline.push(
        ...[
          {
            $addFields: {
              isAlreadyMember: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: [{ $ifNull: ['$peoples', []] }, []] },
                      { $not: ['$peoples'] },
                    ],
                  },
                  then: false,
                  else: true,
                },
              },
            },
          },
          {
            $match: {
              isAlreadyMember: false,
            },
          },
          {
            $project: {
              _id: 1,
              firstName: 1,
              lastName: 1,
              userName: 1,
              profileImage: 1,
              businessOrganizationName: 1,
              followers: 1,
              following: 1,
              connections: 1,
              accountVerified: 1,
              iAmMember: 1,
              professions: 1,
              isFakeAccount: 1,
              peoples: 1,
            },
          },
          { $sort: sortObj },
          { $skip: skipData },
          { $limit: limitData },
        ],
      );

      const result = await this.userModel.aggregate(pipeline);

      const userCountQuery = {
        ...searchObj,
        iAmMember:
          iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY,
        ...(isFakeAccount === true
          ? { isFakeAccount: true }
          : isFakeAccount === false
          ? { isFakeAccount: false }
          : {}),

        ...(isFunding === true
          ? { isFunding: true }
          : isFunding === false
          ? { isFunding: false }
          : {}),
        signUpData: {
          $elemMatch: {
            itemId: { $in: whoAreYouIds },
            isSelected: true,
          },
        },
      };

      const usersCount = await this.userModel.countDocuments(userCountQuery);

      const totalResults = usersCount;
      const currentResults = result?.length;
      const totalPages = Math.ceil(totalResults / limitData);
      const currentPage = Number(page) || 1;

      const paginationObj = {
        totalResults,
        currentResults,
        totalPages,
        currentPage,
      };

      return successResponse(
        result || [],
        CONSTANT.FETCHED_SUCCESSFULLY('Users'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      console.log(error);
      throw new HttpException(error, error?.status || 500);
    }
  }

  public async subscribeUser(req, bodyData: subscribeDto) {
    try {
      const { user: subscriber } = req;

      if (subscriber._id === bodyData.userId) {
        throw new HttpException(
          { message: 'Same user Id' },
          HttpStatus.FORBIDDEN,
        );
      }

      const subscriberExist = await this.subscriberModel.findOne({
        subscriberId: subscriber._id,
        userId: new mongoose.Types.ObjectId(bodyData.userId),
      });

      if (!subscriberExist) {
        const data = {
          subscriberId: subscriber._id,
          userId: new mongoose.Types.ObjectId(bodyData.userId),
        };
        const subscriberUser = await new this.subscriberModel(data).save();

        const params = {
          title: NOTIFICATION_MESSAGES.HAS_SUBSCRIBED_YOU,
          notificationMessage: '',
          notificationType: NotificationsType.SUBSCRIBER,
          redirectionType: RedirectionType.USER,
          receiver:
            subscriber._id === subscriberUser.subscriberId
              ? subscriberUser.userId
              : subscriberUser.subscriberId,
          sender:
            subscriber._id === subscriberUser.subscriberId
              ? subscriberUser.subscriberId
              : subscriberUser.userId,
        };

        this.notificationService.sendToTopic(null, params);

        return successResponse(null, CONSTANT.SUBSCRIBE_USER, HttpStatus.OK);
      }

      const deletedDocument = await this.subscriberModel.deleteOne({
        _id: subscriberExist.id,
      });

      if (!deletedDocument) {
        console.log(`Document with id ${subscriberExist.id} was not found`);
      } else {
        console.log(`Deleted document: ${deletedDocument}`);
      }

      return successResponse(null, CONSTANT.UNSUBSCRIBE_USER, HttpStatus.OK);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async sentHirerVerificationEmail(
    req,
    body: hirerVerificationDTO,
    file,
  ) {
    try {
      const { user: loggedInUser } = req;

      const attachments = file ?? [];

      const user = await this.userModel.findById(loggedInUser._id);

      if (!user) {
        throw new HttpException(
          { message: 'User not found' },
          HttpStatus.NOT_FOUND,
        );
      }

      if (user.hirerEmployerVerifiedStatus === StatusEnum.ACCEPT) {
        throw new HttpException(
          { message: 'Employer / Hirer Verification already done.' },
          HttpStatus.BAD_REQUEST,
        );
      }

      const verificationId = new mongoose.Types.ObjectId().toString();

      const payload = { userId: user._id, verificationId };
      const token = await this.jwtService.generateJwtToken(payload);

      const actionUrl = `${process.env.BACKEND_URL}/api/v1/user/hirer-verification/action/${token}`;

      user.hirerEmployerVerifiedStatus = StatusEnum.PENDING;

      await user.save();

      await this.mailService.SendMail(
        process.env.CONTACT_US_EMAIL || '<EMAIL>',
        'New Hirer Verification Request - Pepli',
        templates.hirerVerificationTemplate({
          message: body.message,
          email: body.email,
          position: body.position,
          profession: body.profession,
          fullName: body.fullName,
          website: body.website,
          actionUrl,
          verificationId,
        }),
        attachments,
      );

      //send pending notification to user
      const params = {
        title:
          NOTIFICATION_MESSAGES.EMPLOYER_HIRER_VERIFICATION_SUBMITION_NOTIFICATION,
        notificationMessage: '',
        notificationType: NotificationsType.HIRER_VERIFICATION,
        redirectionType: RedirectionType.USER,
        receiver: user._id,
        sender: user._id,
      };

      this.notificationService.sendToTopic(null, params);

      return successResponse(
        null,
        CONSTANT.SUCCESSFULL('Verification email sent to admin for review'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(error, error?.status || 500);
    }
  }

  public async handleHirerVerificationAction(req: any) {
    try {
      const { token } = req.params;
      const { action, rejectReason } = req.body;

      const payload = await this.jwtService.verifyToken(token);
      const { userId } = payload;

      if (![StatusEnum.ACCEPT, StatusEnum.REJECT].includes(action)) {
        throw new HttpException(
          { message: 'Invalid action' },
          HttpStatus.BAD_REQUEST,
        );
      }

      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new HttpException(
          { message: 'User not found' },
          HttpStatus.NOT_FOUND,
        );
      }

      let message: string;

      if (action === StatusEnum.ACCEPT) {
        user.hirerEmployerVerifiedStatus = StatusEnum.ACCEPT;
        const hirerRecord = await this.userSignupDataModel.findOne({
          slug: 'employer_1',
          title: 'more_about_you',
        });

        user.signUpData = user.signUpData.map((item) => {
          if (item.itemId?.toString() === hirerRecord._id.toString()) {
            return {
              ...item,
              isSelected: true,
            };
          }
          return item;
        });
        await user.save();
        message = 'Employer / Hirer verification approved';
      } else {
        user.hirerEmployerVerifiedStatus = StatusEnum.REJECT;
        await user.save();
        message = 'Employer / Hirer verification rejected';
      }

      const userMessage =
        action === 'accept'
          ? NOTIFICATION_MESSAGES.EMPLOYER_HIRER_VERIFICATION_APPROVED
          : NOTIFICATION_MESSAGES.EMPLOYER_HIRER_VERIFICATION_REJECTED(
              rejectReason,
            );

      // sent notification to user
      const params = {
        title: userMessage,
        notificationMessage: '',
        notificationType: NotificationsType.HIRER_VERIFICATION,
        redirectionType: RedirectionType.USER,
        receiver: user._id,
        sender: user._id,
      };

      this.notificationService.sendToTopic(null, params);

      return message;
    } catch (error) {
      return 'Employer / Hirer Verification failed';
    }
  }
}
