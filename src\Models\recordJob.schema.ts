import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type recordJobDocument = RecordJob & Document;

@Schema({ timestamps: true })
export class RecordJob {
  @Prop({ type: [String], default: null })
  roleType: string[];

  @Prop({ type: [String], default: null })
  projectType: string[];

  @Prop({ type: [String], default: null })
  commercialBrandedContent: string[];

  @Prop({ type: [String], default: null })
  concert: string[];

  @Prop({ type: [String], default: null })
  fashion: string[];

  @Prop({ type: [String], default: null })
  literature: string[];

  @Prop({ type: [String], default: null })
  movieFilm: string[];

  @Prop({ type: [String], default: null })
  museum: string[];

  @Prop({ type: [String], default: null })
  music: string[];

  @Prop({ type: [String], default: null })
  opera: string[];

  @Prop({ type: [String], default: null })
  hipHop: string[];

  @Prop({ type: [String], default: null })
  rap: string[];

  @Prop({ type: [String], default: null })
  countryMusic: string[];

  @Prop({ type: [String], default: null })
  otherDigitalMedia: string[];

  @Prop({ type: [String], default: null })
  religiousSpiritualMusic: string[];

  @Prop({ type: [String], default: null })
  standUpComedy: string[];

  @Prop({ type: [String], default: null })
  tVShowSeries: string[];

  @Prop({ type: [String], default: null })
  theater: string[];

  @Prop({ type: [String], default: null })
  userGeneratedContentUGC: string[];

  @Prop({ type: [String], default: null })
  typeOfOrganizationYouAreWorkingWithForThisProject: string[];

  @Prop({ type: [String], default: null })
  crafts: string[];

  @Prop({ type: [String], default: null })
  additional: string[];
}
export const RecordJobSchema = SchemaFactory.createForClass(RecordJob);
