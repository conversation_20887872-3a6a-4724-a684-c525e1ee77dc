import { IsMongoId } from 'class-validator';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';
import CONSTANT from '../../../common/constant/common.constant';

export class InviteUserDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('userId', 24, false)
  readonly userId: string;

  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;
}
