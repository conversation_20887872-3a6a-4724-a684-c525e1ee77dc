# Profile Visibility - Simple Documentation

## Overview

The profile visibility system ensures that for each subcategory, **only ONE option can be `true`** and all others must be `false`.

## Simple Rules

1. **For each subcategory** (`profileViewingOptions`, `profileDiscoveryOptions`, `activeStatusOptions`)
2. **Find the FIRST option** that has `isEnabled: true`
3. **Make that option `true`**, make **ALL other options `false`**
4. **If no options are `true`**, make the **FIRST option `true`**
5. **That's it!** Simple and straightforward.

## Subcategories

- **`profileViewingOptions`**: Controls who can view profile information
- **`profileDiscoveryOptions`**: Controls profile discovery settings  
- **`activeStatusOptions`**: Controls active status visibility

## API Endpoint

```
POST /api/v1/profile-visibility/save
```

## Examples

### Example 1: Frontend sends one option as true
**Input:**
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": true },
    { "Name": "Private profile character", "isEnabled": false }
  ]
}
```
**Result:** Same as input (already correct)

### Example 2: Frontend sends multiple options as true
**Input:**
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": true },
    { "Name": "Private profile character", "isEnabled": true }
  ]
}
```
**Result:** First option stays true, second becomes false
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": true },
    { "Name": "Private profile character", "isEnabled": false }
  ]
}
```

### Example 3: Frontend sends no options as true
**Input:**
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": false },
    { "Name": "Private profile character", "isEnabled": false }
  ]
}
```
**Result:** First option becomes true
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": true },
    { "Name": "Private profile character", "isEnabled": false }
  ]
}
```

### Example 4: Partial update - only one option sent
**Input:**
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": false }
  ]
}
```
**Result:** This option becomes false, other option becomes true
```json
{
  "profileViewingOptions": [
    { "Name": "Your name headline", "isEnabled": false },
    { "Name": "Private profile character", "isEnabled": true }
  ]
}
```

## Implementation

The logic is very simple:

```typescript
// For each subcategory
fieldsToValidate.forEach(fieldName => {
  const options = correctedData[fieldName];
  
  if (options && Array.isArray(options)) {
    // Find which option should be true (first one that's true, or first one if none are true)
    let trueOptionIndex = 0;
    for (let i = 0; i < options.length; i++) {
      if (options[i].isEnabled === true) {
        trueOptionIndex = i;
        break;
      }
    }
    
    // Set all options to false, then set the chosen one to true
    options.forEach((option, index) => {
      option.isEnabled = index === trueOptionIndex;
    });
  }
});
```

## Response Format

All responses are **200 OK** with corrected data:

```json
{
  "success": true,
  "message": "Profile Visibility Settings updated successfully",
  "data": {
    "profileVisibility": {
      // corrected data here
    }
  }
}
```

## Key Points

- **No validation errors** - system always corrects the data
- **Always returns 200 OK** - never throws errors
- **Simple logic** - find first true option, make it true, make others false
- **Works with partial updates** - merges with existing data first
- **Supports both formats** - legacy field-based and new settingName-based

## Testing

Run the simple test:
```bash
node test-simple-profile-visibility.js
```

This will show you exactly how the logic works with various input scenarios. 