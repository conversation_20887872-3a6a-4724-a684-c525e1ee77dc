import { Is<PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import {
  JobApplicationStatus,
  JobPostWorkplaceType,
  JobPostWorkType,
} from 'src/common/constant/enum';

export class JobPostDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  title: string;

  @IsString()
  company: string;

  @IsString()
  description: string;

  @IsEnum(JobPostWorkplaceType)
  workplaceType: string;

  @IsString()
  location: string;

  @IsEnum(JobPostWorkType)
  workType: string;
}

export class JobApplicationDto {
  @IsString()
  jobId: string;

  @IsString()
  description: string;

  resume: any;
}

export class JobApplicationStatusDto {
  @IsEnum(JobApplicationStatus)
  status: string;

  @IsString()
  applicationId: string;
}
