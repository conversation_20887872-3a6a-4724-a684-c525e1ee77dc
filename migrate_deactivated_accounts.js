/**
 * Migration script to add deactivatedOn field to existing deactivated accounts
 * 
 * This script handles users who were deactivated before the enhanced deactivation system
 * was implemented. It sets a default deactivatedOn timestamp for existing deactivated accounts.
 * 
 * Run this script ONCE after deploying the enhanced deactivation system.
 */

const { MongoClient } = require('mongodb');

// Configuration - Update these values for your environment
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database-name';
const DATABASE_NAME = 'your-database-name'; // Update this
const COLLECTION_NAME = 'users';

// Default timestamp for existing deactivated accounts
// This sets it to current time minus 15 days, giving users 15 more days before cleanup
const DEFAULT_DEACTIVATED_ON = new Date(Date.now() - (15 * 24 * 60 * 60 * 1000));

async function migrateDeactivatedAccounts() {
  let client;
  
  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const collection = db.collection(COLLECTION_NAME);
    
    console.log('Connected successfully. Starting migration...');
    
    // Find accounts that are deactivated but don't have deactivatedOn field
    const query = {
      isDeactivated: true,
      $or: [
        { deactivatedOn: { $exists: false } },
        { deactivatedOn: null }
      ]
    };
    
    const accountsToUpdate = await collection.find(query).toArray();
    
    if (accountsToUpdate.length === 0) {
      console.log('No accounts need migration. All deactivated accounts already have deactivatedOn field.');
      return;
    }
    
    console.log(`Found ${accountsToUpdate.length} deactivated accounts without deactivatedOn field.`);
    console.log('Sample accounts:');
    accountsToUpdate.slice(0, 5).forEach(account => {
      console.log(`- ${account.email} (ID: ${account._id})`);
    });
    
    // Update accounts with default deactivatedOn timestamp
    const updateResult = await collection.updateMany(
      query,
      {
        $set: {
          deactivatedOn: DEFAULT_DEACTIVATED_ON
        }
      }
    );
    
    console.log(`\nMigration completed successfully!`);
    console.log(`Updated ${updateResult.modifiedCount} accounts with deactivatedOn field.`);
    console.log(`Default timestamp used: ${DEFAULT_DEACTIVATED_ON.toISOString()}`);
    console.log(`These accounts will be cleaned up after: ${new Date(DEFAULT_DEACTIVATED_ON.getTime() + (30 * 24 * 60 * 60 * 1000)).toISOString()}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
      console.log('Database connection closed.');
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  console.log('=== Deactivated Accounts Migration ===');
  console.log('This script will add deactivatedOn field to existing deactivated accounts.');
  console.log(`Default timestamp: ${DEFAULT_DEACTIVATED_ON.toISOString()}`);
  console.log('Starting in 3 seconds... (Press Ctrl+C to cancel)');
  
  setTimeout(() => {
    migrateDeactivatedAccounts();
  }, 3000);
}

module.exports = { migrateDeactivatedAccounts };
