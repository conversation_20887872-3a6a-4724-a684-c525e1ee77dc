// src/services/jobPost.service.ts
import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import CONSTANT from '../../../common/constant/common.constant';
import {
  JobPostDto,
  JobApplicationDto,
  JobApplicationStatusDto,
} from '../dtos/jobPost.dto';
import { JobApplicationStatus } from 'src/common/constant/enum';
import { JobPostRepository } from 'src/Repositories/jobPost.repository';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import mongoose from 'mongoose';

@Injectable()
export class JobPostService {
  constructor(private readonly jobPostRepository: JobPostRepository) {}

  // Add or Update Job Post
  public async addEditJobPost(req, jobPostData: JobPostDto) {
    try {
      const { user: loggedInUser } = req;
      const { _id, ...restData } = jobPostData;

      if (_id) {
        const isExist = await this.jobPostRepository.findById(_id);
        if (!isExist)
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Job Post'),
            HttpStatus.BAD_REQUEST,
          );

        const updatedJob = await this.jobPostRepository.findByIdAndUpdate(_id, {
          ...restData,
          userId: loggedInUser._id,
        });

        return successResponse(
          updatedJob,
          CONSTANT.UPDATED_SUCCESSFULLY('Job Post'),
          HttpStatus.OK,
        );
      }

      const newJob = await this.jobPostRepository.create({
        ...restData,
        userId: loggedInUser._id,
      });
      return successResponse(
        newJob,
        CONSTANT.ADDED_SUCCESSFULLY('Job Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Remove Job Post
  public async removeJobPost(req, id: string) {
    try {
      const { user: loggedInUser } = req;

      const isExist = await this.jobPostRepository.findById(id);
      if (
        !isExist ||
        isExist.userId.toString() !== loggedInUser._id.toString()
      ) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Job Post'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.jobPostRepository.deleteById(id);
      return successResponse(
        {},
        CONSTANT.DELETED_SUCCESSFULLY('Job Post'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Get All Job Posts
  public async getAllJobPost(req, userId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['name'];

      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const jobPosts = await this.jobPostRepository.getAllJobPostsOfUser(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      const totalResults = await this.jobPostRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults,
        currentResults: jobPosts?.length,
        totalPages: Math.ceil(totalResults / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        jobPosts,
        CONSTANT.FETCHED_SUCCESSFULLY('Job Posts'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Apply for Job
  public async applyForJob(req, payload: JobApplicationDto) {
    try {
      const { user: loggedInUser } = req;
      const { jobId, description, resume } = payload;

      const isExist = await this.jobPostRepository.findById(jobId);
      if (!isExist)
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Job Post'),
          HttpStatus.BAD_REQUEST,
        );

      const isApplied = await this.jobPostRepository.getJobApplications(
        {
          jobId: new mongoose.Types.ObjectId(jobId),
        },
        {},
        0,
        1,
      );

      if (isApplied.length)
        throw new HttpException(
          CONSTANT.ALREADY_APPLIED('Job'),
          HttpStatus.BAD_REQUEST,
        );

      const application = await this.jobPostRepository.applyForJob({
        userId: loggedInUser._id,
        jobId,
        description,
        resume,
        appliedDate: new Date(),
        status: JobApplicationStatus.APPLIED,
      });

      return successResponse(
        application,
        CONSTANT.APPLIED_SUCCESSFULLY('Job'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Update Job Application Status
  public async updateJobApplicationStatus(
    req,
    payload: JobApplicationStatusDto,
  ) {
    try {
      const { user: loggedInUser } = req;
      const { applicationId, status } = payload;

      const isExist = await this.jobPostRepository.getJobApplicationById(
        applicationId,
      );

      if (!isExist)
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Job Application'),
          HttpStatus.BAD_REQUEST,
        );

      const job = await this.jobPostRepository.findById(isExist.jobId);

      if (!job || job.userId.toString() !== loggedInUser._id.toString()) {
        throw new HttpException(CONSTANT.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
      }

      await this.jobPostRepository.updateJobApplicationStatus(
        applicationId,
        status,
      );
      return successResponse(
        null,
        CONSTANT.UPDATED_SUCCESSFULLY('Job Application Status'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Get all job applications
  public async getAllJobApplications(req, jobId: string) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;

      const searchFields = ['name'];

      let matchObj = { jobId: new mongoose.Types.ObjectId(jobId) };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const applications = await this.jobPostRepository.getJobApplications(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      const totalResults = await this.jobPostRepository.countDocuments(
        matchObj,
      );

      const paginationObj = {
        totalResults,
        currentResults: applications?.length,
        totalPages: Math.ceil(totalResults / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        applications,
        CONSTANT.FETCHED_SUCCESSFULLY('Job Posts'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException(
        { message: error.message || 'Internal Server Error' },
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
