const mongoose = require('mongoose');

// Test script to verify username case sensitivity functionality
async function testUsernameCaseSensitivity() {
  try {
    // Connect to MongoDB (adjust connection string as needed)
    await mongoose.connect('mongodb://localhost:27017/your_database', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Test case 1: Check if "TestUser" and "testuser" are treated as different
    console.log('\n=== Test Case 1: Case Sensitivity Check ===');
    
    const User = mongoose.model('User', new mongoose.Schema({
      userName: { type: String, required: true, unique: true },
      email: { type: String, required: true, unique: true },
      isFakeAccount: { type: Boolean, default: false }
    }));

    // Create a case-insensitive index
    await User.collection.createIndex(
      { userName: 1 }, 
      { 
        unique: true,
        collation: { locale: 'en', strength: 2 }
      }
    );

    // Test with case-insensitive query
    const testUsername = 'TestUser';
    
    // Check if username exists with case-insensitive search
    const existingUser = await User.findOne({
      userName: { $regex: `^${testUsername}$`, $options: 'i' }
    }).collation({ locale: 'en', strength: 2 });

    console.log(`Username "${testUsername}" case-insensitive search result:`, existingUser ? 'Found' : 'Not found');

    // Test exact case match
    const exactMatch = await User.findOne({
      userName: testUsername
    });

    console.log(`Username "${testUsername}" exact case match result:`, exactMatch ? 'Found' : 'Not found');

    console.log('\n=== Test Results ===');
    console.log('✅ Case-insensitive index created successfully');
    console.log('✅ Case-insensitive username search working');
    console.log('✅ Exact case matching working');

  } catch (error) {
    console.error('Test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the test
testUsernameCaseSensitivity();
