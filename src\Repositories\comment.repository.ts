import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { commentDocument } from 'src/Models/comment.schema';
import { Privacy } from 'src/common/constant/enum';

@Injectable()
export class CommentRepository extends AbstractRepository<commentDocument> {
  constructor(@InjectModel('Comment') commentModel: Model<commentDocument>) {
    super(commentModel);
  }

  async getAllUserComments(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const data = await this.model.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'posts',
          localField: 'postId',
          foreignField: '_id',
          as: 'postId',
          pipeline: [
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'userId',
                pipeline: [
                  { $match: { isFakeAccount: false } },
                  {
                    $project: {
                      firstName: 1,
                      lastName: 1,
                      businessOrganizationName: 1,
                      userName: 1,
                      profileImage: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,

                      hirerEmployerVerifiedStatus: 1,
                      isMembershipVerified: 1,
                      isFakeAccount: 1,
                    },
                  },
                ],
              },
            },
            {
              $lookup: {
                from: 'groups',
                localField: 'group',
                foreignField: '_id',
                as: 'group',
                pipeline: [
                  {
                    $project: {
                      name: 1,
                      privacy: 1,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$group',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'users',
                localField: 'fundraisers',
                foreignField: '_id',
                as: 'fundraisers',
                pipeline: [
                  {
                    $match: {
                      isFakeAccount: false,
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      businessOrganizationName: 1,
                      userName: 1,
                      profileImage: 1,
                      followers: 1,
                      following: 1,
                      connections: 1,
                      accountVerified: 1,
                      iAmMember: 1,
                      professions: 1,

                      hirerEmployerVerifiedStatus: 1,
                      isMembershipVerified: 1,
                      isFakeAccount: 1,
                    },
                  },
                ],
              },
            },
            {
              $addFields: {
                isPrivateGroupComment: {
                  $cond: {
                    if: {
                      $or: [
                        {
                          $and: [
                            { $ne: ['$group', null] }, // group exists
                            { $eq: ['$group.privacy', Privacy.PRIVATE] },
                          ],
                        },
                        {
                          $eq: ['$group', null], // group doesn't exist
                        },
                      ],
                    },
                    then: true,
                    else: false,
                  },
                },
              },
            },
            { $unwind: { path: '$userId', preserveNullAndEmptyArrays: true } },
            {
              $project: {
                updatedAt: 0,
                __v: 0,
                group: 0,
                repostBy: 0,
                collaborators: 0,
                taggedPeople: 0,
              },
            },
          ],
        },
      },
      { $unwind: { path: '$postId', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'postId.isPrivateGroupComment': false,
        },
      },
      {
        $project: {
          updatedAt: 0,
          __v: 0,
          taggedUsers: 0,
          reactions: 0,
          reactionCounts: 0,
          totalReactions: 0,
          parentId: 0,
        },
      },
      { $sort: sortObj },
      { $skip: skipData },
      { $limit: limitData },
    ]);

    return data;
  }
}
