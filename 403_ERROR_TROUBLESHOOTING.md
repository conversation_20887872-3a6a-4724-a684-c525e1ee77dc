# 403 Forbidden Error - Troubleshooting Guide

## Overview

The 403 Forbidden error occurs when your Azure AD application doesn't have the necessary permissions to send emails via Microsoft Graph API. This is the most common issue when setting up OAuth 2.0 email sending.

## Common Causes & Solutions

### 1. **Missing API Permissions**

**Problem**: The application doesn't have the required `Mail.Send` permission.

**Solution**:
1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Select your registered application
4. Go to **API permissions**
5. Click **Add a permission**
6. Select **Microsoft Graph**
7. Choose **Application permissions**
8. Search for and add:
   - `Mail.Send` - Allows the app to send emails
   - `Mail.ReadWrite` - Allows the app to read and write emails
9. Click **Add permissions**
10. **IMPORTANT**: Click **Grant admin consent** (requires admin privileges)

### 2. **Admin Consent Not Granted**

**Problem**: Permissions are added but admin consent hasn't been granted.

**Solution**:
1. In your app registration, go to **API permissions**
2. Look for a yellow warning icon next to your permissions
3. Click **Grant admin consent for [Your Organization]**
4. Confirm the action

### 3. **User Account Permissions**

**Problem**: The user account specified in `SMTP_MAIL` doesn't have permission to send emails.

**Solution**:
1. Verify the email account exists and is active
2. Check if the account has been granted permission to send emails
3. Consider using a shared mailbox or service account
4. Ensure the account is in the same tenant as your application

### 4. **Application vs Delegated Permissions**

**Problem**: Using the wrong type of permissions for your use case.

**Solution**:
- **For server-to-server (recommended)**: Use **Application permissions**
- **For user-specific actions**: Use **Delegated permissions** (requires user consent)

### 5. **Tenant Configuration Issues**

**Problem**: Application is not properly configured for the tenant.

**Solution**:
1. Verify the tenant ID matches your organization
2. Ensure the application is registered in the correct tenant
3. Check if your organization has restrictions on app registrations

## Step-by-Step Verification

### Step 1: Check Current Permissions
```bash
# Test OAuth 2.0 token acquisition
node test-oauth.js
```

### Step 2: Verify API Permissions
1. Go to Azure Portal > App registrations > Your app
2. Check **API permissions** tab
3. Ensure `Mail.Send` is listed under **Application permissions**
4. Verify status shows "Granted for [Your Organization]"

### Step 3: Test Graph API Access
```bash
# Test the connection endpoint
GET http://localhost:3000/test-oauth
```

### Step 4: Check User Account
1. Verify the email in `SMTP_MAIL` environment variable
2. Ensure the account exists and is active
3. Test if the account can send emails manually

## Alternative Solutions

### Option 1: Use a Shared Mailbox
1. Create a shared mailbox in Exchange Online
2. Grant your application access to the shared mailbox
3. Update `SMTP_MAIL` to use the shared mailbox address

### Option 2: Use a Service Account
1. Create a dedicated service account for sending emails
2. Grant the service account appropriate permissions
3. Use the service account email in `SMTP_MAIL`

### Option 3: Use Delegated Permissions (Advanced)
1. Change from client credentials flow to authorization code flow
2. Implement user consent flow
3. Use delegated permissions instead of application permissions

## Debug Information

### Check Error Details
The updated implementation will provide detailed error information:
```json
{
  "message": "Permission denied. Please check your Azure AD application permissions...",
  "details": {
    "error": {
      "code": "ErrorAccessDenied",
      "message": "Access is denied. Check credentials and try again."
    }
  }
}
```

### Common Error Codes
- `ErrorAccessDenied`: Permission denied
- `ErrorForbidden`: Application doesn't have required permissions
- `ErrorUnauthorized`: Invalid or expired token
- `ErrorMailboxNotFound`: User account doesn't exist

## Prevention Tips

1. **Always grant admin consent** after adding permissions
2. **Use application permissions** for server-to-server scenarios
3. **Test with a simple email first** before implementing complex features
4. **Monitor permission changes** in your Azure AD audit logs
5. **Keep client secrets secure** and rotate them regularly

## Getting Help

If you're still experiencing issues:

1. Check the [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)
2. Review [Azure AD OAuth 2.0 Best Practices](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-client-creds-grant-flow)
3. Contact your Azure AD administrator for permission issues
4. Check the [Microsoft Graph API Status](https://status.office.com/) for service issues 