import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AdminService } from '../services/admin.service';
import { recordsDto } from '../dtos/record.dtos';
import { Request } from 'express';
import { AuthGuard } from 'src/Modules/auth/auth.guard';

@Controller('cms')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Patch('records')
  async updaterecords(@Body() recordsData: recordsDto) {
    return this.adminService.updateRecords(recordsData);
  }

  @Post('signupdata')
  async addsignupdata() {
    return this.adminService.addsignupdata();
  }

  //API for sorted data aplphabetically and remove duplicates
  @Post('add')
  async adddata(@Body() req) {
    return this.adminService.adddata(req);
  }

  @UseGuards(AuthGuard)
  @Post('missing-information')
  async addMissingInformation(@Req() req: Request, @Body() body) {
    return this.adminService.addMissingInformation(req, body);
  }

  @UseGuards(AuthGuard)
  @Get('missing-information')
  async getAllMissingInformation() {
    return this.adminService.getAllMissingInformation();
  }
}
