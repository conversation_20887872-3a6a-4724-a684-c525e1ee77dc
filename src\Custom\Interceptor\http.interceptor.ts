import {
  CallH<PERSON>ler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { EncdecService } from '../../common/services/encdec.service';

@Injectable()
export class HttpInterceptor implements NestInterceptor {
  constructor(private readonly securityService?: EncdecService) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const encPayload = await context.switchToHttp().getRequest().body.payload;
    const decData = await this.securityService.doDecryptRequest(encPayload);
    console.log(
      '🤩 ~ file: http.interceptor.ts ~ line 15 ~ HttpInterceptor ~ intercept ~ decData',
      decData,
    );

    context.switchToHttp().getRequest().body = await JSON.parse(decData);

    return next.handle().pipe(
      map(async (response) => {
        const { data, ...rest } = response;
        const newdata = context.switchToHttp().getResponse();
        const enc = await this.securityService.doEncryptResponse(
          JSON.stringify(data),
        );
        return {
          data: enc,
          status: true,
          message: 'success',
          ...rest,
        };
      }),
    );
  }
}
