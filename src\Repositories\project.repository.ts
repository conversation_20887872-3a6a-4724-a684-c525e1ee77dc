import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { projectDocument } from 'src/Models/project';

@Injectable()
export class ProjectRepository extends AbstractRepository<projectDocument> {
  constructor(@InjectModel('Project') projectModel: Model<projectDocument>) {
    super(projectModel);
  }

  async findWithCollaborators(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    const results: any = await this.model
      .find(filter)
      .populate({
        path: 'collaborators',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({
        path: 'affiliateOrganizations',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .populate({
        path: 'userId',
        match: { isFakeAccount: false },
        select:
          '_id firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified userProfileAboutYou.skills',
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();

    return results.map((project) => {
      if (project.userId?.userProfileAboutYou?.skills) {
        project = {
          ...project.toObject(),
          skills: project.userId.userProfileAboutYou.skills,
        };
      }

      project.userId = project.userId?._id;
      return project;
    });
  }

  async addProject(data: any) {
    return new this.model(data).save();
  }

  async updateProject(id: string, updateData: Partial<projectDocument>) {
    return this.model
      .findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      })
      .exec();
  }
}
