import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsMongoId, IsString } from 'class-validator';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';
import CONSTANT from '../../../common/constant/common.constant';
import { InvitationType } from 'src/common/constant/enum';

export class InviteAcceptRejectDto {
  @IsMongoId({ message: CONSTANT.INVALID('notificationId') })
  @IsStringValidation('notificationId', 24, false)
  readonly notificationId: string;

  @IsString()
  @IsEnum(InvitationType, {
    each: true,
    message: CONSTANT.INVALID('invitationType'),
  })
  readonly invitationType: string;
}

export class removeGroupMemberDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;

  @IsArray()
  @IsString({ each: true, message: CONSTANT.INVALID('memberIds') })
  readonly memberIds: string[];
}

export class blockUnblockMemberDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, false)
  readonly groupId: string;

  @IsString()
  readonly memberId: string;
}
