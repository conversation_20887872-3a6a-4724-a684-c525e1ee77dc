import multer from 'multer';
import CONSTANT from '../../common/constant/common.constant';
import { HttpException, HttpStatus } from '@nestjs/common';

// Image & Video upload
export const validFileToUpload = (req, res, next) => {
  // MaxMB is for video file size
  const maxMB = 100 * 1024 * 1024;
  const fileSize = parseInt(req.headers['content-length']);
  const imageStorage = multer.memoryStorage();

  const upload = multer({
    storage: imageStorage,
    limits: { fileSize: maxMB }, // File size limit 10 MB
    fileFilter(req, file, cb) {
      const fileType = file.mimetype.split('/')[0];

      if (fileType === 'image') {
        // Check if file is image & file size is greater than 10MB
        if (fileSize >= 10 * 1024 * 1024) {
          throw new HttpException(
            CONSTANT.FILE_SIZE_LARGE,
            HttpStatus.FORBIDDEN,
          );
        }
        if (!file.originalname.toLowerCase().match(/\.(png|jpg|jpeg|gif)$/)) {
          // upload only png, jpg, jpeg, gif format
          throw new HttpException(
            CONSTANT.IMAGE_TYPE_ERROR,
            HttpStatus.FORBIDDEN,
          );
        }
      } else if (fileType === 'video') {
        if (
          !file.originalname.toLowerCase().match(/\.(mp4|mkv|mpeg|mov|webm)$/)
        ) {
          // upload only mp4, mkv, mpeg, mov, webm format
          throw new HttpException(
            CONSTANT.VIDEO_TYPE_ERROR,
            HttpStatus.FORBIDDEN,
          );
        }
      } else {
        throw new HttpException(
          CONSTANT.INVALID_FILE_TYPE,
          HttpStatus.FORBIDDEN,
        );
      }

      cb(null, true);
    },
  }).array('file');

  upload(req, res, (err) => {
    if (err?.message === 'Unexpected field')
      throw new HttpException(
        CONSTANT.REQUIRED('The upload field must be'),
        HttpStatus.UNPROCESSABLE_ENTITY,
      );

    if (err?.message === 'File too large')
      throw new HttpException(CONSTANT.IMAGE_SIZE_LARGE, HttpStatus.FORBIDDEN);

    next();
  });
};
