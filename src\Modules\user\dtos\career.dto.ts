import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { JobPostWorkplaceType } from 'src/common/constant/enum';
import { JobTypeEnum } from 'src/Models/jobList.schema';

export class MediaDto {
  @IsString()
  mediaType: string;

  @IsString()
  url: string;

  @IsString()
  thumbUrl: string | null;
}
export class educationDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  @IsOptional()
  school: string;

  @IsString()
  degree: string;

  @IsString()
  @IsOptional()
  fieldOfStudy: string;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @IsBoolean()
  isAlum: boolean;

  @IsOptional()
  @IsString()
  activities: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsArray({ message: 'skills must be an array of strings' })
  skills: string[];

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}

export class licenseCertificationDto {
  @IsOptional()
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  organization: string;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  issueDate: Date;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  expirationDate: Date;

  @IsString()
  credentialId: string;

  @IsString()
  credentialUrl: string;

  @IsOptional()
  @IsArray({ message: 'skills must be an array of strings' })
  skills: string[];

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}

export class HonorAndRewardDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  title: string;

  @IsString()
  organizationAssociatedWith: string;

  @IsString()
  description: string;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  issueDate: Date;

  @IsString()
  issuer: string;

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}

export class volunteerExperienceDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsArray()
  @IsString({ each: true })
  organization: string[];

  @IsString()
  role: string;

  @IsString()
  cause: string;

  @IsBoolean()
  isVolunteering: boolean;

  @IsDate()
  @IsOptional()
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @IsDate()
  @IsOptional()
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @IsString()
  description: string;

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}

export class experienceDto {
  @IsOptional()
  @IsString()
  id: string;

  @IsString()
  title: string;

  @IsString()
  companyName: string;

  @IsString()
  employmentType: JobTypeEnum;

  @IsBoolean()
  isCurrentlyWorking: boolean;

  @IsDate()
  @IsOptional()
  @Transform(({ value }) => new Date(value))
  startDate: Date;

  @IsDate()
  @IsOptional()
  @Transform(({ value }) => new Date(value))
  endDate: Date;

  @IsString()
  location: string;

  @IsString()
  locationType: JobPostWorkplaceType;

  @IsString()
  description: string;

  @IsArray()
  @IsString({ each: true })
  skills: string[];

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];
}
