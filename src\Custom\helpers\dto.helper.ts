import {
  IsBoolean,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON>Length,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import CONSTANT from '../../common/constant/common.constant';
import { Types } from 'mongoose';
import { BadRequestException } from '@nestjs/common';
import { Transform } from 'class-transformer';

export function IsStringValidation(
  message: string,
  maxLength: number | null,
  isOptional: boolean,
  isNotEmpty = true,
) {
  return (target: any, key: string) => {
    if (isNotEmpty) IsNotEmpty()(target, key);
    IsString({ message: CONSTANT.INVALID(message) })(target, key);
    if (maxLength) MaxLength(maxLength)(target, key);
    if (isOptional) IsOptional()(target, key);
  };
}

export function IsNumberValidation(
  maxLength: number | null,
  isOptional: boolean,
) {
  return (target: any, key: string) => {
    IsNotEmpty()(target, key);
    IsNumber()(target, key);
    if (maxLength) MaxLength(maxLength)(target, key);
    if (isOptional) IsOptional()(target, key);
  };
}

export function IsBooleanValidation(message: string, isOptional: boolean) {
  return (target: any, key: string) => {
    IsNotEmpty()(target, key);
    IsBoolean({ message: CONSTANT.INVALID(message) })(target, key);
    if (isOptional) IsOptional()(target, key);
  };
}

export const SafeMongoIdTransform = ({ value }) => {
  try {
    if (
      Types.ObjectId.isValid(value) &&
      new Types.ObjectId(value).toString() === value
    ) {
      return value;
    }
    throw new BadRequestException('Invalid ID parameter');
  } catch (error) {
    throw new BadRequestException('Invalid ID parameter');
  }
};

export class IDParamDTO {
  @IsMongoId()
  @IsStringValidation('id', 24, false)
  @Transform((value: any) => SafeMongoIdTransform(value))
  readonly id: string;
}

export function IsLongitude(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isLongitude',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          return typeof value === 'number' && value >= -180 && value <= 180;
        },
        defaultMessage() {
          return 'Longitude must be a number between -180 and 180';
        },
      },
    });
  };
}
export function IsLatitude(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isLatitude',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          return typeof value === 'number' && value >= -90 && value <= 90;
        },
        defaultMessage() {
          return 'Latitude must be a number between -90 and 90';
        },
      },
    });
  };
}
