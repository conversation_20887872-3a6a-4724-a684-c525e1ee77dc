import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { WebService } from '../services/web.service';
import { BetaLeadDto, ContactUsDto } from '../dtos/web.dtos';
import { Request, Response } from 'express';
import path from 'path';

@Controller('web')
export class WebController {
  constructor(private readonly webService: WebService) {}

  @Post('join-beta-program')
  async joinBetaProgram(@Body() recordsData: BetaLeadDto) {
    return this.webService.joinBetaProgram(recordsData);
  }

  @Post('contact-us')
  async contactUs(@Body() recordsData: ContactUsDto) {
    return this.webService.contactUs(recordsData);
  }

  @Get('privacy-policy')
  async getPrivacyPolicy(@Req() req: Request, @Res() res: Response) {
    return res.render('privacyPolicy');
  }

  @Get('terms-of-use')
  async getTermOfUse(@Req() req: Request, @Res() res: Response) {
    return res.render('termOfUse');
  }
}
