import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';

export type educationDocument = Education & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class Education {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  school: mongoose.Types.ObjectId;

  @Prop({ type: String })
  degree: string;

  @Prop({ type: String })
  fieldOfStudy: string;

  @Prop({ type: Date })
  startDate: Date;

  @Prop({ type: Date })
  endDate: Date;

  @Prop({ type: Boolean })
  isAlum: boolean;

  @Prop({ type: String })
  activities: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: [String] })
  skills: string[];

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const EducationSchema = SchemaFactory.createForClass(Education);
