# Microsoft 365 OAuth 2.0 Setup Guide

This guide explains how to configure Microsoft 365 OAuth 2.0 authentication for the email service in your NestJS application.

## Prerequisites

1. A Microsoft 365 account with admin privileges
2. Access to Azure Active Directory (Azure AD)
3. The application must be registered in Azure AD

## Step 1: Register Application in Azure AD

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill in the following details:
   - **Name**: Your application name (e.g., "Pepli Email Service")
   - **Supported account types**: Choose based on your needs (usually "Accounts in this organizational directory only")
   - **Redirect URI**: Leave blank (not needed for client credentials flow)
5. Click **Register**

## Step 2: Configure API Permissions

1. In your registered application, go to **API permissions**
2. Click **Add a permission**
3. Select **Microsoft Graph**
4. Choose **Application permissions**
5. Search for and add the following permissions:
   - `Mail.Send` - Allows the app to send emails
   - `Mail.ReadWrite` - Allows the app to read and write emails
6. Click **Add permissions**
7. Click **Grant admin consent** (requires admin privileges)

## Step 3: Create Client Secret

1. In your registered application, go to **Certificates & secrets**
2. Click **New client secret**
3. Add a description and choose expiration
4. Click **Add**
5. **IMPORTANT**: Copy the secret value immediately (you won't be able to see it again)

## Step 4: Get Application Details

From your registered application, note down:
- **Application (client) ID**
- **Directory (tenant) ID**
- **Client Secret** (from Step 3)

## Step 5: Configure Environment Variables

Add the following environment variables to your `.env` file:

```env
# Microsoft 365 OAuth 2.0 Configuration
MICROSOFT_CLIENT_ID=your_client_id_here
MICROSOFT_CLIENT_SECRET=your_client_secret_here
MICROSOFT_TENANT_ID=your_tenant_id_here

# Email Configuration
SMTP_MAIL=<EMAIL>
```

## Step 6: Verify Configuration

The application will automatically:
1. Acquire an access token using client credentials flow
2. Use Microsoft Graph API to send emails with OAuth 2.0 authentication
3. Handle token refresh automatically

## OAuth 2.0 Flow Explanation

This implementation uses the **Client Credentials Flow**, which is designed for server-to-server authentication:

- **No user interaction required**: The application authenticates directly with Microsoft
- **No redirect URI needed**: Unlike web applications, there's no browser redirect
- **Application-level permissions**: Uses application permissions, not delegated permissions
- **Automatic token management**: Tokens are acquired and refreshed automatically
- **Microsoft Graph API**: Uses Graph API instead of SMTP for better OAuth 2.0 compatibility

This is different from the Authorization Code Flow used in web applications where users log in through a browser.

## Troubleshooting

### Common Issues

1. **"Can't create new access token for user"**
   - This error occurs when nodemailer can't generate OAuth2 tokens
   - **Solution**: The updated implementation uses Microsoft Graph API instead of SMTP
   - Ensure all environment variables are correctly set
   - Run the test script: `node test-oauth.js`

2. **"AADSTS7000215: Invalid client secret"**
   - Ensure the client secret is correct and hasn't expired
   - Create a new client secret if needed

3. **"AADSTS70011: The request body must contain the following parameter: 'scope'"**
   - The scope is automatically set to `https://graph.microsoft.com/.default`
   - Ensure the application has the correct API permissions

4. **"EAUTH: Invalid login"**
   - Verify the email address in `SMTP_MAIL` matches the account used for the application registration
   - Ensure the account has permission to send emails

5. **"AADSTS50020: User account from a foreign tenant"**
   - Ensure the tenant ID matches your organization's tenant ID
   - Verify the application is registered in the correct tenant

6. **"403 Forbidden - Permission denied"**
   - **Most Common Cause**: Missing or incorrect API permissions
   - **Solution**: 
     - Go to Azure Portal > App registrations > Your app > API permissions
     - Add `Mail.Send` permission under Microsoft Graph
     - Click "Grant admin consent" (requires admin privileges)
     - Ensure the user account has permission to send emails
   - **Alternative**: Use a shared mailbox or service account

7. **"Missing required OAuth 2.0 configuration"**
   - Check that all environment variables are set in your `.env` file
   - Verify the variable names match exactly: `MICROSOFT_CLIENT_ID`, `MICROSOFT_CLIENT_SECRET`, `MICROSOFT_TENANT_ID`

### Testing the Connection

#### 1. Command Line Test
Run the test script to verify OAuth 2.0 configuration:
```bash
node test-oauth.js
```

#### 2. API Endpoint Tests
Once your application is running, you can test via API endpoints:

**Test OAuth 2.0 Configuration:**
```bash
GET http://localhost:3000/test-oauth
```

**Send Test Email:**
```bash
POST http://localhost:3000/test-email
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Test Email"
}
```

#### 3. Programmatic Testing
You can test the OAuth 2.0 connection by calling these methods:

```typescript
// In your service or controller
const tokenTest = await this.mailService.testOAuth2Token();
const connectionTest = await this.mailService.verifyConnection();
console.log('Token test:', tokenTest);
console.log('Connection test:', connectionTest);
```

## Security Considerations

1. **Client Secret Security**: Never commit client secrets to version control
2. **Token Storage**: Access tokens are stored in memory and automatically refreshed
3. **Scope Limitation**: Only request the minimum required permissions
4. **Monitoring**: Monitor failed authentication attempts and token refresh failures

## API Permissions Reference

The following Microsoft Graph permissions are required:

| Permission | Description | Required |
|------------|-------------|----------|
| `Mail.Send` | Send emails on behalf of the user | Yes |
| `Mail.ReadWrite` | Read and write emails | Optional |

## Additional Resources

- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)
- [Azure AD OAuth 2.0 Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-client-creds-grant-flow)
- [Nodemailer OAuth2 Documentation](https://nodemailer.com/smtp/oauth2/) 