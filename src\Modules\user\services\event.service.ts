import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import CONSTANT from '../../../common/constant/common.constant';
import mongoose from 'mongoose';
import { createSearchFilterSortPagination } from 'src/Custom/helpers/query.helper';
import { EventRepository } from 'src/Repositories/event.repository';
import { EventDto } from '../dtos/event.dto';

@Injectable()
export class EventService {
  constructor(private readonly eventRepository: EventRepository) {}

  public async addEvent(req, eventData: EventDto) {
    try {
      const { user: loggedInUser } = req;
      const { _id: id, collaborators, ...restEventData } = eventData;

      const collaboratorObjectIds = collaborators?.map(
        (collaboratorId) => new mongoose.Types.ObjectId(collaboratorId),
      );

      if (id) {
        const eventExist = await this.eventRepository.findById(id);
        if (!eventExist) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Event'),
            HttpStatus.BAD_REQUEST,
          );
        }

        await this.eventRepository.findByIdAndUpdate(id, {
          ...restEventData,
          startDate: new Date(restEventData.startDate),
          collaborators: collaboratorObjectIds,
          userId: loggedInUser._id,
        });

        return successResponse(
          null,
          CONSTANT.UPDATED_SUCCESSFULLY('Event'),
          HttpStatus.OK,
        );
      }

      const data = {
        ...restEventData,
        collaborators: collaboratorObjectIds,
        organizedBy: loggedInUser._id,
        participants: [],
      };

      await this.eventRepository.create(data);

      return successResponse(
        null,
        CONSTANT.ADDED_SUCCESSFULLY('Event'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getEvents(userId: string, req: any) {
    try {
      const { search, sortBy, sort, page, perPage } = req.query;
      const searchFields = ['title'];

      let matchObj = { userId: new mongoose.Types.ObjectId(userId) };

      const { searchObj, sortObj, filterObj, skipData, limitData } =
        createSearchFilterSortPagination(
          search,
          searchFields,
          null,
          { sortBy, sort },
          { page, perPage },
        );

      matchObj = { ...matchObj, ...searchObj, ...filterObj };

      const events = await this.eventRepository.findAllEventWithDetails(
        matchObj,
        sortObj,
        skipData,
        limitData,
      );

      const totalResults = await this.eventRepository.countDocuments(matchObj);

      const paginationObj = {
        totalResults,
        currentResults: events?.length,
        totalPages: Math.ceil(totalResults / limitData),
        currentPage: Number(page) || 1,
      };

      return successResponse(
        events,
        CONSTANT.FETCHED_SUCCESSFULLY('Events'),
        HttpStatus.OK,
        paginationObj,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async deleteEvent(id: string) {
    try {
      const eventExist = await this.eventRepository.findById(id);
      if (!eventExist) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Event'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.eventRepository.deleteById(id);

      return successResponse(
        null,
        CONSTANT.DELETED_SUCCESSFULLY('Event'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async participateEvent(
    req,
    eventId: string,
    action: 'join' | 'leave',
  ) {
    try {
      const { user: loggedInUser } = req;

      const event = await this.eventRepository.findById(eventId);
      if (!event) {
        throw new HttpException(
          CONSTANT.NOT_FOUND_MESSAGE('Event'),
          HttpStatus.NOT_FOUND,
        );
      }

      const userId = new mongoose.Types.ObjectId(loggedInUser._id);
      const isParticipant = event.participants.some((participant) =>
        participant.equals(userId),
      );

      if (action === 'join') {
        if (isParticipant) {
          throw new HttpException(
            CONSTANT.ALREADY_PARTICIPATED,
            HttpStatus.BAD_REQUEST,
          );
        }

        event.participants.push(userId);
        await event.save();

        return successResponse(
          null,
          CONSTANT.SUCCESSFULL('Participated in the event.'),
          HttpStatus.OK,
        );
      } else if (action === 'leave') {
        if (!isParticipant) {
          throw new HttpException(
            CONSTANT.NOT_FOUND_MESSAGE('Participant'),
            HttpStatus.BAD_REQUEST,
          );
        }

        event.participants = event.participants.filter(
          (participant) => !participant.equals(userId),
        );
        await event.save();

        return successResponse(
          null,
          'Successfully left the event.',
          HttpStatus.OK,
        );
      }

      throw new HttpException('Invalid action.', HttpStatus.BAD_REQUEST);
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
