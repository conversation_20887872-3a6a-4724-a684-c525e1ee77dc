const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/your_database', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define schemas (simplified versions for migration)
const userSignupDataSchema = new mongoose.Schema({
  title: String,
  parentSlug: [String],
  itemText: String,
  slug: String,
  selectionType: String,
  subCategory: String,
}, { timestamps: true });

const userSchema = new mongoose.Schema({
  businessOrganizationName: String,
  iAmMember: String,
  signUpData: [{
    itemId: mongoose.Schema.Types.ObjectId,
    isSelected: Boolean,
    subCategory: String,
    title: String,
    slug: String,
    itemText: String
  }]
});

const UserSignupData = mongoose.model('UserSignupData', userSignupDataSchema);
const User = mongoose.model('User', userSchema);

// Migration function
async function migrateOrganizations() {
  try {
    console.log('Starting organization migration...');

    // Get all users who are organizations
    const organizationUsers = await User.find({
      iAmMember: 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility',
      businessOrganizationName: { $exists: true, $ne: null, $ne: '' },
      isFakeAccount: false
    });

    console.log(`Found ${organizationUsers.length} organizations to migrate`);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const user of organizationUsers) {
      try {
        // Check if organization already exists in userSignupData
        const existingOrg = await UserSignupData.findOne({
          itemText: user.businessOrganizationName,
          subCategory: "organization"
        });

        if (existingOrg) {
          console.log(`Skipping ${user.businessOrganizationName} - already exists`);
          skippedCount++;
          continue;
        }

        // Get main category from existing API
        const mainCategories = await UserSignupData.find({
          parentSlug: { $in: ["4"] },
          subCategory: "main_category"
        }).sort({ _id: 1 });

        if (mainCategories.length === 0) {
          console.log(`No main categories found for ${user.businessOrganizationName}`);
          continue;
        }

        const mainCategory = mainCategories[0]; // Use first main category for organization type
        let subcategorySlug = null;

        // Try to find subcategory from user's signUpData
        if (user.signUpData && user.signUpData.length > 0) {
          const selectedSubcategories = user.signUpData
            .filter(item => item.isSelected && item.subCategory === "sub_type")
            .map(item => item.slug);

          if (selectedSubcategories.length > 0) {
            subcategorySlug = selectedSubcategories[0]; // Use first selected subcategory
          }
        }

        // If no subcategory found, get available subcategories from existing API
        if (!subcategorySlug) {
          const availableSubcategories = await UserSignupData.find({
            parentSlug: [mainCategory.slug],
            subCategory: "sub_type"
          }).sort({ _id: 1 });

          if (availableSubcategories.length > 0) {
            // Use the first available subcategory as default
            subcategorySlug = availableSubcategories[0].slug;
          } else {
            // If no subcategories available, create a generic one
            subcategorySlug = `${mainCategory.slug}_generic`;
            
            // Check if generic subcategory exists
            let genericSubcategory = await UserSignupData.findOne({
              slug: subcategorySlug
            });

            if (!genericSubcategory) {
              genericSubcategory = await new UserSignupData({
                title: `${mainCategory.slug}_subcategory`,
                parentSlug: [mainCategory.slug],
                itemText: `Other ${mainCategory.itemText}`,
                slug: subcategorySlug,
                selectionType: "multiple",
                subCategory: "sub_type"
              }).save();
              console.log(`Created generic subcategory: ${subcategorySlug}`);
            }
          }
        }

        // Create organization entry
        const organizationSlug = `${subcategorySlug}_${user.businessOrganizationName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
        
        await new UserSignupData({
          title: `${subcategorySlug}_organization`,
          parentSlug: [subcategorySlug],
          itemText: user.businessOrganizationName,
          slug: organizationSlug,
          selectionType: "multiple",
          subCategory: "organization"
        }).save();

        console.log(`Migrated: ${user.businessOrganizationName} -> ${subcategorySlug}`);
        migratedCount++;

      } catch (error) {
        console.error(`Error migrating ${user.businessOrganizationName}:`, error.message);
      }
    }

    console.log(`\nMigration completed!`);
    console.log(`Migrated: ${migratedCount} organizations`);
    console.log(`Skipped: ${skippedCount} organizations (already existed)`);

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run migration
migrateOrganizations(); 