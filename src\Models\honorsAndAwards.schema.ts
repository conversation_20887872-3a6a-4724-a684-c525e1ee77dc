import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';

export type HonorAndAwardDocument = HonorAndAward & Document;

interface MediaData {
  mediaType: string;
  url: string;
  thumbUrl: string | null;
}

@Schema({ timestamps: true, versionKey: false })
export class HonorAndAward {
  @Prop({ type: String })
  title: string;

  @Prop({ type: String })
  organizationAssociatedWith: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  issuer: string;

  @Prop({ type: Date })
  issueDate: Date;

  @Prop({
    type: [
      {
        mediaType: String,
        url: String,
        thumbUrl: { type: String },
        _id: false,
      },
    ],
  })
  media: MediaData[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  userId: mongoose.Types.ObjectId;
}

export const HonorAndAwardSchema = SchemaFactory.createForClass(HonorAndAward);
