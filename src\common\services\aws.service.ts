import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import CONSTANT from '../constant/common.constant';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import fs from 'fs';
import Ffmpeg = require('fluent-ffmpeg');

@Injectable()
export class AwsService {
  private readonly s3Client: S3Client;

  constructor(private readonly configService: ConfigService) {
    this.s3Client = new S3Client({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
    });
  }

  async getFilenameFromS3Url(url, mediaType) {
    if (mediaType === 'PROFILE') {
      return await url.split('/profile/').slice(1).toString();
    } else if (mediaType === 'POST') {
      return await url.split('/posts/').slice(1).toString();
    } else if (mediaType === 'STORY') {
      return await url.split('/stories/').slice(1).toString();
    } else {
      return await url.split('/stories/thumbnail/').slice(1).toString();
    }
  }

  async generateThumbnail(videoPath, thumbnailData) {
    return await new Promise((resolve, reject) => {
      Ffmpeg(videoPath)
        .screenshots({
          ...thumbnailData,
        })
        // .size('640x480')
        .on('end', () => {
          resolve(true);
        })
        .on('error', (err) => {
          console.error('ffmpeg thumbnail generation error:', err);
          reject(err);
        });
    });
  }

  async signedUrl(key: string) {
    if (key) {
      const command = new GetObjectCommand({
        Bucket: this.configService.get<string>('AWS_BUCKET'),
        Key: key,
      });

      try {
        return await getSignedUrl(this.s3Client, command, {
          expiresIn: 86400, //3600:1hr, 86400:1Day
        });
      } catch (error) {
        console.error('Error getting signed url:', error);
      }
    } else {
      return null;
    }
  }

  async uploadFile(file: any, folderPath: string) {
    let folder = folderPath;
    if (this.configService.get<string>('NODE_ENV') !== 'development') {
      folder = folderPath.split('Pepli/')[1];
    }

    const myFile = file.originalname.toLowerCase().split('.');
    const fileType = myFile[myFile.length - 1];
    const randomString = Math.floor(Date.now() * Math.random());

    const command = new PutObjectCommand({
      ContentType: file.mimetype,
      Bucket: this.configService.get<string>('AWS_BUCKET'),
      Key: `${folder}${randomString}.${fileType}`,
      Body: file.buffer,
    });

    try {
      const uploadCommand: any = await this.s3Client.send(command);
      if (!uploadCommand) {
        throw new HttpException(
          CONSTANT.FILE_UPLOAD_ERROR,
          HttpStatus.FORBIDDEN,
        );
      }
      // if (uploadCommand.$metadata.httpStatusCode === HttpStatus.OK) {
      //   return `${folder}${randomString}.${fileType}`;
      // } else {
      //   throw new HttpException(
      //     { message: 'File uploading error' },
      //     HttpStatus.FORBIDDEN,
      //   );
      // }
      const fileUrl = `https://${this.configService.get<string>(
        'AWS_BUCKET',
      )}.s3.${this.configService.get<string>(
        'AWS_REGION',
      )}.amazonaws.com/${folder}${randomString}.${fileType}`;

      return fileUrl;
    } catch (error) {
      console.error('Error uploading file to S3=', error);
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  async s3Upload(files: any, folder: string) {
    if (Array.isArray(files)) {
      return Promise.all(files.map((file) => this.uploadFile(file, folder)));
    }
    return this.uploadFile(files, folder);
  }

  async s3Delete(file, folderPath, mediaType) {
    if (file) {
      // Get filename from s3 bucket url
      const filename = await this.getFilenameFromS3Url(file, mediaType);

      const command = new DeleteObjectCommand({
        Bucket: this.configService.get<string>('AWS_BUCKET'),
        Key: `${folderPath}${filename}`,
      });

      try {
        const deleteData: any = await this.s3Client.send(command);
        if (deleteData.$metadata.httpStatusCode == HttpStatus.NO_CONTENT) {
          console.log('File Deleted');
        }
      } catch (error) {
        console.error('Error deleting file to S3=', error);
      }
    }
    return;
  }

  async s3DeleteByKey(key: string) {
    if (key) {
      const command = new DeleteObjectCommand({
        Bucket: this.configService.get<string>('AWS_BUCKET'),
        Key: key,
      });

      try {
        const deleteData: any = await this.s3Client.send(command);
        if (deleteData.$metadata.httpStatusCode !== HttpStatus.NO_CONTENT) {
          console.log(CONSTANT.ERROR('deleting file'));
        }
      } catch (error) {
        console.error('Error deleting file to S3:', error);
      }
    } else {
      return;
    }
  }

  async uploadThumbnail(videoPath, folderPath) {
    try {
      const videoThumbName = `thumb_${Date.now()}.png`;
      const dir = `${process.cwd()}/public/thumbnail`;

      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      const thumbnailData = {
        filename: videoThumbName,
        folder: dir,
        count: 1,
        // size: '536x354',
      };
      await this.generateThumbnail(videoPath, thumbnailData);

      const bodyThumb = await fs.createReadStream(
        `${process.cwd()}/public/thumbnail/${videoThumbName}`,
      );

      if (!bodyThumb) {
        return null;
      }

      const command = new PutObjectCommand({
        Bucket: this.configService.get<string>('AWS_BUCKET'),
        Key: `${folderPath}/${videoThumbName}`,
        Body: bodyThumb,
      });

      const uploadCommand: any = await this.s3Client.send(command);
      if (!uploadCommand) {
        throw new HttpException(
          CONSTANT.THUMBNAIL_UPLOAD_ERROR,
          HttpStatus.FORBIDDEN,
        );
      }

      const thumbnailUrl = `https://${this.configService.get<string>(
        'AWS_BUCKET',
      )}.s3.${this.configService.get<string>(
        'AWS_REGION',
      )}.amazonaws.com/${folderPath}/${videoThumbName}`;

      fs.unlinkSync(`${process.cwd()}/public/thumbnail/${videoThumbName}`);
      return thumbnailUrl;
    } catch (error) {
      console.error('Error uploading thumbnail to S3=', error);
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
