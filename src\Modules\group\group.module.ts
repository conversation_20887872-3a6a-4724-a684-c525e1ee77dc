import { forwardRef, Module } from '@nestjs/common';
import { GroupService } from './group.service';
import { GroupController } from './group.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Group, GroupSchema } from 'src/Models/group.schema';
import { User, UserSchema } from 'src/Models/user.schema';
import { CommonModule } from 'src/common/common.module';
import {
  Notifications,
  NotificationsSchema,
} from 'src/Models/notification.schema';
import { GroupMember, GroupMemberSchema } from 'src/Models/groupMember.schema';
import { Bookmark, BookmarkSchema } from 'src/Models/bookmark.schema';
import { Post, PostSchema } from 'src/Models/post.schema';
import { GroupRule, GroupRuleSchema } from 'src/Models/groupRule.schema';
import {
  FollowerInfo,
  FollowerInfoSchema,
} from 'src/Models/followerInfo.schema';
import { PostModule } from '../post/post.module';

@Module({
  imports: [
    forwardRef(() => PostModule),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Group.name, schema: GroupSchema },
      { name: Post.name, schema: PostSchema },
      { name: GroupMember.name, schema: GroupMemberSchema },
      { name: Notifications.name, schema: NotificationsSchema },
      { name: Bookmark.name, schema: BookmarkSchema },
      { name: GroupRule.name, schema: GroupRuleSchema },
      { name: FollowerInfo.name, schema: FollowerInfoSchema },
    ]),
    CommonModule,
  ],
  controllers: [GroupController],
  providers: [GroupService],
  exports: [GroupService],
})
export class GroupModule {}
