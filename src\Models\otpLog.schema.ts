import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type otpLogDocument = OtpLog & Document;

@Schema({ timestamps: true })
export class OtpLog {
  @Prop()
  otp: string;

  @Prop()
  email: string;

  @Prop()
  phone: string;

  @Prop({ default: false })
  isMatch: boolean;

  @Prop()
  otpExpires: Date;

  @Prop()
  deletedAt: Date;
}
export const OtpLogSchema = SchemaFactory.createForClass(OtpLog);
