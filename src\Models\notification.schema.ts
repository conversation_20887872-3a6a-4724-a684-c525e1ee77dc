import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from './user.schema';
import {
  InvitationType,
  NotificationsType,
  RedirectionType,
} from 'src/common/constant/enum';
import { Group } from './group.schema';
import { Post } from './post.schema';
import { Story } from './story.schema';
export type NotificationDocument = Notification & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class Notifications {
  @Prop({ type: String, trim: true, required: true })
  title: string;

  @Prop({ type: String, trim: true })
  notificationMessage: string;

  @Prop({
    enum: NotificationsType,
  })
  notificationType: string;

  @Prop({
    enum: RedirectionType,
  })
  redirectionType: string;

  @Prop({ type: Boolean, required: true, default: false })
  isRead: boolean;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  sender: User;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  receiver: User;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Group' })
  group: Group;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Post' })
  postId: Post;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Story' })
  storyId: mongoose.Schema.Types.ObjectId;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'ConnectionInfo' })
  connectionId: mongoose.Schema.Types.ObjectId;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'ClientInfo' })
  clientId: mongoose.Schema.Types.ObjectId;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'People' })
  memberId: mongoose.Schema.Types.ObjectId;

  @Prop({
    enum: InvitationType,
    default: null,
  })
  action: string;

  @Prop({ type: Boolean })
  isGroupMember: boolean;
}

export const NotificationsSchema = SchemaFactory.createForClass(Notifications);
