# Global Search API Documentation

## Overview
The Global Search API provides Instagram-like search functionality across multiple content types in the Pepli Node application. It supports searching through accounts, posts, podcasts, jobs, events, and union organizations with tab-based filtering.

## API Endpoint

```
GET /user/search?q={search_string}
```

## Authentication
This endpoint requires authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer {your_jwt_token}
```

## Request Parameters

### Query Parameters
- `q` (string, required): The search string to look for

### Request Body
```json
{
  "tab": "for_you|accounts|posts|podcasts|jobs|events|union_organizations",
  "page": 1,
  "perPage": 10
}
```

### Tab Options
- `for_you` (default): Returns top results from all categories
- `accounts`: Search for user accounts
- `posts`: Search for posts
- `podcasts`: Search for podcast content
- `jobs`: Search for job postings
- `events`: Search for events
- `union_organizations`: Search for union organizations
- `tags`: Search for hashtags in posts

## Response Format

**Note:** All search responses maintain consistent data hierarchy for easier frontend integration. The `data` field always contains a nested object with the tab name as the key.

### For You Tab Response
```json
{
  "status": true,
  "message": "For You search results fetched successfully",
  "data": {
    "accounts": [...],           // Up to 8 accounts (prioritizing connections/followers)
    "posts": [...],             // Up to 8 posts (prioritizing connections/followers)
    "podcasts": [...],          // Up to 8 podcasts (prioritizing connections/followers)
    "jobs": [...],              // Up to 8 jobs (prioritizing connections/followers)
    "events": [...],            // Up to 8 events (prioritizing connections/followers)
    "union_organizations": [...], // Up to 8 organizations (prioritizing connections/followers)
    "relatedHashtags": [...]    // Up to 8 posts with related hashtags and counts (prioritizing connections/followers)
  }
}
```

**Priority Logic:**
- **First Priority**: Content from user's connections and followers
- **Second Priority**: Other relevant content from the platform
- **Maximum**: 8 items per category (instead of previous 3)
- **No Pagination**: Direct results for quick discovery

### Specific Tab Response
```json
{
  "status": true,
  "message": "Accounts fetched successfully",
  "data": {
    "accounts": [...]
  },
  "pagination": {
    "totalResults": 100,
    "currentResults": 10,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

### Jobs Search Response Example
```json
{
  "status": true,
  "message": "Jobs fetched successfully",
  "data": {
    "jobs": [
      {
        "_id": "job_id_here",
        "title": "Software Engineer",
        "company": "Tech Corp",
        "description": "We are looking for a skilled software engineer...",
        "workplaceType": "remote",
        "location": "New York, NY",
        "workType": "full-time",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "isApplied": true,
        "applicationCount": 25,
        "user": {
          "firstName": "John",
          "lastName": "Doe",
          "userName": "johndoe",
          "profileImage": "https://example.com/profile.jpg",
          "businessOrganizationName": "Tech Corp"
        }
      }
    ]
  },
  "pagination": {
    "totalResults": 50,
    "currentResults": 10,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

### Tags Search Response Examples

#### 1. General Search Response (e.g., "music")
```json
{
  "status": true,
  "message": "Related hashtags fetched successfully",
  "data": {
    "posts": [],
    "relatedHashtags": [
      {
        "hashtag": "#music",
        "count": 45
      },
      {
        "hashtag": "#live",
        "count": 23
      },
      {
        "hashtag": "#concert",
        "count": 18
      },
      {
        "hashtag": "#performance",
        "count": 12
      },
      {
        "hashtag": "#festival",
        "count": 8
      }
    ]
  },
  "pagination": {
    "totalResults": 5,
    "currentResults": 5,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

#### 2. Exact Hashtag Search Response (e.g., "#music")
```json
{
  "status": true,
  "message": "Tags search results fetched successfully",
  "data": {
    "posts": [
      {
        "_id": "post_id_here",
        "caption": "Amazing #music performance tonight! #live #concert",
        "title": "Live Music Event",
        "media": [
          {
            "mediaType": "image",
            "url": "https://example.com/image.jpg",
            "thumbUrl": "https://example.com/thumb.jpg"
          }
        ],
        "reactionCounts": {
          "like": 25,
          "love": 10,
          "support": 5
        },
        "totalComments": 15,
        "totalReactions": 40,
        "createdAt": "2024-01-15T10:30:00.000Z",
        "postLabel": "generalPost",
        "relatedHashtags": [
          {"hashtag": "#music", "count": 5},
          {"hashtag": "#live", "count": 3},
          {"hashtag": "#concert", "count": 2}
        ],
        "user": {
          "firstName": "Jane",
          "lastName": "Smith",
          "userName": "janesmith",
          "profileImage": "https://example.com/profile.jpg"
        }
      }
    ],
    "relatedHashtags": []
  },
  "pagination": {
    "totalResults": 30,
    "currentResults": 10,
    "totalPages": 3,
    "currentPage": 1
  }
}
```

## Search Capabilities

### Accounts Search
Searches across:
- First name, last name, username
- Business organization name
- Headline, position, industry
- Visual arts, performing arts, dance, acting, music, film/media, design, literary arts, crafts, applied arts
- Professions, skills, specializations

### Posts Search
Searches across:
- Post caption
- Post title

### Podcasts Search
Searches across:
- Podcast posts (posts with postLabel: 'podcast')
- Caption and title

### Jobs Search
Searches across:
- Job title
- Company name
- Job description
- Location

**Additional Information:**
- `isApplied`: Boolean indicating if the logged-in user has applied to this job
- `applicationCount`: Number of total applications for this job
- Job poster information (name, username, profile image, business name)

### Events Search
Searches across:
- Event title
- Organization name
- Event description

### Union Organizations Search
Searches across:
- Business organization name
- Username
- Headline
- Industry

### Tags Search
Searches for hashtags in posts with enhanced functionality:

#### Search Modes:
1. **General Search**: Search for posts containing hashtags (e.g., "music")
2. **Exact Hashtag Search**: Search for posts with specific hashtag (e.g., "#music")

#### Features:
- Post captions and titles containing hashtags
- Extracts and highlights hashtags from the content
- Related hashtags discovery with usage counts
- Exact hashtag filtering for precise results

#### Additional Information:
- `hashtags`: Array of hashtags found in the post
- `relatedHashtags`: Array of related hashtags with usage counts
- Post details (caption, title, media, reactions, comments)
- User information (name, username, profile image)

#### Usage Examples:
- Search "music" → Returns only related hashtags list (no posts)
- Search "#music" → Returns only posts containing exactly "#music" hashtag (no related hashtags)

## Example Usage

### Search for "actor" in accounts
```bash
curl -X GET "http://localhost:3000/user/search?q=actor" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "tab": "accounts",
    "page": 1,
    "perPage": 10
  }'
```

### Search for "music" in all categories (For You)
```bash
curl -X GET "http://localhost:3000/user/search?q=music" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "tab": "for_you"
  }'
```

### Search for "job" in jobs
```bash
curl -X GET "http://localhost:3000/user/search?q=job" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "tab": "jobs",
    "page": 1,
    "perPage": 5
  }'
```

### Search for "music" hashtags
```bash
curl -X GET "http://localhost:3000/user/search?q=music" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "tab": "tags",
    "page": 1,
    "perPage": 10
  }'
```

### Search for exact "#music" hashtag
```bash
curl -X GET "http://localhost:3000/user/search?q=%23music" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "tab": "tags",
    "page": 1,
    "perPage": 10
  }'
```

## Features

### Smart Search
- Case-insensitive search
- Multi-word search support
- Regex-based matching for better results

### Profile Visibility Integration
- **Privacy Compliance**: Respects user privacy preferences
- **Discovery Settings**: Filters results based on profile discovery options
  - "Everyone": Visible to all users
  - "My Connections": Only visible to connections
  - "People I Follow": Only visible to followers
  - "No One": Not discoverable in search
- **Profile Viewing Options**: Controls what information is shown
  - "Your name headline": Shows full profile information
  - "Private profile character": Shows limited profile information
- **Cross-Tab Filtering**: All search types (accounts, posts, jobs, events, tags) respect privacy settings

### "For You" Personalization
The "For You" tab provides personalized search results by prioritizing content from users you're connected to:

#### Priority System
1. **Connections First**: Content from users you're directly connected with
2. **Followers Second**: Content from users who follow you
3. **Platform Content**: Other relevant content from the platform
4. **Maximum Results**: Up to 8 items per category for comprehensive discovery

#### Benefits
- **Personalized Experience**: See content from your network first
- **Relevant Discovery**: Find content from people you know and trust
- **Network Building**: Discover connections and followers you might have missed
- **Quick Overview**: Get a comprehensive view across all categories

### Pagination
- Configurable page size
- Page-based navigation
- Total results and pages information

### Sorting
- **Accounts**: Sorted by followers count (descending), then by creation date
- **Posts**: Sorted by total reactions (descending), then by creation date
- **Podcasts**: Sorted by total reactions (descending), then by creation date
- **Jobs**: Sorted by creation date (newest first)
- **Events**: Sorted by start date (upcoming first), then by creation date
- **Union Organizations**: Sorted by followers count (descending), then by creation date

### Security
- Excludes current user from account search results
- Requires authentication
- Input validation and sanitization
- **Profile Visibility Filtering**: Respects user privacy settings
  - Users with "No One" discovery setting are excluded from search results
  - Users with "My Connections" setting only appear to their connections
  - Users with "Private profile character" show limited information
  - All search types apply visibility filtering based on user preferences

## Error Handling

### Common Error Responses

#### 401 Unauthorized
```json
{
  "status": false,
  "message": "Unauthorized"
}
```

#### 400 Bad Request
```json
{
  "status": false,
  "message": "Search string is required"
}
```

#### 500 Internal Server Error
```json
{
  "status": false,
  "message": "Internal server error"
}
```

## Implementation Details

### Search Algorithm
1. Splits search string into individual words
2. Creates case-insensitive regex patterns for each word
3. Searches across multiple fields using MongoDB's `$in` operator
4. Applies sorting and pagination
5. Returns formatted results with pagination metadata

### Performance Considerations
- Uses MongoDB indexes for efficient searching
- Implements pagination to limit result sets
- Uses projection to select only required fields
- Leverages MongoDB's aggregation pipeline for complex queries

### Data Models Used
- `User` model for accounts and union organizations
- `Post` model for posts and podcasts
- `JobPost` model for jobs
- `Event` model for events

## Frontend Integration

### React/JavaScript Example
```javascript
const searchGlobal = async (searchTerm, tab = 'for_you', page = 1, perPage = 10) => {
  try {
    const response = await fetch(`/user/search?q=${encodeURIComponent(searchTerm)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tab,
        page,
        perPage
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Search error:', error);
    throw error;
  }
};
```

## Future Enhancements
- Full-text search with MongoDB Atlas Search
- Search result highlighting
- Search suggestions and autocomplete
- Advanced filters (date range, location, etc.)
- Search analytics and trending searches
- Elasticsearch integration for better performance 