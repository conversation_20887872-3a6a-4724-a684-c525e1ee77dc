const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/pepli_node';

// Define schemas
const userSchema = new mongoose.Schema({
  businessOrganizationName: String,
  iAmMember: String,
  isFakeAccount: Boolean,
  signUpData: Array,
  userName: String,
  email: String,
  password: String
}, { timestamps: true });

const userSignupDataSchema = new mongoose.Schema({
  title: String,
  parentSlug: [String],
  itemText: String,
  slug: String,
  selectionType: String,
  subCategory: String
}, { timestamps: true });

const User = mongoose.model('User', userSchema);
const UserSignupData = mongoose.model('UserSignupData', userSignupDataSchema, 'usersignupdatas');

async function verifyMigration() {
  try {
    console.log('🔍 Verifying migration data...\n');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
    
    // Check database info
    const db = mongoose.connection.db;
    console.log(`Database: ${db.databaseName}`);
    
    // List collections
    const collections = await db.listCollections().toArray();
    console.log('\nCollections:');
    collections.forEach(col => console.log(`  - ${col.name}`));
    
    // Check organizations
    console.log('\n📊 === ORGANIZATIONS ===');
    const orgCount = await User.countDocuments({
      iAmMember: 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility'
    });
    console.log(`Total organizations: ${orgCount}`);
    
    if (orgCount > 0) {
      const orgs = await User.find({
        iAmMember: 'unionAffiliateOrganizationBusinessSchoolsTrainingFacility'
      }).limit(3);
      
      console.log('\nSample organizations:');
      orgs.forEach((org, index) => {
        console.log(`  ${index + 1}. ${org.businessOrganizationName}`);
        console.log(`     ID: ${org._id}`);
        console.log(`     signUpData: ${JSON.stringify(org.signUpData)}`);
        console.log(`     isFakeAccount: ${org.isFakeAccount}`);
        console.log('');
      });
    }
    
    // Check main categories
    console.log('📊 === MAIN CATEGORIES ===');
    const mainCatCount = await UserSignupData.countDocuments({
      title: 'who_are_you',
      subCategory: 'main_category'
    });
    console.log(`Total main categories: ${mainCatCount}`);
    
    if (mainCatCount > 0) {
      const mainCats = await UserSignupData.find({
        title: 'who_are_you',
        subCategory: 'main_category'
      }).limit(3);
      
      console.log('\nSample main categories:');
      mainCats.forEach((cat, index) => {
        console.log(`  ${index + 1}. ${cat.itemText}`);
        console.log(`     ID: ${cat._id}`);
        console.log(`     slug: ${cat.slug}`);
        console.log('');
      });
    }
    
    // Check subcategories
    console.log('📊 === SUBCATEGORIES ===');
    const subCatCount = await UserSignupData.countDocuments({
      title: { $regex: ".*subcategory" }
    });
    console.log(`Total subcategories: ${subCatCount}`);
    
    if (subCatCount > 0) {
      const subCats = await UserSignupData.find({
        title: { $regex: ".*subcategory" }
      }).limit(3);
      
      console.log('\nSample subcategories:');
      subCats.forEach((sub, index) => {
        console.log(`  ${index + 1}. ${sub.itemText}`);
        console.log(`     ID: ${sub._id}`);
        console.log(`     parent: ${sub.parentSlug}`);
        console.log('');
      });
    }
    
    // Check all users
    console.log('📊 === ALL USERS ===');
    const allUserCount = await User.countDocuments();
    console.log(`Total users: ${allUserCount}`);
    
    // Check all signup data
    console.log('📊 === ALL SIGNUP DATA ===');
    const allSignupDataCount = await UserSignupData.countDocuments();
    console.log(`Total signup data entries: ${allSignupDataCount}`);
    
    console.log('\n🎉 Verification completed!');
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run verification
verifyMigration();
