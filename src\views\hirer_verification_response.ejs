<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Hirer Verification Response</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        background-color: #f3f4f8;
        font-family: Helvetica, Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        text-align: center;
      }
      .container {
        background-color: #fff;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        max-width: 500px;
        width: 100%;
      }
      .logo {
        margin-bottom: 20px;
      }
      .logo img {
        width: 80px;
        height: auto;
      }
      .icon {
        margin-bottom: 20px;
      }
      .icon svg {
        width: 50px;
        height: 50px;
      }
      .message {
        font-size: 20px;
        color: #000000;
        opacity: 0.8;
        margin-bottom: 30px;
      }
      .button {
        display: inline-block;
        background-color: #30d5c8;
        color: #ffffff;
        font-size: 16px;
        font-weight: 400;
        padding: 10px 20px;
        border-radius: 4px;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">
        <img src="https://pepli-beta.s3.us-east-1.amazonaws.com/Pepli/logo_tm.png" alt="Pepli Logo" />
      </div>

      <!-- Conditionally display tick or cross icon based on the message -->
      <div class="icon">
        <% if (message.toLowerCase().includes('approved')) { %>
          <!-- Green Tick Icon for Success -->
          <svg viewBox="0 0 24 24" fill="none" stroke="#00cc00" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10" fill="none" stroke="#00cc00" />
            <path d="M7 13l3 3 7-7" />
          </svg>
        <% } else { %>
          <!-- Red Cross Icon for Rejection or Failure -->
          <svg viewBox="0 0 24 24" fill="none" stroke="#ff4d4f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10" fill="none" stroke="#ff4d4f" />
            <path d="M7 7l10 10M7 17L17 7" />
          </svg>
        <% } %>
      </div>

      <div class="message">
        <%= message %>
      </div>
      <a href="https://pepliapp.com" class="button">Return to Pepli</a>
    </div>
  </body>
</html>