import { IsEnum, IsMongoId, IsOptional, IsString } from 'class-validator';
import { IsStringValidation } from '../../../Custom/helpers/dto.helper';
import CONSTANT from '../../../common/constant/common.constant';
import { SuggestUsersEnum } from 'src/common/constant/enum';

export class GetSuggestedUsersDto {
  @IsMongoId({ message: CONSTANT.INVALID('groupId') })
  @IsStringValidation('groupId', 24, true)
  readonly groupId: string;

  @IsStringValidation('type', 50, false)
  @IsEnum(SuggestUsersEnum, { each: true, message: CONSTANT.INVALID('type') })
  readonly type: string;

  @IsString()
  @IsOptional()
  readonly search?: string;

  @IsStringValidation('page', 3, true)
  readonly page?: string;

  @IsStringValidation('per page', 3, true)
  readonly perPage?: string;

  @IsStringValidation('sort', 2, true)
  readonly sort?: string;

  @IsStringValidation('sort by', 20, true)
  readonly sortBy?: string;
}
