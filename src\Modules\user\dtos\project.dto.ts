import { Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class MediaDto {
  @IsString()
  mediaType: string;

  @IsString()
  url: string;

  @IsString()
  thumbUrl: string | null;
}
export class projectDto {
  @IsOptional()
  @IsString()
  _id: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsArray({ message: 'selfIdentifiableQualities must be an array of strings' })
  skills: string[];

  @IsOptional()
  @IsArray({ message: 'affiliateOrganizations must be an array of strings' })
  affiliateOrganizations: string[];

  @IsOptional()
  link: string;

  @IsOptional()
  @IsBoolean()
  currentlyWorking: boolean;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  startDate: string;

  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  endDate: string;

  @IsOptional()
  @IsNotEmpty()
  media: MediaDto[];

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true }) // Ensure that each element in the array is a valid MongoDB ObjectId
  collaborators: string[];
}
