# Organization Categorization System - Hybrid Approach

## Overview

This document explains the hybrid approach implemented for organization categorization in the Pepli Node application. The system ensures that all organizations are properly categorized under main categories and subcategories while maintaining backward compatibility.

## Architecture

### Two-Tier System

1. **Predefined Organizations** (Static)
   - Stored in `userSignupData` collection
   - Follow hierarchical structure: Main Category → Subcategory → Organization
   - Examples: SAG-AFTRA, CAA, Juilliard School

2. **Dynamic Organizations** (Auto-generated)
   - Created automatically during signup
   - Also stored in `userSignupData` collection
   - Categorized based on user's `iAmMember` type and selected signup data

## Hierarchy Structure

### Main Categories (Level 1)
- **Union/Affiliate Organization/Business/School/Training Facility**
  - Maps to `iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY`

### Subcategories (Level 2)
Under "Union/Affiliate Organization/Business/School/Training Facility":
- **Union**: SAG, AFTRA, DGA, WGA, IATSE, AGN
- **Affiliate Organization**: Professional Association, Industry Group, Trade Organization, Non-Profit Arts
- **Affiliate Business**: Production Company, Talent Agency, Casting Agency, Entertainment Business, Media Company
- **School/Training Facility**: Acting School, Film School, Music School, Dance School

### Organizations (Level 3)
Specific organizations under each subcategory.

## Implementation Details

### 1. Automatic Organization Creation

When a new organization signs up:

```typescript
// During signup process
if (signupData.iAmMember === iAmMemberEnum.UNION_AFFILIATE_ORGANIZATION_BUSINESS_SCHOOLSTRAININGINFACILITY) {
  if (signupData.businessOrganizationName) {
    // Only subcategory_slug is needed - main category is auto-detected
    await this.createOrganizationSignupDataEntry(signupData);
  }
}
```

The system uses existing APIs to determine categories:
- **Main Categories**: Fetched from `/api/v1/user/organization-categories`
- **Subcategories**: Fetched from `/api/v1/user/organization-subcategories/:mainCategorySlug`

### 2. Categorization Logic

The system determines the appropriate subcategory based on:

1. **User's selected signup data** (if they selected specific subcategories)
2. **iAmMember type** (determines main category from existing API)
3. **Available subcategories from existing API** (if no specific subcategory selected)
4. **Generic subcategory** (if no subcategories available in API)

### 3. Database Schema

#### userSignupData Collection
```javascript
{
  title: String,           // e.g., "sag_organization"
  parentSlug: [String],    // e.g., ["sag_union"]
  itemText: String,        // e.g., "SAG-AFTRA"
  slug: String,           // e.g., "sag_aftra_main"
  selectionType: String,   // e.g., "multiple"
  subCategory: String     // e.g., "organization", "sub_type", "main_category"
}
```

## API Endpoints

The system uses existing APIs for organization categorization:

### Existing APIs Used
- `GET /user/organization-categories` - Get main categories
- `GET /user/organization-subcategories/:mainCategorySlug` - Get subcategories

### Core Functionality
The system automatically creates organization entries in `userSignupData` during signup, but does not expose additional query endpoints. Organizations are categorized using the existing API structure.

## Migration Process

### Manual Migration Script

Use the provided migration script to categorize existing organizations:

```bash
node migrate_organizations.js
```

The script will:
1. Find all organization users in the `users` collection
2. Check if they already exist in `userSignupData`
3. Create appropriate entries with proper categorization
4. Handle cases where no subcategory was selected

### Migration Logic

1. **Existing Organizations**: Skip if already in `userSignupData`
2. **Main Category Detection**: Use existing API to get main categories
3. **Subcategory Detection**: Use user's `signUpData` to determine subcategory
4. **API Fallback**: Use available subcategories from existing API if none selected
5. **Generic Subcategory**: Create "Other Organizations" subcategory if no API subcategories available
6. **Slug Generation**: Create unique slugs for new organizations

## Benefits of Hybrid Approach

### ✅ Advantages
1. **Backward Compatibility**: Existing organizations continue to work
2. **Automatic Categorization**: New organizations are automatically categorized
3. **Consistent Structure**: All organizations follow the same hierarchical pattern
4. **Scalable**: Easy to add new main categories, subcategories, or organizations
5. **Queryable**: Can easily find organizations by category or hierarchy

### 🔄 How It Works
1. **During Signup**: New organizations are automatically added to `userSignupData`
2. **Categorization**: Based on user's selections and `iAmMember` type
3. **Fallback**: Generic subcategory if no specific category selected
4. **Linking**: Organizations are linked to users via `signUpData` references

## Usage Examples

### Backend Queries

```javascript
// Find all organizations under a subcategory
const orgs = await userSignupDataModel.find({
  parentSlug: ['sag_union'],
  subCategory: 'organization'
});

// Find organization hierarchy
const org = await userSignupDataModel.findOne({
  itemText: 'SAG-AFTRA',
  subCategory: 'organization'
});
```


### Frontend Integration

The system automatically categorizes organizations during signup. To display organization information, you can query the `userSignupData` collection directly or use your existing organization-related queries.

## Maintenance

### Adding New Organizations
1. Add to `signupData.json` for predefined organizations
2. New organizations are automatically created during signup

### Adding New Categories
1. Add main category to `signupData.json`
2. Add subcategories under the main category
3. Update the `getMainCategoryFromIAmMember` method if needed

### Data Integrity
- All organizations have proper parent-child relationships
- Slugs are unique and URL-friendly
- Hierarchical queries work efficiently

## Conclusion

The hybrid approach successfully unifies organization management while maintaining flexibility and scalability. All organizations, whether predefined or dynamically created, follow the same hierarchical structure and can be queried consistently. 