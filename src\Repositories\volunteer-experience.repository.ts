import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AbstractRepository } from './abstract.repository';
import { volunteerExperienceDocument } from 'src/Models/volunteer-experience.schema';

@Injectable()
export class VolunteerExperienceRepository extends AbstractRepository<volunteerExperienceDocument> {
  constructor(
    @InjectModel('VolunteerExperience')
    experienceModel: Model<volunteerExperienceDocument>,
  ) {
    super(experienceModel);
  }

  async findExperiences(
    filter: any,
    sortObj: any,
    skipData: number,
    limitData: number,
  ) {
    return await this.model
      .find(filter)
      .select('-__v -userId')
      .populate({
        path: 'organization',
        match: { isFakeAccount: false },
        select:
          'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
      })
      .sort(sortObj)
      .skip(skipData)
      .limit(limitData)
      .exec();
  }
}
