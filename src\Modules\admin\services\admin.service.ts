import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { successResponse } from 'src/Custom/helpers/responseHandler';
import { recordDocument } from 'src/Models/record.schema';
import CONSTANT from '../../../common/constant/common.constant';
import { storeData } from '../dtos/record.dtos';
import { recordJobDocument } from 'src/Models/recordJob.schema';
import { userSignupDataDocument } from 'src/Models/userSignupData.schema';
import * as fs from 'fs';
import * as path from 'path';
import { MissingInformationDocument } from 'src/Models/missinginformation.schema';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel('Record')
    private readonly recordModel: Model<recordDocument>,
    @InjectModel('RecordJob')
    private readonly recordJobModel: Model<recordJobDocument>,
    @InjectModel('UserSignupData')
    private readonly userSignupDataModel: Model<userSignupDataDocument>,

    @InjectModel('MissingInformation')
    private readonly missingInformationModel: Model<MissingInformationDocument>,
  ) {}

  public async updateRecords(recordData) {
    try {
      let record: any;

      if (!recordData.isJobRecords) {
        record = await this.recordModel.findOne();

        if (!record) {
          // If the record doesn't exist, create new
          record = new this.recordModel();
        }

        // Set the specified field based on the type
        switch (recordData.type) {
          case storeData.AMERICAN_UNIONS:
            record.americanUnions = recordData.data;
            break;
          case storeData.GLOBAL_UNIONS:
            record.globalUnions = recordData.data;
            break;
          case storeData.AMERICAN_AFFILLIATE_ORGANIZATION:
            record.americanAffiliateOrganization = recordData.data;
            break;
          case storeData.GLOBAL_AFFILIATE_ORGANIZATION:
            record.globalAffiliateOrganization = recordData.data;
            break;
          case storeData.NON_PROFIT_PHILANTHROPIC_INSTITUTION:
            record.nonProfitPhilanthropicInstitution = recordData.data;
            break;
          case storeData.AMERICAN_CATERING_CRAFTIES:
            record.americanCateringCrafties = recordData.data;
            break;
          case storeData.ART_GALLERIES:
            record.artGalleries = recordData.data;
            break;
          case storeData.CATERING_CRAFT_SERVICE_SGLOBALLY:
            record.cateringCraftServicesGlobally = recordData.data;
            break;
          case storeData.MUSEUMS:
            record.museums = recordData.data;
            break;
          case storeData.TALENT_REPS_AGENCIES_MANAGEMENT_COMPANIES:
            record.talentRepsAgenciesManagementCompanies = recordData.data;
            break;
          case storeData.GLOBAL_TALENT_REPS_AGENCIES_MANAGEMENT_COMPANIES:
            record.globalTalentRepsAgenciesManagementCompanies =
              recordData.data;
            break;
          case storeData.TALENT_REPS_AGENCIES_MANAGEMENT_COMPANIES_FOR_SPEAKERS:
            record.talentRepsAgenciesManagementCompaniesForSpeakers =
              recordData.data;
            break;
          case storeData.ENTERTAINMENT_LAW_FIRMS:
            record.entertainmentLawFirms = recordData.data;
            break;
          case storeData.GLOBAL_COMEDY_CLUBS:
            record.globalComedyClubs = recordData.data;
            break;
          case storeData.AUDIO_PRODUCTION_RECORDING_STUDIOS:
            record.audioProductionRecordingStudios = recordData.data;
            break;
          case storeData.AMERICAN_COMEDY_CLUBS:
            record.americanComedyClubs = recordData.data;
            break;
          case storeData.GLOBAL_THEATERS:
            record.globalTheaters = recordData.data;
            break;
          case storeData.AMERICAN_THEATERS:
            record.americanTheaters = recordData.data;
            break;
          case storeData.CAUSES:
            record.causes = recordData.data;
            break;
          case storeData.CASTING:
            record.casting = recordData.data;
            break;
          case storeData.GLOBAL_RECORDING_STUDIOS:
            record.globalRecordingStudios = recordData.data;
            break;
          case storeData.LIFE_COACH_AGENCIES:
            record.lifeCoachAgencies = recordData.data;
            break;
          case storeData.ANIMAL_ACTORS_AGENCIES_COMPANIES:
            record.animalActorsAgenciesCompanies = recordData.data;
            break;
          case storeData.PUBLICATIONS:
            record.publications = recordData.data;
            break;
          case storeData.INVESTMENT_ENTITIES:
            record.investmentEntities = recordData.data;
            break;
          case storeData.PRODUCTION_ACQUISITION_DISTRIBUTION_COMPANIES:
            record.productionAcquisitionDistributionCompanies = recordData.data;
            break;
          case storeData.SCHOOL_TRAINING_IN_ARTS_ENTERTAINMENT:
            record.schoolsTrainingInArtsEntertainment = recordData.data;
            break;
          case storeData.VISUAL_ART:
            record.visualArt = recordData.data;
            break;
          case storeData.PERFORMANING_ART:
            record.performingArt = recordData.data;
            break;
          case storeData.DANCE:
            record.dance = recordData.data;
            break;
          case storeData.ACTING:
            record.acting = recordData.data;
            break;
          case storeData.MUSIC:
            record.music = recordData.data;
            break;
          case storeData.FILM_MEDIA:
            record.filmMedia = recordData.data;
            break;
          case storeData.DESIGN:
            record.design = recordData.data;
            break;
          case storeData.LITERARY_ART:
            record.literaryArt = recordData.data;
            break;
          case storeData.CRAFTS:
            record.crafts = recordData.data;
            break;
          case storeData.APPLIED_ART:
            record.appliedArt = recordData.data;
            break;
          case storeData.OTHER:
            record.other = recordData.data;
            break;
          case storeData.SELFIOTHER:
            record.selfIother = recordData.data;
            break;
          case storeData.FEEDBACK_CATEGORIES:
            record.feedbackCategories = recordData.data;
            break;
          case storeData.INDUSTRY:
            record.industry = recordData.data;
            break;
          case storeData.SEXUAL_ORIENTATION:
            record.sexualOrientation = recordData.data;
            break;
          case storeData.ORGANIZATION_TYPE:
            record.organizationType = recordData.data;
            break;
          case storeData.SPECIALIZATION:
            record.specialization = recordData.data;
            break;
          case storeData.COMPANY_SIZE:
            record.companySize = recordData.data;
            break;
          case storeData.PRONOUNS:
            record.pronouns = recordData.data;
            break;
          case storeData.FANDEGREE:
            record.fanDegree = recordData.data;
            break;
          // case storeData.PRODUCTIONCOMPANIES:
          //   record.productionCompanies = recordData.data;
          //   break;

          // case storeData.PROFESSION:
          //   record.profession = recordData.data;
          //   break;

          // case storeData.PROJECTTYPEGENRE:
          //   record.projectTypeGenre = recordData.data;
          //   break;
          // case storeData.DEGREE:
          //   record.degree = recordData.data;
          //   break;
          // case storeData.PASSPORTCOUNTRIES:
          //   record.passportCountries = recordData.data;
          //   break;
          // case storeData.SCHOOLSTRAININGINENTERTAINMENT:
          //   record.schoolsTrainingInEntertainment = recordData.data;
          //   break;

          // case storeData.INTERESTS:
          //   record.interests = recordData.data;
          //   break;
          case storeData.ETHNICITY:
            record.ethnicity = recordData.data;
            break;
          case storeData.NATIONALITY:
            record.nationality = recordData.data;
            break;
          case storeData.DISABILITY:
            record.disability = recordData.data;
            break;
          case storeData.GENDER:
            record.gender = recordData.data;
            break;
          case storeData.LANGUAGESSPOKENSIGNED:
            record.languagesSpokenSigned = recordData.data;
            break;
          case storeData.PROFICIENCYLEVEL:
            record.proficiencyLevel = recordData.data;
            break;
          // case storeData.OTHER:
          //   record.other = recordData.data;
          //   break;
          case storeData.AGE:
            record.age = recordData.data;
            break;
          case storeData.SELFIGENDER:
            record.selfIgender = recordData.data;
            break;
          case storeData.SKILLS:
            record.skills = recordData.data;
            break;
          case storeData.PROFESSIONS:
            record.professions = recordData.data;
            break;
          case storeData.POSITIONS:
            record.positions = recordData.data;
            break;
          case storeData.FIELD_OF_STUDY:
            record.fieldOfStudy = recordData.data;
            break;
          case storeData.DEGREE:
            record.degree = recordData.data;
            break;
          // case storeData.CURRENCY:
          //   record.currency = recordData.data;
          //   break;
          default:
            console.log('Invalid type');
            break;
        }
        // console.log(record, 'new');
        record = await record.save();
      }

      //isJobRecords:true insert data for JOB
      /* if (recordData.isJobRecords) {
        record = await this.recordJobModel.findOne();

        if (!record) {
          // If the record doesn't exist, create new
          record = new this.recordJobModel();
        }

        // Set the specified field based on the type
        switch (recordData.type) {
          case storeData.ROLETYPE:
            record.roleType = recordData.data;
            break;
          case storeData.PROJECTTYPE:
            record.projectType = recordData.data;
            break;
          case storeData.COMMERCIAL_BRANDED_CONTENT:
            record.commercialBrandedContent = recordData.data;
            break;
          case storeData.CONCERT:
            record.concert = recordData.data;
            break;
          case storeData.FASHION:
            record.fashion = recordData.data;
            break;
          case storeData.LITERATURE:
            record.literature = recordData.data;
            break;
          case storeData.MOVIE_FILM:
            record.movieFilm = recordData.data;
            break;
          case storeData.MUSEUM:
            record.museum = recordData.data;
            break;
          case storeData.MUSIC:
            record.music = recordData.data;
            break;
          case storeData.OPERA:
            record.opera = recordData.data;
            break;
          case storeData.HIPHOP:
            record.hipHop = recordData.data;
            break;
          case storeData.RAP:
            record.rap = recordData.data;
            break;
          case storeData.COUNTRY_MUSIC:
            record.countryMusic = recordData.data;
            break;
          case storeData.OTHER_DIGITAL_MEDIA:
            record.otherDigitalMedia = recordData.data;
            break;
          case storeData.RELIGIOUS_SPIRITUAL_MUSIC:
            record.religiousSpiritualMusic = recordData.data;
            break;
          case storeData.STAND_UP_COMEDY:
            record.standUpComedy = recordData.data;
            break;
          case storeData.TV_SHOW_SERIES:
            record.tVShowSeries = recordData.data;
            break;
          case storeData.THEATER:
            record.theater = recordData.data;
            break;
          case storeData.USER_GENERATED_CONTENT_UGC:
            record.userGeneratedContentUGC = recordData.data;
            break;
          case storeData.TYPE_OF_ORGANIZATION_YOU_WORKING_WITH_PROJECT:
            record.typeOfOrganizationYouAreWorkingWithForThisProject =
              recordData.data;
            break;
          case storeData.CRAFTS:
            record.crafts = recordData.data;
            break;
          case storeData.ADDITIONAL:
            record.additional = recordData.data;
            break;
          default:
            console.log('Invalid type');
            break;
        }

        record = await record.save();
      } */

      return successResponse(
        record[recordData.type],
        CONSTANT.UPDATED_SUCCESSFULLY(recordData.type),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async addsignupdata() {
    try {
      // Read the signupData.json file
      const filePath = path.join(
        `${process.cwd()}/src/common/json/`,
        'signupData.json',
      );

      const fileContents = fs.readFileSync(filePath, 'utf-8');

      // Parse the JSON data
      const signupData = JSON.parse(fileContents);

      const bulkOps = signupData.map((item) => ({
        updateOne: {
          filter: { slug: item.slug },
          update: { $set: item },
          upsert: true,
        },
      }));

      await this.userSignupDataModel.bulkWrite(bulkOps);

      return successResponse(
        null,
        CONSTANT.ADDED_SUCCESSFULLY('signupData'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async adddata(req) {
    const dupArr = req;
    function remove(dupArr) {
      return dupArr.filter((i, index) => dupArr.indexOf(i) === index);
    }
    // console.log(remove(dupArr));
    const dR = remove(dupArr);
    function removeNumbersFromArray(array) {
      // Use map to create a new array without numbers
      const newArray = array.map((item) => item.replace(/\d+\.\s/g, ''));
      return newArray;
    }
    const newArray = removeNumbersFromArray(dR);

    function sortArrayAlphabetically(array) {
      // Use localeCompare to sort alphabetically
      const sortedArray = array.sort((a, b) =>
        a.localeCompare(b, undefined, { numeric: true, sensitivity: 'base' }),
      );
      return sortedArray;
    }

    const sortedArray = sortArrayAlphabetically(newArray);
    const ws = sortedArray.map((entry) => entry.trim());

    return successResponse(ws, 'Alphabetic / sorted data', HttpStatus.OK);
  }
  public async addMissingInformation(req, data: any) {
    const { user: loggedInUser } = req;

    const userId = loggedInUser._id;
    try {
      const exists = await this.missingInformationModel.findOne({
        message: data.message,
        userId: userId,
        sections: { $all: data.sections, $size: data.sections.length },
      });

      if (!exists) {
        const missingInfo = new this.missingInformationModel({
          message: data.message,
          sections: data.sections,
          userId: userId,
        });

        await missingInfo.save();
      }

      return successResponse(
        null,
        CONSTANT.ADDED_SUCCESSFULLY('MissingInformation'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }

  public async getAllMissingInformation() {
    try {
      const missingInfos = await this.missingInformationModel
        .find()
        .populate({
          path: 'userId',
          select:
            'firstName lastName businessOrganizationName userName profileImage followers following connections accountVerified iAmMember professions isFakeAccount hirerEmployerVerifiedStatus isMembershipVerified',
        })
        .lean();
      return successResponse(
        missingInfos,
        CONSTANT.FETCHED_SUCCESSFULLY('MissingInformation'),
        HttpStatus.OK,
      );
    } catch (error) {
      throw new HttpException({ message: error.message }, error?.status || 500);
    }
  }
}
